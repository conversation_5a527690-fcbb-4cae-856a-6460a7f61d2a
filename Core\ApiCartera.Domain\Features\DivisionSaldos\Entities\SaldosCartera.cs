﻿using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Domain.Features.DivisionSaldos.Entities;

public class SaldosCartera : Entity
{
    public double Saldo1 { get; set; }
    public double Saldo2 { get; set; }
    public double Saldo3 { get; set; }
    public double Saldo4 { get; set; }
    public int IdSubproducto { get; set; }
    public int? IdSaldosSolicitudCartera { get; set; } = null;
    public int? IdSaldosCarteraAnt { get; set; } = null;
    public int? IdMovimiento { get; set; }
}
