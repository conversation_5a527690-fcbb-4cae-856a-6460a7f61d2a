﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;

namespace ApiCartera.Application.Services.BeneficioAcuerdo001;

public class SegmentacionBeneficioAcuerdo001Service : ISegmentacionBeneficioAcuerdo001Service
{
    private readonly ISolicitudCarteraActivaRepository _solicitudCarteraActivaRepository;

    public SegmentacionBeneficioAcuerdo001Service(ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository)
    {
        _solicitudCarteraActivaRepository = solicitudCarteraActivaRepository;
    }

    public async Task<List<int>> ObtenerSegmentacionBeneficioAcuerdo001()
    {
        var segmentacion = await _solicitudCarteraActivaRepository.ObtenerSegmentacionBeneficioAcuerdo001();

        return segmentacion;
    }
}
