using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.Afectaciones.Constants;
using ApiCartera.Domain.Features.Afectaciones.Services;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class CalcularSaldosCarteraAfectacionFactory
    {
        private readonly Dictionary<string, ICalcularSaldosCarteraAfectacionService> _calculadoresSaldosCartera;

        public CalcularSaldosCarteraAfectacionFactory(
            ISaldosCarteraRepository saldosCarteraRepository,
            ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository)
        {
            _calculadoresSaldosCartera = new Dictionary<string, ICalcularSaldosCarteraAfectacionService>
            {
                
                { TiposNovedadAfectacion.AFECTACION_A_CAPITAL_DISMINUCION, new AfectacionesAfetacionACapitalService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.AFECTACION_A_CAPITAL_AUMENTO, new AfectacionesAfetacionACapitalAumentoService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.PASO_AL_COBRO, new AfectacionesAfectacionPorPasoAlCobroService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.RECAUDO_ASOBANCARIA, new AfectacionesSaldoACapitalRecaudoService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.RECAUDO_MANUAL, new AfectacionesSaldoACapitalRecaudoService(saldosCarteraRepository, solicitudCarteraActivaRepository) },                
                { TiposNovedadAfectacion.CONDONACION_MUERTE_O_INVALIDEZ, new AfectacionesSaldoACapitalCondonacionMuerteInvalidezService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.CONDONACION_GRADUACION, new AfectacionesSaldoACapitalCondonacionGraduacionSaberProService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.CONDONACION_SABERPRO, new AfectacionesSaldoACapitalCondonacionGraduacionSaberProService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.CONDONACION_ALIANZA, new AfectacionesSaldoACapitalCondonacionAlianzaService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.REVERSION_PASO_AL_COBRO, new AfectacionesReversionPorPasoAlCobroService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.NOVACION_VIEJO, new AfectacionesSaldoACapitalPorNovacionService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.DESEMBOLSO_DE_PRESTAMO, new AfectacionesSaldoACapitalPorDesembolsoService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
            };
        }

        public ICalcularSaldosCarteraAfectacionService ObtenerCalculadoraSaldos(string codigoNovedad)
        {
            if (_calculadoresSaldosCartera.TryGetValue(codigoNovedad, out var calcularSaldosCartera))
            {
                return calcularSaldosCartera;
            }

            var validaciones = new List<ValidationFailure>()
            {
                new()
                {
                    PropertyName = nameof(codigoNovedad),
                    ErrorMessage = $"No se encontró un código de novedad asociado a este valor: {codigoNovedad}"
                }
            };

            throw new ValidationException(validaciones);
        }
    }
}
