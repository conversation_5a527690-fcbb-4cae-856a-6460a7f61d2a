﻿using Oracle.ManagedDataAccess.Client;

namespace ApiCartera.Infrastructure.Shared;

public static class OracleDataReaderExtensions
{
    public static bool HasColumn(this OracleDataReader reader, string columnName)
    {
        for (int i = 0; i < reader.FieldCount; i++)
        {
            if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                return true;
        }
        return false;
    }
}

