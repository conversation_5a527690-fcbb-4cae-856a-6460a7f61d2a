﻿using ApiCartera.Application.Interfaces;
using Oracle.ManagedDataAccess.Client;
using System.Data;

namespace ApiCartera.Infrastructure.Data.AdoDbContext;

public class OracleDataContext(OracleConnection connection) : ITransactionScope
{
    private readonly OracleConnection _connection = connection;
    private OracleTransaction? _transaction;
    private bool _isTransactionActive = false;
    private bool _disposed = false;

    public OracleCommand CreateCommand(string sqlQuery)
    {
        var command = _connection.CreateCommand();
        command.CommandText = sqlQuery;
        command.Transaction = _transaction; 
        return command;
    }
    public bool GetConnection()
    {
        return _connection.State == System.Data.ConnectionState.Open;
    }
    
    public OracleTransaction? GetCurrentTransaction()
    {
        return _transaction;
    }

    public void BeginTransaction()
    {
        if (_connection.State != System.Data.ConnectionState.Open)
            _connection.Open();

        _transaction = _connection.BeginTransaction();
        _isTransactionActive = true;
        Console.WriteLine("Transacción iniciada");
    }

    public async Task BeginTransactionAsync()
    {
        if (_connection.State != ConnectionState.Open)
            await _connection.OpenAsync();

        _transaction = (OracleTransaction?)await _connection.BeginTransactionAsync();
    }

    public void Commit()
    {
        if (_isTransactionActive && _transaction != null)
        {
            _transaction.Commit();
            _transaction = null;
            _isTransactionActive = false;
            Console.WriteLine("Transacción confirmada");
        }
        else
        {
            Console.WriteLine("ADVERTENCIA: Se intentó confirmar una transacción cuando no hay ninguna activa");
        }
    }

    public async Task CommitAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            _transaction = null;
        }
    }

    public void Rollback()
    {
        if (_isTransactionActive && _transaction != null)
        {
            _transaction.Rollback();
            _transaction = null;
            _isTransactionActive = false;
            Console.WriteLine("Transacción revertida");
        }
        else
        {
            Console.WriteLine("ADVERTENCIA: Se intentó revertir una transacción cuando no hay ninguna activa");
        }
    }

    public async Task RollbackAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _transaction?.Dispose();
                _connection?.Dispose();
            }
            _disposed = true;
        }
    }
}