﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalReversionRecaudoService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraService
    {
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoDTO movimiento)
        {
            ValidarMovimiento(movimiento);

            var saldosCarteraDTO = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            ValidarSaldosCartera(saldosCarteraDTO, movimiento.IdSolicitud);
            var nuevosSaldosCartera = (SaldosCarteraDTO)saldosCarteraDTO.Clone();

            var saldosCarteraRecaudo = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto, movimiento.FechaMovimiento);
            ValidarSaldosCartera(saldosCarteraRecaudo, movimiento.IdSolicitud);

            var saldosCarteraAntesDeRecaudo = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorId(saldosCarteraRecaudo.IdSaldosCarteraAnt.Value);
            ValidarSaldosCartera(saldosCarteraAntesDeRecaudo, movimiento.IdSolicitud);

            var saldos = new List<SaldosRecaudoDTO>
            {
                new() { Saldo = nuevosSaldosCartera.Saldo1, SaldoAnt = saldosCarteraAntesDeRecaudo.Saldo1 },
                new() { Saldo = nuevosSaldosCartera.Saldo2, SaldoAnt = saldosCarteraAntesDeRecaudo.Saldo2 },
                new() { Saldo = nuevosSaldosCartera.Saldo3, SaldoAnt = saldosCarteraAntesDeRecaudo.Saldo3 },
                new() { Saldo = nuevosSaldosCartera.Saldo4, SaldoAnt = saldosCarteraAntesDeRecaudo.Saldo4 },
            };

            for (int i = 0; i < saldos.Count; i++)
            {
                var saldo = saldos[i].Saldo;
                var saldoAnterior = saldos[i].SaldoAnt;
                if (movimiento.ValorGiro == 0) break;

                if (movimiento.ValorGiro >= saldoAnterior)
                {
                    movimiento.ValorGiro -= saldoAnterior;
                    saldo += saldoAnterior;
                }
                else
                {
                    saldo += movimiento.ValorGiro;
                    movimiento.ValorGiro = 0;
                }
                saldos[i].Saldo = saldo;
                saldos[i].SaldoAnt = saldoAnterior;
            }

            nuevosSaldosCartera.Saldo1 = saldos[0].Saldo;
            nuevosSaldosCartera.Saldo2 = saldos[1].Saldo;
            nuevosSaldosCartera.Saldo3 = saldos[2].Saldo;
            nuevosSaldosCartera.Saldo4 = saldos[3].Saldo;
            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimiento;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCarteraDTO.Id;

            var saldosCartera = this.MapearSaldosCartera(nuevosSaldosCartera);
            solicitudCarteraActivaRepository.GuardarSaldos(saldosCartera);

            return nuevosSaldosCartera;
        }
    }
}
