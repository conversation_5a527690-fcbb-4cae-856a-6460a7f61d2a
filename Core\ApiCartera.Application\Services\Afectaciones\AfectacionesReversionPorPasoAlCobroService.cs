using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using FluentValidation.Results;
using System.Globalization;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesReversionPorPasoAlCobroService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {

        private readonly IAfectacionesSaldosACapitalService _afectacionesSaldosACapital = afectacionesSaldosACapital;

        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            ValidarMovimientoAfectacion(movimiento);
            ValidarFechaEfectivaMovimiento(movimiento.FechaEfectivaMovimiento);
            var verificaMovimientoAnteriorConsolidado = 0;
            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var buscarPasoAlCobro = 0;

            var fechaMovimiento = ParsearFechaEfectivaMovimiento(movimiento.FechaEfectivaMovimiento);
            
            fechaMovimiento = fechaMovimiento.Date;

            List<MovimientosDiariosDTO>? movimientosDiarios = await _afectacionesSaldosACapital.ObtenerListadoMovimientosPorIdIcetex(movimiento.IdIcetex);

            buscarPasoAlCobro = movimientosDiarios
                .Where(movimientoDiario => movimientoDiario.CodNovedad == "012" && 
                                    movimientoDiario.CodSubproductoXObligacion != "10006")
                .OrderBy(movimientoDiario => movimientoDiario.IdMovimientoDiario)
                .Select(movimientoDiario => movimientoDiario.IdMovimientoDiario)
                .FirstOrDefault();


            var saldoConsolidado = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, 6);
            verificaMovimientoAnteriorConsolidado = ValidarSaldosCarteraConsolidado(saldoConsolidado);
            
            if (verificaMovimientoAnteriorConsolidado != 0)
            {
                var nuevosSaldosCartera = (SaldosCarteraDTO)saldoConsolidado.Clone();
                return nuevosSaldosCartera;
            }
            {

                var saldosCarteraPasoAlCobro = await saldosCarteraRepository
                                             .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto, fechaMovimiento);
                ValidarSaldosCartera(saldosCarteraPasoAlCobro, movimiento.IdIcetex);

                var saldosCarteraActual = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);
                ValidarSaldosCartera(saldosCarteraActual, movimiento.IdIcetex);

                var saldosCarteraAnterior = await saldosCarteraRepository
                                                .ObtenerSaldosCarteraPorId(saldosCarteraPasoAlCobro.IdSaldosCarteraAnt.Value);
                ValidarSaldosCartera(saldosCarteraAnterior, movimiento.IdIcetex);

                var diferenciaSaldo1 = saldosCarteraAnterior.Saldo1 - saldosCarteraPasoAlCobro.Saldo1;
                var diferenciaSaldo2 = saldosCarteraAnterior.Saldo2 - saldosCarteraPasoAlCobro.Saldo2;
                var diferenciaSaldo3 = saldosCarteraAnterior.Saldo3 - saldosCarteraPasoAlCobro.Saldo3;
                var diferenciaSaldo4 = saldosCarteraAnterior.Saldo4 - saldosCarteraPasoAlCobro.Saldo4;
                var nuevosSaldoCartera = (SaldosCarteraDTO) saldosCarteraActual.Clone();

                nuevosSaldoCartera.Saldo1 += diferenciaSaldo1;
                nuevosSaldoCartera.Saldo2 += diferenciaSaldo2;
                nuevosSaldoCartera.Saldo3 += diferenciaSaldo3;
                nuevosSaldoCartera.Saldo4 += diferenciaSaldo4;

                nuevosSaldoCartera.IdMovimiento = movimiento.IdMovimientoDiario;
                nuevosSaldoCartera.IdSaldosCarteraAnt = saldosCarteraActual.Id;
                var saldosCartera = this.MapearSaldosCarteraAfectaciones(saldosCarteraActual);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldosCartera);

                return nuevosSaldoCartera;
            }


           //return nuevosSaldosCartera;



        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }

        private static int ValidarSaldosCarteraConsolidado(SaldosCarteraDTO saldoConsolidado){
            var verificador = 0;
            if (saldoConsolidado != null)
            { verificador = 1; }
            return verificador;
        }

        private static void ValidarFechaEfectivaMovimiento(string fechaEfectivaMovimiento)
        {
            if (string.IsNullOrWhiteSpace(fechaEfectivaMovimiento))
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(fechaEfectivaMovimiento),
                        ErrorMessage = ValidationMessages.FormatoFechaEfectivaMovimiento
                    }
                ]);
            }

            string[] formatosValidos = {
                "yyyy-MM-dd HH:mm:ss.fff",
                "yyyy-MM-dd",
                "dd/MM/yyyy HH:mm:ss.fff",
                "dd/MM/yyyy"
            };

            if (!DateTime.TryParseExact(fechaEfectivaMovimiento, formatosValidos, CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(fechaEfectivaMovimiento),
                        ErrorMessage = ValidationMessages.FormatoFechaEfectivaMovimiento
                    }
                ]);
            }
        }

        private static DateTime ParsearFechaEfectivaMovimiento(string fechaEfectivaMovimiento)
        {
            string[] formatosValidos = {
                "yyyy-MM-dd HH:mm:ss.fff",
                "yyyy-MM-dd",
                "dd/MM/yyyy HH:mm:ss.fff",
                "dd/MM/yyyy"
            };

             var fecha = DateTime.ParseExact(fechaEfectivaMovimiento, formatosValidos, CultureInfo.InvariantCulture, DateTimeStyles.None);
             return fecha.Date;
        }
    }
}
