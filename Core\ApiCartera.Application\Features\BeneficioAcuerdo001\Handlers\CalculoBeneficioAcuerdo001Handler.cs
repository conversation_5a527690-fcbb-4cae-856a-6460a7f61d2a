﻿using ApiCartera.Application.Features.BeneficioAcuerdo001.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Handlers
{
    public class CalculoBeneficioAcuerdo001Handler(
    ICalcularBeneficioAcuerdo001Service calcularBeneficioAcuerdo001Service,
        IMapper mapper) : IRequestHandler<CalculoBeneficioAcuerdo001Command, InteresesLiquidadosVM>
    {
        public async Task<InteresesLiquidadosVM> Handle(CalculoBeneficioAcuerdo001Command request, CancellationToken cancellationToken)
        {
            var resultado = await calcularBeneficioAcuerdo001Service.CalcularBeneficio(request.Movimiento);
            return mapper.Map<InteresesLiquidadosVM>(resultado);
        }
    }
}
