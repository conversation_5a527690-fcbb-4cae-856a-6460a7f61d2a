﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("SOLICITUD")]
public class SolicitudEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public long IdSolicitud { get; set; }
    public string? IdSolicitante { get; set; }
    public int IdTipoLinea { get; set; }
    public int IdTipoSubLinea { get; set; }
    public string? CodSnies_Prog { get; set; }
}