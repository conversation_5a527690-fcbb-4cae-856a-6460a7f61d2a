﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("INSTITUCIONGIRO")]
public class InstitucionGiroEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;    
    public int YearsGiro { get; set; }
    public int SemestreGiro { get; set; }
    public string? Modalidad_Rubro { get; set; }
    public int? NoRelacion { get; set; }
}
