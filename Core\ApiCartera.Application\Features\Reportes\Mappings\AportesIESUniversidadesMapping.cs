﻿using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.DTOs;
using AutoMapper;
using System.Globalization;

namespace ApiCartera.Application.Features.Reportes.Mappings;

public class AportesIESUniversidadesMapping: Profile
{
    public AportesIESUniversidadesMapping()
    {
        CreateMap<DateTime?, string>().ConvertUsing(src => src.HasValue
            ? src.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture).Remove(10)
            : "");

        CreateMap<AportesIESUniversidadesDTO, AportesIESUniversidadesVm>();
    }
}
