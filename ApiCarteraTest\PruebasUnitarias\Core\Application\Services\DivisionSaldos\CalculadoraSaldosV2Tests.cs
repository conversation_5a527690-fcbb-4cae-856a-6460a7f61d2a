﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Resources;
using ApiCartera.Application.Services.DivisionSaldos;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.DivisionSaldos;

public class CalculadoraSaldosV2Tests
{
    private readonly Mock<ISolicitudCarteraActivaRepository> _solicitudCarteraActivaRepositoryMock;
    private readonly Mock<ICalcularTasasCarteraService> _calcularTasasCarteraServiceMock;
    private readonly Mock<IActualizarControlDivisionSaldos> _actualizarDivisionMock;
    private readonly CalculadoraSaldos _calculadoraSaldos;

    public CalculadoraSaldosV2Tests()
    {
        _solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
        _calcularTasasCarteraServiceMock = new Mock<ICalcularTasasCarteraService>();
        _actualizarDivisionMock = new Mock<IActualizarControlDivisionSaldos>();

        _calculadoraSaldos = new CalculadoraSaldos(
            _solicitudCarteraActivaRepositoryMock.Object,
            _calcularTasasCarteraServiceMock.Object,
            _actualizarDivisionMock.Object
        );
    }

    [Fact]
    public async Task ObtenerSaldos_SinDesembolsos_ActualizaControlDivisionSaldos()
    {
        long idSolicitud = 12345;
        var saldosSolicitudes = new List<SaldosSolicitudCarteraDTO>
        {
            new SaldosSolicitudCarteraDTO { Id = 1, CodigoSubproducto = "10001" }
        };
        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerPorIdSolicitud(idSolicitud))
            .ReturnsAsync(saldosSolicitudes);

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
            .ReturnsAsync(new List<DesembolsoDTO>());

        await _calculadoraSaldos.ObtenerSaldos(idSolicitud);

        _actualizarDivisionMock.Verify(
            x => x.ActualizarControlDivisionSaldos(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()),
            Times.AtLeastOnce
        );       
        
    }

    [Fact]
    public async Task ObtenerSaldos_ConPeriodicidadNull_ActualizaControlDivisionSaldos()
    {
        // Arrange
        long idSolicitud = 12345;
        var saldosSolicitudes = new List<SaldosSolicitudCarteraDTO>
        {
            new SaldosSolicitudCarteraDTO { Id = 1, CodigoSubproducto = "10001" }
        };
        var desembolsos = new List<DesembolsoDTO>
        {
            new DesembolsoDTO { Marca = "", Periodicidad = null, YearGiro = 2024 }
        };

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerPorIdSolicitud(idSolicitud))
            .ReturnsAsync(saldosSolicitudes);

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
            .ReturnsAsync(desembolsos);

        // Act
        await _calculadoraSaldos.ObtenerSaldos(idSolicitud);

        // Assert
        _actualizarDivisionMock.Verify(
            x => x.ActualizarControlDivisionSaldos(1, Constants.PERIODICIDAD_NULL, It.IsAny<int>()),
            Times.AtLeastOnce
        );
    }

    [Fact]
    public async Task ObtenerSaldos_ConDesembolsosCuentaCobroNacion_ActualizaControlDivisionSaldos()
    {
        // Arrange
        long idSolicitud = 12345;
        var saldosSolicitudes = new List<SaldosSolicitudCarteraDTO>
        {
            new SaldosSolicitudCarteraDTO { Id = 1, CodigoSubproducto = "CORTO_PLAZO" }
        };
        var desembolsos = new List<DesembolsoDTO>
        {
            new DesembolsoDTO { Marca = TiposBeneficio.CUENTA_COBRO_NACION }
        };

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerPorIdSolicitud(idSolicitud))
            .ReturnsAsync(saldosSolicitudes);

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
            .ReturnsAsync(desembolsos);

        // Act
        await _calculadoraSaldos.ObtenerSaldos(idSolicitud);

        // Assert
        _actualizarDivisionMock.Verify(
            x => x.ActualizarControlDivisionSaldos(1, TiposBeneficio.CUENTA_COBRO_NACION, It.IsAny<int>()),
            Times.AtLeastOnce
        );        
    }

    [Fact]
    public async Task ObtenerSaldos_ConAlertaEnFirme_ActualizaControlDivisionSaldos()
    {
        // Arrange
        long idSolicitud = 12345;
        var saldosSolicitudes = new List<SaldosSolicitudCarteraDTO>
        {
            new SaldosSolicitudCarteraDTO { Id = 1, CodigoSubproducto = "CORTO_PLAZO" }
        };
        var desembolsos = new List<DesembolsoDTO>
        {
            new DesembolsoDTO { Marca = TiposBeneficio.ALERTA, Estado = "EN FIRME" }
        };

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerPorIdSolicitud(idSolicitud))
            .ReturnsAsync(saldosSolicitudes);

        _solicitudCarteraActivaRepositoryMock
            .Setup(repo => repo.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
            .ReturnsAsync(desembolsos);

        // Act
        await _calculadoraSaldos.ObtenerSaldos(idSolicitud);

        _actualizarDivisionMock.Verify(x => x.ActualizarControlDivisionSaldos(1, Constants.ESTADO_ALERTA_EN_FIRME, It.IsAny<int>()),
            Times.AtLeastOnce
        );
    }
    [Fact]
    public void SumarDesembolsos_DatosValidos_CalculosCorrectos()
    {
        // Arrange
        var desembolsos = new List<DesembolsoDTO>
        {
            new DesembolsoDTO
            {
                Marca = TiposBeneficio.CONTRIBUCION_IES,
                Estado = Constants.ESTADO_DESEMBOLSO_EN_FIRME,
                TotalGirar = 1000,
                YearGiro = 2023,
                Fecha = DateTime.Now.AddMonths(-1),
                Periodicidad = 12
            },
            new DesembolsoDTO
            {
                Marca = TiposBeneficio.APORTES_IES,
                Estado = Constants.ESTADO_DESEMBOLSO_EN_FIRME,
                TotalGirar = 500,
                YearGiro = 2023,
                Fecha = DateTime.Now.AddMonths(-2),
                Periodicidad = 6
            },
            new DesembolsoDTO
            {
                Marca = null,
                Estado = Constants.ESTADO_DESEMBOLSO_EN_FIRME,
                TotalGirar = 300,
                YearGiro = 2024,
                Fecha = DateTime.Now.AddMonths(-3),
                Periodicidad = 12
            },
            new DesembolsoDTO
            {
                Marca = TiposBeneficio.NO_APLICA_BENEFICIO,
                Estado = Constants.ESTADO_DESEMBOLSO_EN_FIRME,
                TotalGirar = 400,
                YearGiro = 2024,
                Fecha = DateTime.Now.AddYears(-5),
                Periodicidad = 12
            }

        };

        // Mock del método FinalizoPlanPagos
        Func<DateTime, int, bool> mockFinalizoPlanPagos = (fecha, periodicidad) => fecha < DateTime.Now.AddYears(-2);

        // Act
        double sumaContribucionIES;
        double sumaAportesIES;
        double sumaSinBeneficiosSinFinalizarPlanPagos;
        double sumaSinBeneficiosPlanPagosFinalizado;

        CalculadoraSaldos.SumarDesembolsos(
            desembolsos,
            out sumaContribucionIES,
            out sumaAportesIES,
            out sumaSinBeneficiosSinFinalizarPlanPagos,
            out sumaSinBeneficiosPlanPagosFinalizado
        );

        // Assert
        Assert.Equal(1000, sumaContribucionIES);
        Assert.Equal(500, sumaAportesIES); 
        Assert.Equal(300, sumaSinBeneficiosSinFinalizarPlanPagos);
        Assert.Equal(400, sumaSinBeneficiosPlanPagosFinalizado);
    }
    [Fact]
    public async Task CalcularSaldosLargoPlazoCuandoNoHayDesembolsos_filtraYGUardaSaldo()
    {
        // Crear los mocks de las dependencias
        var mockSolicitudCarteraActivaRepository = new Mock<ISolicitudCarteraActivaRepository>();
        var mockCalcularTasasCarteraService = new Mock<ICalcularTasasCarteraService>();
        var mockActualizarControlDivisionSaldos = new Mock<IActualizarControlDivisionSaldos>();

        // Crear una instancia real de CalculadoraSaldos usando los mocks
        var calculadoraSaldos = new CalculadoraSaldos(
            mockSolicitudCarteraActivaRepository.Object,
            mockCalcularTasasCarteraService.Object,
            mockActualizarControlDivisionSaldos.Object
        );

        // Configurar los mocks si es necesario
        mockSolicitudCarteraActivaRepository
            .Setup(repo => repo.GuardarSaldos(It.IsAny<SaldosCartera>()))
            .Returns(Task.CompletedTask);

        mockCalcularTasasCarteraService
            .Setup(service => service.CalcularTasasYProporcionSaldos(It.IsAny<long>(), It.IsAny<int>()));

        mockActualizarControlDivisionSaldos
            .Setup(service => service.ActualizarControlDivisionSaldos(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        // Crear los datos de entrada
        var listaSaldos = new List<SaldosSolicitudCarteraDTO>
{
    new SaldosSolicitudCarteraDTO
    {
        CodigoSubproducto = TiposSubproducto.LARGO_PLAZO,
        SaldoCapitalVigente = 1000,
        IdSolicitud = 1,
        IdSubproducto = 10,
        Id = 100
    }
};

        // Actuar llamando al método real
        await calculadoraSaldos.CalcularSaldosLargoPlazoCuandoNoHayDesembolsos(listaSaldos);

        // Verificar si los métodos en las dependencias fueron llamados
        mockSolicitudCarteraActivaRepository.Verify(repo => repo.GuardarSaldos(It.IsAny<SaldosCartera>()), Times.Once);
        mockCalcularTasasCarteraService.Verify(service => service.CalcularTasasYProporcionSaldos(It.IsAny<long>(), It.IsAny<int>()), Times.Once);
        mockActualizarControlDivisionSaldos.Verify(service => service.ActualizarControlDivisionSaldos(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()), Times.Once);
       
    }
    [Fact]
    public async Task SubProductosSinDesembolsosQueNoHacenDivision_CuandoHaySubproductos_FiltraYGuardaCorrectamente()
    {
        // Arrange
        var mockSolicitudCarteraActivaRepository = new Mock<ISolicitudCarteraActivaRepository>();
        var mockCalcularTasasCarteraService = new Mock<ICalcularTasasCarteraService>();
        var mockActualizarDivision = new Mock<IActualizarControlDivisionSaldos>();

        var calculadoraSaldos = new CalculadoraSaldos(
            mockSolicitudCarteraActivaRepository.Object,
            mockCalcularTasasCarteraService.Object,
            mockActualizarDivision.Object
        );

        var listaSaldos = new List<SaldosSolicitudCarteraDTO>
    {
        new SaldosSolicitudCarteraDTO
        {
            CodigoSubproducto = TiposSubproducto.ALIANZAS_REEMBOLSABLE_LP_RA,
            TipoCartera = TiposCartera.ESTUDIOS,
            IdSolicitud = 1,
            IdSubproducto = 10,
            Id = 100
        },
        new SaldosSolicitudCarteraDTO
        {
            CodigoSubproducto = TiposSubproducto.ORI_CONDONABLE,
            TipoCartera = TiposCartera.ESTUDIOS,
            IdSolicitud = 2,
            IdSubproducto = 20,
            Id = 200
        }
    };
        mockSolicitudCarteraActivaRepository
          .Setup(repo => repo.GuardarSaldos(It.IsAny<SaldosCartera>()))
          .Returns(Task.CompletedTask);

        mockCalcularTasasCarteraService
            .Setup(service => service.CalcularTasasYProporcionSaldos(It.IsAny<long>(), It.IsAny<int>()));

        mockActualizarDivision
            .Setup(service => service.ActualizarControlDivisionSaldos(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()))
            .Returns(Task.CompletedTask);

        // Act
        await calculadoraSaldos.SubProductosSinDesembolsosQueNoHacenDivision(listaSaldos);

        // Assert
        mockSolicitudCarteraActivaRepository.Verify(repo => repo.GuardarSaldos(It.IsAny<SaldosCartera>()), Times.Exactly(2));
        mockCalcularTasasCarteraService.Verify(service => service.CalcularTasasYProporcionSaldos(It.IsAny<long>(), It.IsAny<int>()), Times.Exactly(2));
        mockActualizarDivision.Verify(service => service.ActualizarControlDivisionSaldos(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()), Times.Exactly(2));
    }
    [Fact]
    public async Task CalcularSaldosCortoPlazoCuandoNoHayDesembolsosAsync_CuandoHaySaldoCortoPlazo_GuardaSaldos()
    {
        // Arrange
        var mockSolicitudCarteraActivaRepository = new Mock<ISolicitudCarteraActivaRepository>();
        var mockCalcularTasasCarteraService = new Mock<ICalcularTasasCarteraService>();
        var mockActualizarDivision = new Mock<IActualizarControlDivisionSaldos>();

        var service = new CalculadoraSaldos(
            mockSolicitudCarteraActivaRepository.Object,
            mockCalcularTasasCarteraService.Object,
            mockActualizarDivision.Object
        );

        var saldosSolicitudes = new List<SaldosSolicitudCarteraDTO>
    {
        new SaldosSolicitudCarteraDTO
        {
            CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
            SaldoCapitalVigente = 1000,
            IdSolicitud = 1,
            IdSubproducto = 20,
            Id = 200
        }
    };

        mockSolicitudCarteraActivaRepository
            .Setup(repo => repo.GuardarSaldos(It.IsAny<SaldosCartera>()))
            .Returns(Task.CompletedTask);

        // Act
        await service.CalcularSaldosCortoPlazoCuandoNoHayDesembolsosAsync(saldosSolicitudes);

        // Assert
        mockSolicitudCarteraActivaRepository.Verify(
            repo => repo.GuardarSaldos(It.Is<SaldosCartera>(s =>
                s.Saldo1 == 1000 &&
                s.Saldo2 == 0 &&
                s.Saldo3 == 0 &&
                s.Saldo4 == 0 &&
                s.IdSubproducto == 20 &&
                s.IdSaldosSolicitudCartera == 200
            )),
            Times.Once
        );
    }
    [Fact]
    public void ConvertirFechaSiNull_CuandoFechaEsNull_AsignaFechaCorrecta()
    {
        // Arrange
        var desembolsos = new List<DesembolsoDTO>
    {
        new DesembolsoDTO { Fecha = null, SemestreGiro = 1, YearGiro = 2023 },
        new DesembolsoDTO { Fecha = null, SemestreGiro = 2, YearGiro = 2024 },
        new DesembolsoDTO { Fecha = new DateTime(2022, 5, 1), SemestreGiro = 1, YearGiro = 2022 } // Fecha no nula
    };

        // Act
        CalculadoraSaldos.ConvertirFechaSiNull(desembolsos);

        // Assert
        Assert.Equal(new DateTime(2023, 1, 1), desembolsos[0].Fecha); // Primer semestre del año 2023
        Assert.Equal(new DateTime(2024, 7, 1), desembolsos[1].Fecha); // Segundo semestre del año 2024
        Assert.Equal(new DateTime(2022, 5, 1), desembolsos[2].Fecha); // No cambia, ya tenía fecha
    }
    [Fact]
    public void CalcularSaldosCartera_CuandoSaldoCapitalVigenteEsCero_CalculaConCero()
    {
        // Arrange
        var saldosSolicitudCarteraActiva = new SaldosSolicitudCarteraDTO
        {
            Id = 1,
            TipoCartera = "Consumo",
            IdSolicitud = 123456789,
            IdSignature = 987654321,
            IdSolicitante = 456123789,
            IdSubproducto = 101,
            CodigoSubproducto = "CS001",
            SaldoCapitalVigente = 50000.75,
            SaldoTotalCapital = 120000.50,
            SaldoCapitalVencido = 2000.00,
            CuotasPendientesPorPagar = 12,
            PorcentajeCarteraPlan = 75.5,
            Periodicidad = 30,
            SumatoriaDesembolsosContribucionIES = 25000.00,
            SumatoriaDesembolsosAportesIES = 15000.00,
            SumatoriaDesembolsosSinBeneficiosSinFinalizarPlanPagos = 5000.00,
            SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado = 7000.00
        };

        // Act
        var resultado = CalculadoraSaldos.CalcularSaldosCartera(saldosSolicitudCarteraActiva);

        // Assert
        Assert.Equal(0, resultado.Saldo1);
        Assert.Equal(0, resultado.Saldo2);
        Assert.Equal(0, resultado.Saldo3);
        Assert.Equal(0, resultado.Saldo4);
    }
}


