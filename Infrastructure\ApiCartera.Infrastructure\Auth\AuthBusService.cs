﻿using ICETEX.Librerias.BusServicios;
using ICETEX.Librerias.BusServicios.Exceptions;
using ApiCartera.Application.Contracts.Auth;
using ApiCartera.Application.Contracts.Auth.Models;
using ApiCartera.Domain.Exceptions;

namespace ApiCartera.Infrastructure.Auth
{
    public class AuthBusService(BusService busService) : IAuthService
    {

        public async Task<InicioSesionResponse> IniciarSesion(InicioSesionRequest request)
        {
            try
            {
                var response = await busService.Services.PortalTransaccional.IniciarSesionPorEmail(request.Email, request.Password, request.IP);
                return new InicioSesionResponse
                {
                    Nombres = response.Respuesta.NombresUsuario,
                    Apellidos = response.Respuesta.ApellidosUsuario,
                    Email = response.Respuesta.Email,
                    Token = response.Respuesta.AccessToken
                };
            }
            catch (UsuarioInvalidoException ex)
            {
                throw new BaseException(ex.Message);
            }
        }

    }
}
