﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Reportes.DTOs
{
    public class AportesIESUniversidadesDTO
    {
        [JsonProperty("codigoSNIES")]
        public int? CodigoSNIES { get; set; }

        [JsonProperty("relacionGiro")]
        public long? RelacionGiro { get; set; }

        [JsonProperty("nombreCompleto")]
        public string? NombreCompleto { get; set; }

        [JsonProperty("documento")]
        public long? Documento { get; set; }

        [JsonProperty("idSolicitud")]
        public int? IdSolicitud { get; set; }

        [JsonProperty("tipoLineaCredito")]
        public string? TipoLineaCredito { get; set; }

        [JsonProperty("tipoSubLineaCredito")]
        public string? TipoSubLineaCredito { get; set; }

        [JsonProperty("tipoCartera")]
        public string? TipoCartera { get; set; }

        [JsonProperty("modalidadCredito")]
        public string? ModalidadCredito { get; set; }

        [JsonProperty("sisben")]
        public string? Sisben { get; set; }

        [JsonProperty("estrato")]
        public int? Estrato { get; set; }

        [JsonProperty("valorSubsidioIES")]
        public double? ValorSubsidioIES { get; set; }

        [JsonProperty("valorIESLargoPlazo")]
        public double? ValorIESLargoPlazo { get; set; }

        [JsonProperty("valorIESCortoPlazo")]
        public double? ValorIESCortoPlazo { get; set; }

        [JsonProperty("valorIcetexSubsidio")]
        public double? ValorIcetexSubsidio { get; set; }

        [JsonProperty("valorIcetexCredito")]
        public double? ValorIcetexCredito { get; set; }

        [JsonProperty("valorAlianzaSubsidio")]
        public double? ValorAlianzaSubsidio { get; set; }

        [JsonProperty("valorAlianzaCredito")]
        public double? ValorAlianzaCredito { get; set; }

        [JsonProperty("valorPrima")]
        public double? ValorPrima { get; set; }

        [JsonProperty("valorTotal")]
        public double? ValorTotal { get; set; }

        [JsonProperty("totalGirar")]
        public double? TotalGirar { get; set; }

        [JsonProperty("yearGiro")]
        public int? YearGiro { get; set; }

        [JsonProperty("semestreGiro")]
        public int? SemestreGiro { get; set; }

        [JsonProperty("fechaGiro")]
        public DateTime? FechaGiro { get; set; }

        [JsonProperty("estadoResolucion")]
        public string? EstadoResolucion { get; set; }

        [JsonProperty("valorLegRenovado")]
        public double? ValorLegRenovado { get; set; }

        [JsonProperty("porcentajeIPC")]
        public double? PorcentajeIPC { get; set; }

        [JsonProperty("puntos")]
        public double? Puntos { get; set; }

        [JsonProperty("porcentajePuntosEquivalentes")]
        public double? PorcentajePuntosEquivalentes { get; set; }

        [JsonProperty("valorExigible")]
        public double? ValorExigible { get; set; }

        [JsonProperty("valorNoExigible")]
        public double? ValorNoExigible { get; set; }

        [JsonProperty("periodicidad")]
        public int? Periodicidad { get; set; }

        [JsonProperty("tipoAnexo")]
        public string? TipoAnexo { get; set; }

        [JsonProperty("tipoBeneficio")]
        public string? TipoBeneficio { get; set; }

        [JsonProperty("porcentajeAporteIES")]
        public double? PorcentajeAporteIES { get; set; }

        [JsonProperty("valorAporteIES")]
        public double? ValorAporteIES { get; set; }

        [JsonProperty("porcentajeAporteIcetex")]
        public double? PorcentajeAporteIcetex { get; set; }

        [JsonProperty("valorAporteIcetex")]
        public double? ValorAporteIcetex { get; set; }

        [JsonProperty("porcentajeFondoSostenibilidad")]
        public double? PorcentajeFondoSostenibilidad { get; set; }

        [JsonProperty("subfondoIES")]
        public double? SubfondoIES { get; set; }

        [JsonProperty("valorPorcPiloRezagado")]
        public double? ValorPorcPiloRezagado { get; set; }

        [JsonProperty("conteoGirosConAporte")]
        public int? ConteoGirosConAporte { get; set; }

        [JsonProperty("saldoCapital")]
        public double? SaldoCapital { get; set; }

        [JsonProperty("diasInteres")]
        public int? DiasInteres { get; set; }

        [JsonProperty("factor2")]
        public double? Factor2 { get; set; }

        [JsonProperty("factor3")]
        public double? Factor3 { get; set; }

        [JsonProperty("esComplementario")]
        public string? EsComplementario { get; set; }

        [JsonProperty("fechaGiroAnteriorAporte")]
        public DateTime? FechaGiroAnteriorAporte { get; set; }

        [JsonProperty("fechaGiroConAporte")]
        public DateTime? FechaGiroConAporte { get; set; }

        [JsonProperty("nitIES")]
        public long? NitIES { get; set; }
    }
}
