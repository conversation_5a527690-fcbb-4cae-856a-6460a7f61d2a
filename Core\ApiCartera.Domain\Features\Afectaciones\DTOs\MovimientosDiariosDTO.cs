using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Afectaciones.DTOs
{
    public class MovimientosDiariosDTO
    {
        [JsonProperty("idMovimientoDiario")]
        public int IdMovimientoDiario { get; set; }

        [JsonProperty("idIcetex")]
        public long IdIcetex { get; set; }

        [JsonProperty("idSignature")]
        public long IdSignature { get; set; }

        [JsonProperty("sdoTotalCapitalVigente")]
        public decimal SdoTotalCapitalVigente { get; set; }

        [JsonProperty("fechaEfectivaMovimiento")]
        public DateTime FechaEfectivaMovimiento { get; set; }

        [JsonProperty("fechaPosteo")]
        public DateTime FechaPosteo { get; set; }

        [JsonProperty("montoMovimientoDeCapital")]
        public decimal MontoMovimientoDeCapital { get; set; }

        [Json<PERSON>roperty("codigoTipoMovtoDeCapital")]
        public int CodigoTipoMovtoDeCapital { get; set; }

        [Json<PERSON>roperty("nombreTipoMovtoDeCapital")]
        public string NombreTipoMovtoDeCapital { get; set; }

        [JsonProperty("codSubproductoXObligacion")]
        public string CodSubproductoXObligacion { get; set; }

        [JsonProperty("saldoTotalDeCapital")]
        public decimal SaldoTotalDeCapital { get; set; }

        [JsonProperty("saldoCapitalVencido")]
        public decimal SaldoCapitalVencido { get; set; }

        [JsonProperty("descripcionMovtoMemo")]
        public string DescripcionMovtoMemo { get; set; }

        [JsonProperty("codNovedad")]
        public string CodNovedad { get; set; }

        [JsonProperty("tipoCartera")]
        public string TipoCartera { get; set; }

        [JsonProperty("cuotasPendientesPorPagar")]
        public int CuotasPendientesPorPagar { get; set; }

        [JsonProperty("descripcionNovacion")]
        public string? DescripcionNovacion { get; set; }

        [JsonProperty("referenciaCus")]
        public string? ReferenciaCus { get; set; }

        [JsonProperty("year")]
        public int Year { get; set; }

        [JsonProperty("semestre")]
        public int Semestre { get; set; }

        [JsonProperty("nombreArchivo")]
        public string NombreArchivo { get; set; }

        [JsonProperty("fechaEjecucion")]
        public DateTime FechaEjecucion { get; set; }

        [JsonProperty("controlMovimiento")]
        public int ControlMovimiento { get; set; }
    }
}