using AutoMapper;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Application.Features.Parametricas.Queries.Delitos.ObtenerTodosDelitos;
using Sarlaft.Application.Features.Parametricas.ViewModels;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Shared.Extensions;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Delitos
{
    public class ObtenerTodosDelitosTest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var mockRepository = new Mock<IDelitoRepository>();
            var mockMapper = new Mock<IMapper>();

            var handler = new ObtenerTodosDelitosQueryHandler(mockRepository.Object, mockMapper.Object);

            var query = new ObtenerTodosDelitosQuery();
            var fileName = "test.xls";
            List<Delito> delitos = new()
            {
                new ()
                {
                    Id = EntityExtensionTest.GenerateId(),
                    NombreArchivo = fileName,
                    UbicacionRelativa = $"{Folders.Delitos}/{fileName}",
                    FechaCreacion = DateTime.Now
                },

                new ()
                {
                    Id = EntityExtensionTest.GenerateId(),
                    NombreArchivo = fileName,
                    UbicacionRelativa = $"{Folders.Delitos}/{fileName}",
                    FechaCreacion = DateTime.Now
                },
            };
            var delitosVm = delitos.Select(delito => new DelitoVm
            {
                Id = delito.Id,
                Nombre = delito.Nombre,
                TipoDelito = delito.TipoDelito,
                FechaUltimoCargue = delito.FechaUltimoCargue.ToFullDateTime(),
                FechaCreacion = delito.FechaCreacion.ToFullDateTime(),
                NombreArchivo = delito.NombreArchivo,
                FechaUltimaActualizacion = delito.FechaUltimaActualizacion.ToFullDateTime()
            }).ToList() ?? new List<DelitoVm>();

            // Configura el comportamiento esperado para el repositorio y el mapeador
            mockRepository.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(delitos);
            mockMapper.Setup(storage => storage.Map<List<DelitoVm>>(delitos)).Returns(delitosVm);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(result.Count(), delitosVm.Count());
        }
    }
}