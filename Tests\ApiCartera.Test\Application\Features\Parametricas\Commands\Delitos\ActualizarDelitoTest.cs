using MediatR;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Contracts.Reader;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Application.Features.Parametricas.Commands.Delitos.ActualizarDelito;
using Sarlaft.Domain.Features.Files.Entities;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Parametricas.Enums;
using Sarlaft.Test.Application.Config;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.Commands.Delitos
{
    public class ActualizarDelitoTest
    {
        private byte[] fileBytesTest => Enumerable.Repeat((byte)0x55, 20).ToArray();
        [Fact]
        public async Task ActualizarDelito_Correcto_ConDocumento()
        {
            //Arrange
            var validator = new ActualizarDelitoCommandValidator();
            var repository = new Mock<IDelitoRepository>();
            var mediator = new Mock<IMediator>();
            var storage = new Mock<IStorageService>();
            var reader = new Mock<IExcelReader>();
            var mapper = MapperConfig.Build();

            var file = new CustomFile(fileBytesTest, "test.xls", ApplicationTypes.ExcelNuevo);
            var nombreDelito = "Secuestro";
            var nombreDelitoNuevo = "Secuestro otro";
            var tipoDelito = TipoDelitoEnum.FT;
            var delito = new Delito
            {
                Nombre = nombreDelito,
                TipoDelito = tipoDelito,
            };
            delito.Id = EntityExtensionTest.GenerateId();
            var command = new ActualizarDelitoCommand(delito.Id, nombreDelitoNuevo, tipoDelito, file);
            var handler = new ActualizarDelitoCommandHandler(storage.Object, repository.Object, reader.Object, mediator.Object);




            repository.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(new List<Delito> {
                delito
            });
            repository.Setup(repo => repo.ObtenerPorIdAsync(It.IsAny<int>())).ReturnsAsync(delito);

            //Act
            var validationResult = validator.Validate(command);
            await handler.Handle(command, CancellationToken.None);

            //Assert
            repository.Verify(repo => repo.ActualizarAsync(It.IsAny<Delito>()));
            var entities = await repository.Object.ObtenerTodosAsync();
            var entity = await repository.Object.ObtenerPorIdAsync(delito.Id);
            Assert.NotNull(entities);
            Assert.NotEqual(default, delito.Id);
            Assert.Equal(1, entities?.Count());
            Assert.Equal(nombreDelitoNuevo, entity.Nombre);
            Assert.True(validationResult.IsValid);
        }


    }
}