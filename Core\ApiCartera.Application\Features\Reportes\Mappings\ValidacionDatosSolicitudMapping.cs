﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.DTOs;
using AutoMapper;

namespace ApiCartera.Application.Features.Reportes.Mappings
{
    public class ValidacionDatosSolicitudMapping: Profile
    {
        public ValidacionDatosSolicitudMapping()
        {
            CreateMap<ValidacionDatosSolicitudDTO, ValidacionDatosSolicitudVm>();
        }
    }
}
