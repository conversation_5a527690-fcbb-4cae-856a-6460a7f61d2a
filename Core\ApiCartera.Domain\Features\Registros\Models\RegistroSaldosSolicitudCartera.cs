﻿using CsvHelper.Configuration.Attributes;

namespace ApiCartera.Domain.Features.Registros.Models
{
    public class RegistroSaldosSolicitudCartera
    {
        [Name("D755IDICE")]
        public string IdIcetex { get; set; }
        [Name("D755IDSIG")]
        public string IdSignature { get; set; }
        [Name("D755CAVIG")]
        public double SaldoCapitalVigente { get; set; }
        [Name("D755FECMO")]
        public string FechaEfectivaMovimiento { get; set; }
        [Name("D755FPMOV")]
        public string FechaPosteoMovimiento { get; set; }
        [Name("D755PRODU")]
        public string Producto { get; set; }
        [Name("D755CAPIT")]
        public double SaldoCapitalTotal { get; set; }
        [Name("D755CAVEN")]
        public double SaldoCapitalVencido { get; set; }
        [Name("D755TCART")]
        public string TipoCartera { get; set; }
        [Name("D755CUPEN")]
        public int CuotasPendientesPorPagar { get; set; }
        [Name("D755FEFIN")]
        public string FechaFinalizacionPlanPagos { get; set; }
    }
}
