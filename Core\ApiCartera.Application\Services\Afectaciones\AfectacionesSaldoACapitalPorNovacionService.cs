﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.Constants;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation.Results;
using System.Text.RegularExpressions;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorNovacionService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            ValidarMovimientoAfectacion(movimiento);
            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCarteraActual = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);
            ValidarSaldosCartera(saldosCarteraActual, movimiento.IdIcetex);

            var (valorDesde, valorHacia) = ExtraerValores(movimiento.DescripcionMovimientoMemo);
            var nuevosSaldosCartera = (SaldosCarteraDTO)saldosCarteraActual.Clone();
            var nuevosSaldosCarteraCreditoNuevo = (SaldosCarteraDTO)saldosCarteraActual.Clone();

            nuevosSaldosCartera = this.ConfigurarSaldosACero(nuevosSaldosCartera);

            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCarteraActual.Id;
            var nuevosSaldos = MapearSaldosCarteraAfectaciones(nuevosSaldosCartera);
            await solicitudCarteraActivaRepository.GuardarSaldos(nuevosSaldos);

           
            nuevosSaldosCarteraCreditoNuevo.IdSolicitud = valorHacia;
            nuevosSaldosCarteraCreditoNuevo.IdMovimiento = movimiento.IdMovimientoDiario;
            nuevosSaldosCarteraCreditoNuevo.IdSaldosCarteraAnt = 0;
            nuevosSaldosCarteraCreditoNuevo.Saldo1 = movimiento.MontoMovimientoCapital;

            var nuevosSaldosCreditoNuevo = MapearSaldosCarteraAfectaciones(nuevosSaldosCarteraCreditoNuevo);
            await solicitudCarteraActivaRepository.GuardarSaldos(nuevosSaldosCreditoNuevo);

            return nuevosSaldosCarteraCreditoNuevo;
        }

        public static (int desde, int hacia) ExtraerValores(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return (0, 0);

            var pattern = @"^Novado desde:\s*(\d+)\s+hacia:\s*(\d+)$";
            var match = Regex.Match(input, pattern);

            if (!match.Success)
                return (0, 0);

            if (int.TryParse(match.Groups[1].Value, out int desde) &&
                int.TryParse(match.Groups[2].Value, out int hacia))
            {
                return (desde, hacia);
            }

            return (0, 0);
        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }
    }
}
