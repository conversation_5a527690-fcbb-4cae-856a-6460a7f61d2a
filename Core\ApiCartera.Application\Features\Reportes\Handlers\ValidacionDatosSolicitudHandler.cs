﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Application.Services.Reportes;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Reportes.Handlers
{
    public class ValidacionDatosSolicitudHandler(
        IReporteValidacionDatosSolicitudService reporteValidacionDatosSolicitudService,
        IMapper mapper
        ) : IRequestHandler<ValidacionDatosSolicitudQuery, List<ValidacionDatosSolicitudVm>>
    {
        public async Task<List<ValidacionDatosSolicitudVm>> Handle(ValidacionDatosSolicitudQuery request, CancellationToken cancellationToken)
        {
            var resultado = await reporteValidacionDatosSolicitudService.ObtenerReporte(request.Desde, request.Hasta);
            return mapper.Map<List<ValidacionDatosSolicitudVm>>(resultado);
        }
    }
}
