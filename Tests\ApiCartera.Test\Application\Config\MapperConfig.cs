﻿using AutoMapper;
using FluentValidation;
using Sarlaft.Application.Features.Parametricas.Mappings;
using System.Reflection;

namespace Sarlaft.Test.Application.Config
{
    public static class  MapperConfig
    {
        public static IMapper Build()
        { 
            var assembly = Assembly.GetAssembly(typeof(DelitosMappings));
            var profiles = assembly.GetTypes()
            .Where(type => type.IsSubclassOf(typeof(Profile)))
            .Select(Activator.CreateInstance)
            .Cast<Profile>()
            .ToList();

            var config = new MapperConfiguration(cfg =>
            {
                foreach (var profile in profiles)
                {
                    cfg.AddProfile(profile);
                }
            });

            return config.CreateMapper();
        }
    }
}
