﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using ApiCartera.Application.Contracts.Reader;
using ApiCartera.Domain.Attributes;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Files.Messages;
using System.Reflection;

namespace ApiCartera.Infrastructure.Reader
{
    public class ExcelReader : IExcelReader
    {
        public List<TEntity> ReadTableExcel<TEntity>(byte[] file, bool validateCellEmpty = false) where TEntity : new()
        {
            List<TEntity> listaEntidades = new List<TEntity>();

            using (MemoryStream memoryStream = new MemoryStream(file))
            {
                IWorkbook workbook = new XSSFWorkbook(memoryStream); 

                ISheet sheet = workbook.GetSheetAt(0); 

                // Obtén los títulos de las columnas desde la primera fila
                IRow headerRow = sheet.GetRow(0);

                // Mapea las propiedades de la entidad con los títulos de las columnas
                Dictionary<string, PropertyInfo> propertyMap = new Dictionary<string, PropertyInfo>();
                foreach (var property in typeof(TEntity).GetProperties())
                {
                    string columnHeader = property.Name; // Por defecto, usa el nombre de la propiedad
                    var columnHeaderAttribute = property.GetCustomAttribute<ColumnHeaderAttribute>();
                    if (columnHeaderAttribute != null)
                    {
                        columnHeader = columnHeaderAttribute.Name;
                    }

                    propertyMap[columnHeader] = property;
                }

                // Itera sobre las filas del archivo Excel, comenzando desde la segunda fila (índice 1)
                for (int row = 1; row <= sheet.LastRowNum; row++)
                {
                    IRow excelRow = sheet.GetRow(row);

                    TEntity entidad = new ();

                    // Itera sobre las celdas de la fila
                    foreach (var kvp in propertyMap)
                    {
                        int columnIndex = headerRow.Cells.FindIndex(c => c.StringCellValue.Trim() == kvp.Key);
                        if (columnIndex != -1)
                        {
                            // Asegúrate de que el índice de columna sea válido
                            ICell cell = excelRow.GetCell(columnIndex);
                            if (validateCellEmpty && (cell == null || string.IsNullOrEmpty(cell.ToString())))
                                throw new NullException(string.Format(FileValidationMessages.CeldaVacia, kvp.Key, row + 1));
                            if (cell != null)
                            {
                                // Convierte el valor de la celda al tipo de propiedad
                                object cellValue = GetCellValue(cell, kvp.Value.PropertyType);
                                kvp.Value.SetValue(entidad, cellValue);
                            }
                        }
                        else
                        {
                            throw new NotFoundException(string.Format(FileValidationMessages.NoSeEncontroColumnaExcel, kvp.Key));
                        }
                    }

                    listaEntidades.Add(entidad);
                }
            }

            return listaEntidades;
        }

        static object GetCellValue(ICell cell, Type targetType)
        {
            // Agrega lógica para convertir el valor de la celda al tipo de propiedad
            if (targetType == typeof(int))
            {
                return (int)cell.NumericCellValue;
            }
            else if (targetType == typeof(string))
            {
                return cell.ToString();
            }
            else if (targetType == typeof(DateTime))
            {
                return cell.DateCellValue;
            }
            // Agrega más casos según los tipos de datos que necesites manejar

            // Si no hay coincidencia con ningún tipo, simplemente devuelve el valor de la celda
            return cell.ToString();
        }
    }
}
