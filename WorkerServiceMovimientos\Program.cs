using ApiCartera.Application.Contracts.Persistence.Http;
using ApiCartera.Application.Contracts.Persistence.Oracle;
using ApiCartera.Application.Contracts.Persistence.Registros;
using ApiCartera.Application.Services.Registros;
using ApiCartera.Domain.Features.Registros.DTOs;
using ApiCartera.Domain.Features.Registros.Services;
using ApiCartera.Infrastructure.Http;
using ApiCartera.Infrastructure.Persitence.Oracle;
using ApiCartera.Infrastructure.Registros;
using Serilog;
using WorkerServiceMovimientos;

var builder = Host.CreateApplicationBuilder(args);

Log.Logger = new LoggerConfiguration()
    .WriteTo.File("Logs/log-.txt", rollingInterval: RollingInterval.Day)
    .ReadFrom
    .Configuration(builder.Configuration)
    .CreateLogger();

builder.Logging.ClearProviders();
builder.Logging.AddSerilog();

builder.Services.Configure<SftpSettingsDTO>(builder.Configuration.GetSection("SftpSettings"));
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
builder.Services.AddSingleton<IGestorRegistrosArchivosUT, GestorRegistrosSaldosSolicitudCartera>();
builder.Services.AddSingleton<ISFTPRepository, SFTPRepository>();
builder.Services.AddSingleton<IOracleRepository, OracleRepository>();
builder.Services.AddSingleton<IHttpRequestRepository, HttpRequestRepository>();

builder.Services.AddHostedService<Worker>();
builder.Services.AddProblemDetails();

var host = builder.Build();
host.Run();
