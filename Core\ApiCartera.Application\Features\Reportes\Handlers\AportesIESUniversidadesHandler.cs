﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Reportes.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Reportes.Handlers;

public class AportesIESUniversidadesHandler(
IReporteAportesIESUniversidadesService reporteAportesIESUniversidadesService,
IMapper mapper
) : IRequestHandler<AportesIESUniversidadesQuery, List<AportesIESUniversidadesVm>>
{
    public async Task<List<AportesIESUniversidadesVm>> Handle(AportesIESUniversidadesQuery request, CancellationToken cancellationToken)
    {
        List<AportesIESUniversidadesDTO>? resultado = await reporteAportesIESUniversidadesService.ObtenerReporte(request.Desde, request.Hasta, request.page);
        return mapper.Map<List<AportesIESUniversidadesVm>>(resultado);
    }
}
