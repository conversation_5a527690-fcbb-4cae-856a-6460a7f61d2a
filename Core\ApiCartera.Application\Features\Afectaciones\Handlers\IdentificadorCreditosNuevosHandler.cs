﻿using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class IdentificadorCreditosNuevosHandler(
        IAfectacionesIdentificacionNuevosCreditos _afectacionesIdentificacionNuevosCreditos,
        IMapper _mapper) : IRequestHandler<IdentificadorCreditosNuevosCommand, SaldoCarteraVm>
    {
        public async Task<SaldoCarteraVm> Handle(IdentificadorCreditosNuevosCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _afectacionesIdentificacionNuevosCreditos.IdentificacionNuevosCreditos(request.dataNuevosCreditosDTO);
            return _mapper.Map<SaldoCarteraVm>(resultado);
        }
    }
}
