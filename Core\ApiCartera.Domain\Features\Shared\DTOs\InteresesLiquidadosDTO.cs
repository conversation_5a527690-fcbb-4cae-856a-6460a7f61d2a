﻿namespace ApiCartera.Domain.Features.Shared.DTOs;

public class InteresesLiquidadosDTO
{
    public int Id { get; set; }
    public int IdSaldosSolicitudCartera { get; set; }
    public int IdMovimiento { get; set; }
    public double BeneficioAcuerdo001Saldo1 { get; set; }
    public double AjusteCIESSaldo2 { get; set; }
    public double AjusteAportesIESSaldo3 { get; set; }
    public int DiasCalculo { get; set; }
    public double? ValorTasaContratacion { get; set; }
    public string? IpcContratacion { get; set; }
    public double? ValorTasaBeneficio { get; set; }
    public string? IpcBeneficio { get; set; }
    public double? ValorLimiteAjuste { get; set; }
    public double? ValorAjusteAcumulado { get; set; }
}
