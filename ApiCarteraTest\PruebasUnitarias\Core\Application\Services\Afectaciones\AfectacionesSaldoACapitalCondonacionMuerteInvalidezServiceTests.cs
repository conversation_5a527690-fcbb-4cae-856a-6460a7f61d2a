﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalCondonacionMuerteInvalidezServiceTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosCondonacionMuerteInvalidez), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazo_RetornaSaldosEnCero(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 0,
                Saldo2 = 0,
                Saldo3 = 544892.52,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 29456561.67,
                Saldo2 = 0,
                Saldo3 = 7262220,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            var afectacionesSaldoACapitalCondonacionMuerteInvalidezService = new AfectacionesSaldoACapitalCondonacionMuerteInvalidezService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await afectacionesSaldoACapitalCondonacionMuerteInvalidezService.CalcularSaldosCartera(movimiento);
            
            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(0, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
        }
    }
}
