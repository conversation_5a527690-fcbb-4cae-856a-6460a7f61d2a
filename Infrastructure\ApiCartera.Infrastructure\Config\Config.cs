﻿using ICETEX.Librerias.BusServicios;
using ICETEX.Librerias.BusServicios.Services.PortalTransaccional;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ApiCartera.Application.Contracts.Auth;
using ApiCartera.Application.Contracts.Reader;
using ApiCartera.Application.Contracts.Storage;
using ApiCartera.Infrastructure.Auth;
using ApiCartera.Infrastructure.Reader;
using ApiCartera.Infrastructure.Storage;

namespace ApiCartera.Infrastructure.Config;

public static class Config
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterPersistence();

        services.AddScoped<IStorageService, StorageLocalService>();

        services.AddScoped<IAuthService, AuthBusService>();

        var busService = configuration.GetSection("BusService");
        services.AddBusServiceICETEX(options =>
            options
            .WithUrl(busService["urlBusServicios"])
            .SetToken<PortalTransaccionalService>(busService["tokenPortal"])
            .SetTimeOut(60)
            .NotRequiredTokenGlobal()
        );

        services.AddScoped<IExcelReader, ExcelReader>();

        return services;
    }
}
