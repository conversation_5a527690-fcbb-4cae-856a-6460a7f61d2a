﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using ApiCartera.Infrastructure.Persitence.BusDataService;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;

namespace ApiCartera.Infrastructure.Persitence
{ 
    public class ReporteContribucionesAportesIESDataService(BusService busService) : GenericRepositoryBusDataService<Solicitud>(busService), IReporteContribucionesAportesIESRepository
    {
        public async Task<List<ContribucionesAportesIESDTO>> ObtenerReporte(DateTime desde, DateTime hasta)
        {
            var url = $"reportecontribucionesaportesies?desde={desde}&hasta={hasta}";

            var respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<ContribucionesAportesIESDTO>>>(url);

            if (respuesta.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(desde)} y {nameof(hasta)}",
                        ErrorMessage = respuesta.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuesta.Data;

        }
    }
}
