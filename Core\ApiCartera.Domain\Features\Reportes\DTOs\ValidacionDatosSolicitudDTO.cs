﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Reportes.DTOs
{
    public class ValidacionDatosSolicitudDTO
    {
        [JsonProperty("idSolicitud")]
        public int IdSolicitud { get; set; }

        [JsonProperty("sumatoriaSaldos")]
        public double SumatoriaSaldos { get; set; }

        [JsonProperty("valorCapitalCore")]
        public double ValorCapitalCore { get; set; }

        [JsonProperty("diferencia")]
        public double Diferencia { get; set; }
    }
}
