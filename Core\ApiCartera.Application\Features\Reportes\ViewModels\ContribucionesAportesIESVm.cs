﻿namespace ApiCartera.Application.Features.Reportes.ViewModels;

    public record ContribucionesAportesIESVm(
        int IdSolicitud,
        int IdSignature,
        string Linea,
        string Sublinea,
        string CodigoSNIES,
        string TipoCartera,
        string ModalidadCredito,
        string PeriodoGiro,
        string Anexo,
        double PorcentajeAsumeIES,
        double PorcentajeAsumeIcetex,
        string? FechaGiro,
        string? FechaGeneracion,
        double DiasCalculo,
        double CapitalExigible,
        double CapitalNoExigible,
        double Saldo1,
        double AjusteSaldo1,
        double Saldo2,
        double AjusteSaldo2,
        double Saldo3,
        double AjusteSaldo3,
        double Saldo4,
        double AjusteSaldo4,
        double ValorTasaContratacion,
        string IPCContratacion,
        double TasaMinimaSaldo1LargoPlazo,
        double TasaMinimaSaldo2LargoPlazo,
        double TasaMinimaSaldo3LargoPlazo,
        double TasaMinimaSaldo4LargoPlazo,
        double TasaMaximaSaldo1LargoPlazo,
        double TasaMaximaSaldo2LargoPlazo,
        double TasaMaximaSaldo3LargoPlazo,
        double TasaMaximaSaldo4LargoPlazo,
        double Factor1,
        double Factor2,
        double Factor3,
        double Factor4,
        double ResultadoFactor2Factor3,
        double ValorAporteAplicado,
        double ValorAporteIES,
        double ValorAjuste,
        string ValidadorAjuste,
        double SaldoAporte,
        string? FechaFinEjecucion
);
