﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;

public class TasasSaldosCarteraDTO
{
    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("idSolicitud")]
    public long IdSolicitud { get; set; }

    [JsonProperty("tasaMinimaSaldo1")]
    public double TasaMinimaSaldo1 { get; set; }

    [JsonProperty("tasaMinimaSaldo2")]
    public double TasaMinimaSaldo2 { get; set; }

    [JsonProperty("tasaMinimaSaldo3")]
    public double TasaMinimaSaldo3 { get; set; }

    [JsonProperty("tasaMinimaSaldo4")]
    public double TasaMinimaSaldo4 { get; set; }

    [JsonProperty("tasaMaximaSaldo1")]
    public double TasaMaximaSaldo1 { get; set; }

    [JsonProperty("tasaMaximaSaldo2")]
    public double TasaMaximaSaldo2 { get; set; }

    [JsonProperty("tasaMaximaSaldo3")]
    public double TasaMaximaSaldo3 { get; set; }

    [JsonProperty("tasaMaximaSaldo4")]
    public double Tasa<PERSON>aximaSaldo4 { get; set; }

    [JsonProperty("proporcionSaldo1")]
    public double ProporcionSaldo1 { get; set; }

    [JsonProperty("proporcionSaldo2")]
    public double ProporcionSaldo2 { get; set; }

    [JsonProperty("proporcionSaldo3")]
    public double ProporcionSaldo3 { get; set; }

    [JsonProperty("proporcionSaldo4")]
    public double ProporcionSaldo4 { get; set; }

    [JsonProperty("idSubproducto")]
    public int IdSubproducto { get; set; }

    [JsonProperty("codigoSubproducto")]
    public string CodigoSubproducto { get; set; }
    public int? IdMovimiento { get; set; }
    public int? IdSaldosSolicitudCartera { get; set; }
}
