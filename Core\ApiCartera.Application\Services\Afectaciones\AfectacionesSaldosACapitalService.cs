﻿using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Models.CICETEX;
using ApiCartera.Domain.ValueObjects;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldosACapitalService(
    IRepository<SolicitudEntity, string> sRepository,
    IRepository<SolicitudBeneficioEntity, string> sbRepository,
    IRepository<MbtSaldosSolicitudCarteraEntity, string> sscRepository,
    IRepository<MbtSubProductosEntity, string> spRepository,
    IRepository<TipoSubLineaCreditoEntity, string> tslcRepository,
    IRepository<BeneficiosEntity, string> beneficiosRepository,
    IRepository<InstitucionGiroDetalleEntity, string> institucionGiroDetalleRepository,
    IRepository<InstitucionGiroEntity, string> institucionGiroRepository,
    IRepository<TipoDuracionEntity, string> tipoDuracionRepository,
    IRepository<TipoProgramaEntity, string> tipoProgramaRepository,
    IRepository<MbtMovimientoDiarioEntity, string> movimientoDiarioRepository
    ) : IAfectacionesSaldosACapitalService
    {
        private readonly IRepository<SolicitudEntity, string> _sRepository = sRepository;
        private readonly IRepository<SolicitudBeneficioEntity, string> _sbRepository = sbRepository;
        private readonly IRepository<MbtSaldosSolicitudCarteraEntity, string> _sscRepository = sscRepository;
        private readonly IRepository<MbtSubProductosEntity, string> _spRepository = spRepository;
        private readonly IRepository<TipoSubLineaCreditoEntity, string> _tslcRepository = tslcRepository;
        private readonly IRepository<BeneficiosEntity, string> _beneficiosRepository = beneficiosRepository;
        private readonly IRepository<InstitucionGiroDetalleEntity, string> _institucionGiroDetalleRepository = institucionGiroDetalleRepository;
        private readonly IRepository<InstitucionGiroEntity, string> _institucionGiroRepository = institucionGiroRepository;
        private readonly IRepository<TipoDuracionEntity, string> _tipoDuracionRepository = tipoDuracionRepository;
        private readonly IRepository<TipoProgramaEntity, string> _tipoProgramaRepository = tipoProgramaRepository;
        private readonly IRepository<MbtMovimientoDiarioEntity, string> _movimientoDiarioRepository = movimientoDiarioRepository;


    }
}
