﻿using AutoMapper;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;

namespace Sarlaft.Test.Application.Config
{
    public class ConfigTest
    {
        [Fact]
        public void AddApplicationServices_DeberiaConfigurarAutoMapperCorrectamente()
        {
            // Arrange
            IServiceCollection services = new ServiceCollection();

            // Act
            Sarlaft.Application.Config.Config.AddApplicationServices(services);

            // Assert
            // Realiza más aserciones según sea necesario
        }
    }
}
