using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Events;
using Sarlaft.Application.Features.Parametricas.Events.Handlers;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Parametricas.ValueObjects;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.EventsHandlers
{
    public class DANECargadoTest
    {
        private List<DANEExcel> excel = new()
            {
                new()
                {
                    Municipio = "Bucaramanga",
                    CodigoMunicipio = "031",
                    Departamento = "Santander",
                    CodigoDepartamento = "12",
                    CodigoDANE = "12031",
                    Poblacion = 888371
                },
                new()
                {
                    Municipio = "Piedecuesta",
                    CodigoMunicipio = "032",
                    Departamento = "Santander",
                    CodigoDepartamento = "12",
                    CodigoDANE = "12032",
                    Poblacion = 23412
                },
            };

        [Fact]
        public async Task DANECargado_Crear_Correcto()
        {
            //Arrange
            var repositoryCiudad = new Mock<IGenericRepository<Ciudad>>();
            var repositoryDepartamento = new Mock<IGenericRepository<Departamento>>();
            var id = EntityExtensionTest.GenerateId();

            var command = new DANECargado(id, excel);
            var handler = new DANECargadoHandler(repositoryCiudad.Object, repositoryDepartamento.Object);



            repositoryCiudad.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<Ciudad>());
            var departamentoReturn = new Departamento { Nombre = "Santander", CodigoDANE = "12" };
            departamentoReturn.GenerateId();
            repositoryDepartamento.SetupSequence(repo => repo.ObtenerTodosAsync()).ReturnsAsync(new List<Departamento>())
                .ReturnsAsync(new List<Departamento>() { departamentoReturn });

            //Act
            await handler.Handle(command, CancellationToken.None);

            //Assert
            repositoryCiudad.Verify(repo => repo.CrearAsync(It.IsAny<Ciudad>()));
            repositoryDepartamento.Verify(repo => repo.CrearAsync(It.IsAny<Departamento>()));
            Assert.Equal(excel.Count + 1, repositoryCiudad.Invocations.Count);
        }

        [Fact]
        public async Task DANECargado_Actualizar_Correcto()
        {
            //Arrange
            var repositoryCiudad = new Mock<IGenericRepository<Ciudad>>();
            var repositoryDepartamento = new Mock<IGenericRepository<Departamento>>();
            var id = 1;

            var command = new DANECargado(id, excel);
            var handler = new DANECargadoHandler(repositoryCiudad.Object, repositoryDepartamento.Object);


            var ciudadesEnBd = excel
                .Select(registro => new Ciudad { Nombre = registro.Municipio, CodigoDANE = registro.CodigoMunicipio, CodigoDANECompleto = registro.CodigoDANE })
                .Select(entity =>
                {
                    new Random().Next(1,9999);
                    return entity;
                })
                .ToList();
            var departamentosEnBd = excel
                .GroupBy(registro => registro.CodigoDepartamento)
                .Select(group =>
                {
                    var registro = group.First();
                    var entity = new Departamento { Nombre = registro.Departamento, CodigoDANE = registro.CodigoDepartamento };
                    entity.Id = new Random().Next(1, 999999);
                    return entity;
                })
                .ToList();
            repositoryCiudad.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(ciudadesEnBd);
            repositoryDepartamento.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(departamentosEnBd);

            //Act
            await handler.Handle(command, CancellationToken.None);

            //Assert
            repositoryCiudad.Verify(repo => repo.ActualizarAsync(It.IsAny<Ciudad>()), Times.Exactly(2));
            repositoryDepartamento.Verify(repo => repo.ActualizarAsync(It.IsAny<Departamento>()), Times.Exactly(1));
        }


    }
}