﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorAdjudicacionService(ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
        ICalculadoraSaldosService calculadoraSaldosService
        ) : AfectacionesService, IAfectacionesSaldoACapitalPorAdjudicacionService
    {
        public async Task<List<SaldosCarteraDTO>> CalcularSaldosCartera(MovimientoDTO movimiento)
        {
            var saldosCarteraSolicitud = new List<SaldosCarteraDTO>();
            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);

            if (saldosCartera == null)
            {
                //Ajuste por el tipo de campo NoRelacion
                var NumeroRelacion = int.Parse(movimiento.NoRelacion);

                var desembolso = await solicitudCarteraActivaRepository.ObtenerDesembolsoPorNoRelacion(movimiento.IdSolicitud, NumeroRelacion);
                ValidarDesembolso(desembolso);
                
                var solicitud = await solicitudCarteraActivaRepository.ObtenerSolicitudPorId(movimiento.IdSolicitud, movimiento.FechaMovimiento);
                if (solicitud.TipoSublinea.Equals(TiposSublinea.EXTERIOR))
                {
                    var nuevosSaldosCartera = new SaldosCarteraDTO
                    {
                        IdSolicitud = movimiento.IdSolicitud,
                        IdSubproducto = movimiento.IdSubproducto,
                        Saldo1 = movimiento.SaldoCapitalVigente,
                        Saldo2 = 0,
                        Saldo3 = 0,
                        Saldo4 = 0,
                        IdMovimiento = movimiento.IdMovimiento
                    };
                    var nuevosSaldosCarteraEntidad = this.MapearSaldosCartera(nuevosSaldosCartera);
                    await solicitudCarteraActivaRepository.GuardarSaldos(nuevosSaldosCarteraEntidad);
                    saldosCarteraSolicitud.Add(nuevosSaldosCartera);
                }
                else
                {
                    ValidarSaldosCartera(saldosCartera, movimiento.IdSolicitud);
                    //ldosCarteraSolicitud = await calculadoraSaldosService.ObtenerSaldos(movimiento.IdSolicitud);
                }
            }

            return saldosCarteraSolicitud;
        }

        private static void ValidarDesembolso(DesembolsoDTO desembolso)
        {
            if (desembolso == null)
            {
                throw new ValidationException(
                [
                    new()
                    {
                        PropertyName = nameof(desembolso.IdSolicitud),
                        ErrorMessage = $"{ValidationMessages.Desembolso} {desembolso.IdSolicitud}"
                    }
                ]);
            }
            var desembolsoValidador = new DesembolsoValidator();
            desembolsoValidador.ValidateAndThrowCustomValidationException(desembolso);
        }
    }
}
