﻿using ApiCartera.Application.Features.BeneficioAcuerdo001.Queries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Interfaces.implementacionFabrica
{
    internal class SegmentacionBeneficioAcuerdo001QueryFactory : ISegmentacionBeneficioAcuerdo001Factory
    {
        public SegmentacionBeneficioAcuerdo001Query Create()
        {
            return new SegmentacionBeneficioAcuerdo001Query();
        }
    }
}
