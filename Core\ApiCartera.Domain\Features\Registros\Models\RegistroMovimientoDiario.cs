using CsvHelper.Configuration.Attributes;

namespace ApiCartera.Domain.Features.Registros.Models
{
    public class RegistroMovimientoDiario
    {
        [Name("ID.ICETEX   ")]
        public string IdIcetex { get; set; }
        [Name("ID.SIGNATURE")]
        public string IdSignature { get; set; }
        [Name("SDO TOTAL CAPITAL VIGENTE")]
        public decimal SaldoCapitalVigente { get; set; }
        [Name("FECHA EFECTIVAMOVIMIENTO")]
        public string FechaEfectivaMovimiento { get; set; }
        [Name("FECHA POSTEO")]
        public string FechaPosteo { get; set; }
        [Name("MONTO MOVIMIENTO DE CAPITAL")]
        public decimal MontoMovimientoDeCapital { get; set; }
        [Name("CODIGO TIPO MOVTO DE CAPITAL")]
        public string CodigoTipoMovimientoDeCapital { get; set; }
        [Name("NOMBRE TIPO MOVTO DE CAPITAL")]
        public string NombreTipoMovimientoDeCapital { get; set; }
        [Name("COD. SUBPRODUCTO X OBLIGACION")]
        public string CodigoSubproductoPorObligacion { get; set; }
        [Name("SALDO TOTAL DE CAPITAL")]
        public decimal SaldoTotalCapital { get; set; }
        [Name("SALDO CAPITAL VENCIDO")]
        public decimal SaldoCapitalVencido { get; set; }
        [Name("DESCRIPCION MOVTO MEMO")]
        public string DescripcionMovimiento { get; set; }
        [Name("COD.NOVEDAD")]
        public string CodigoNovedad { get; set; }
        [Name("TIPO CARTERA")]
        public string TipoCartera { get; set; }
        [Name("CUOTAS PENDIENTES POR PAGAR")]
        public int CuotasPendientesPorPagar { get; set; }
        [Name("DESCRIPCION NOVACION")]
        public string DescripcionNovacion { get; set; }
        [Name("REFERENCIA CUS")]
        public string ReferenciaCus { get; set; }
    }
}