﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Models.BeneficioAcuerdo001;
using ApiCartera.Domain.ValueObjects;
using Microsoft.Extensions.DependencyInjection;

namespace ApiCartera.Application.Services.BeneficioAcuerdo001
{
    public class CalcularTasasCarteraService(
        ******************** serviceScopeFactory,
        IRepository<TasasSaldosCarteraEntity, string> repository,
        ITasaCarteraRepository tasaCarteraRepository,
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : ICalcularTasasCarteraService
    {
        private IRepository<TasasSaldosCarteraEntity, string> _repository = repository;
        private readonly ******************** _serviceScopeFactory = serviceScopeFactory;

        public async Task<TasasSaldosCarteraDTO> CalcularTasasYProporcionSaldos(long idSolicitud, int idSubproducto, DateTime fechaMovimiento)
        {            
            var tasasCartera = new TasasSaldosCarteraDTO();

            var solicitud = await solicitudCarteraActivaRepository.ObtenerSolicitudPorId(idSolicitud, fechaMovimiento);
            var saldosCartera = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(idSolicitud, idSubproducto);

            var tasasMinimas = new TasasSaldosCarteraDTO();
            var tasasMaximas = new TasasSaldosCarteraDTO();

            if (saldosCartera.CodigoSubproducto == TiposSubproducto.CORTO_PLAZO)
            {
                tasasMinimas = await CalcularTasasMinimasCortoPlazo(solicitud, saldosCartera);
            }

            if (saldosCartera.CodigoSubproducto == TiposSubproducto.LARGO_PLAZO 
                                    || saldosCartera.CodigoSubproducto.Equals(TiposSubproducto.LARGO_PLAZO_AMORTIZACION)
                                    || saldosCartera.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP_AMORTIZACION)
                                    || saldosCartera.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP))
            {
                tasasMinimas = await CalcularTasasMinimasLargoPlazo(solicitud, saldosCartera);
            }

            tasasMaximas = await CalcularTasasMaximas(solicitud, saldosCartera);

            tasasCartera.TasaMaximaSaldo1 = tasasMaximas.TasaMaximaSaldo1;
            tasasCartera.TasaMaximaSaldo2 = tasasMaximas.TasaMaximaSaldo2;
            tasasCartera.TasaMaximaSaldo3 = tasasMaximas.TasaMaximaSaldo3;
            tasasCartera.TasaMaximaSaldo4 = tasasMaximas.TasaMaximaSaldo4;

            tasasCartera.TasaMinimaSaldo1 = tasasMinimas.TasaMinimaSaldo1;
            tasasCartera.TasaMinimaSaldo2 = tasasMinimas.TasaMinimaSaldo2;
            tasasCartera.TasaMinimaSaldo3 = tasasMinimas.TasaMinimaSaldo3;
            tasasCartera.TasaMinimaSaldo4 = tasasMinimas.TasaMinimaSaldo4;

            var proporciones = await CalcularProporcion(tasasCartera);

            tasasCartera.ProporcionSaldo1 = proporciones.ProporcionSaldo1;
            tasasCartera.ProporcionSaldo2 = proporciones.ProporcionSaldo2;
            tasasCartera.ProporcionSaldo3 = proporciones.ProporcionSaldo3;
            tasasCartera.ProporcionSaldo4 = proporciones.ProporcionSaldo4;

            tasasCartera.IdSubproducto = idSubproducto;
            tasasCartera.IdSolicitud = idSolicitud;
            tasasCartera.CodigoSubproducto = saldosCartera.CodigoSubproducto;
            tasasCartera.IdSaldosSolicitudCartera = saldosCartera.IdSaldosSolicitudCartera!;
            tasasCartera.IdMovimiento = saldosCartera.IdMovimiento;

            TasasSaldosCarteraEntity nuevosSaldos = MapperTasasCartera(tasasCartera);
            
            await _repository.CreateAsync(nuevosSaldos, DatabaseType.Oracle);

            return tasasCartera;
        }

        public async Task<TasasSaldosCarteraDTO> CalcularProporcion(TasasSaldosCarteraDTO tasasCartera)
        {
            if (tasasCartera.TasaMinimaSaldo1 > 0 && tasasCartera.TasaMaximaSaldo1 > 0)
            {
                tasasCartera.ProporcionSaldo1 = Math.Round(((tasasCartera.TasaMaximaSaldo1 / tasasCartera.TasaMinimaSaldo1) - 1), 4);
            }

            if (tasasCartera.TasaMinimaSaldo2 > 0 && tasasCartera.TasaMaximaSaldo2 > 0)
            {
                tasasCartera.ProporcionSaldo2 = Math.Round(((tasasCartera.TasaMaximaSaldo2 / tasasCartera.TasaMinimaSaldo2) - 1), 4);
            }

            if (tasasCartera.TasaMinimaSaldo3 > 0 && tasasCartera.TasaMaximaSaldo3 > 0)
            {
                tasasCartera.ProporcionSaldo3 = Math.Round(((tasasCartera.TasaMaximaSaldo3 / tasasCartera.TasaMinimaSaldo3) - 1), 4);
            }

            if (tasasCartera.TasaMinimaSaldo4 > 0 && tasasCartera.TasaMaximaSaldo4 > 0)
            {
                tasasCartera.ProporcionSaldo4 = Math.Round(((tasasCartera.TasaMaximaSaldo4 / tasasCartera.TasaMinimaSaldo4) - 1), 4);
            }

            tasasCartera.ProporcionSaldo1 = tasasCartera.ProporcionSaldo1 < 0 ? 0 : tasasCartera.ProporcionSaldo1;
            tasasCartera.ProporcionSaldo2 = tasasCartera.ProporcionSaldo2 < 0 ? 0 : tasasCartera.ProporcionSaldo2;
            tasasCartera.ProporcionSaldo3 = tasasCartera.ProporcionSaldo3 < 0 ? 0 : tasasCartera.ProporcionSaldo3;
            tasasCartera.ProporcionSaldo4 = tasasCartera.ProporcionSaldo4 < 0 ? 0 : tasasCartera.ProporcionSaldo4;

            return tasasCartera;
        }

        public TasasSaldosCarteraEntity MapperTasasCartera(TasasSaldosCarteraDTO tasaCartera)
        {
            return new TasasSaldosCarteraEntity
            {
                IdSolicitud = tasaCartera.IdSolicitud,
                TasaMinimaSaldo1 = tasaCartera.TasaMinimaSaldo1,
                TasaMinimaSaldo2 = tasaCartera.TasaMinimaSaldo2,
                TasaMinimaSaldo3 = tasaCartera.TasaMinimaSaldo3,
                TasaMinimaSaldo4 = tasaCartera.TasaMinimaSaldo4,
                TasaMaximaSaldo1 = tasaCartera.TasaMaximaSaldo1,
                TasaMaximaSaldo2 = tasaCartera.TasaMaximaSaldo2,
                TasaMaximaSaldo3 = tasaCartera.TasaMaximaSaldo3,
                TasaMaximaSaldo4 = tasaCartera.TasaMaximaSaldo4,
                ProporcionSaldo1 = tasaCartera.ProporcionSaldo1,
                ProporcionSaldo2 = tasaCartera.ProporcionSaldo2,
                ProporcionSaldo3 = tasaCartera.ProporcionSaldo3,
                ProporcionSaldo4 = tasaCartera.ProporcionSaldo4,
                IdSubproducto = tasaCartera.IdSubproducto,
                CodigoSubproducto = tasaCartera.CodigoSubproducto,
                IdSaldosSolicitudCartera = tasaCartera.IdSaldosSolicitudCartera
            };
        }


        public async Task<TasasSaldosCarteraDTO> CalcularTasasMinimasCortoPlazo(SolicitudDTO solicitud, SaldosCarteraDTO saldos)
        {
            var tasasSaldoConTasasMinimas = new TasasSaldosCarteraDTO();

            var estaEnMora = (solicitud.DiasMora > 0);
            var Usolidaria = (solicitud.MarcaAnterior == TiposBeneficio.APORTES_IES || solicitud.Marca == TiposBeneficio.APORTES_IES);
            //var fueUSolidaria = (solicitud.MarcaAnterior == TiposBeneficio.APORTES_IES);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo1 = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo2 = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo3 = Math.Round(solicitud.IPC, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo4 = Math.Round(solicitud.TasaContratacion, 4);

            return tasasSaldoConTasasMinimas;
        }

        public async Task<TasasSaldosCarteraDTO> CalcularTasasMinimasLargoPlazo(SolicitudDTO solicitud, SaldosCarteraDTO saldos)
        {
            var tasasSaldoConTasasMinimas = new TasasSaldosCarteraDTO();

            var estaEnMora = (solicitud.DiasMora > 0);
            var Usolidaria = (solicitud.MarcaAnterior == TiposBeneficio.APORTES_IES || solicitud.Marca == TiposBeneficio.APORTES_IES);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo1 = estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo2 = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo3 = Math.Round(solicitud.IPC, 4);

            tasasSaldoConTasasMinimas.TasaMinimaSaldo4 = Math.Round(solicitud.TasaContratacion, 4);

            return tasasSaldoConTasasMinimas;
        }

        public async Task<TasasSaldosCarteraDTO> CalcularTasasMaximas(SolicitudDTO solicitud, SaldosCarteraDTO saldos)
        {
            var tasasSaldoConTasasMaximas = new TasasSaldosCarteraDTO();
            var enMora = (solicitud.DiasMora > 0);
            var Usolidaria = (solicitud.MarcaAnterior == TiposBeneficio.APORTES_IES || solicitud.Marca == TiposBeneficio.APORTES_IES);

            tasasSaldoConTasasMaximas.TasaMaximaSaldo1 = Math.Round(solicitud.TasaContratacion, 4);

            tasasSaldoConTasasMaximas.TasaMaximaSaldo2 = Usolidaria && enMora ?
                                                         Math.Round(solicitud.IPC4, 4) :
                                                         Usolidaria && !enMora ? Math.Round(solicitud.IPC2, 4) : Math.Round(solicitud.TasaContratacion, 4);

            tasasSaldoConTasasMaximas.TasaMaximaSaldo3 = Usolidaria && enMora ?
                                                         Math.Round(solicitud.IPC4, 4) :
                                                         Usolidaria && !enMora ? Math.Round(solicitud.IPC2, 4) : Math.Round(solicitud.TasaContratacion, 4);

            tasasSaldoConTasasMaximas.TasaMaximaSaldo4 = Math.Round(solicitud.TasaContratacion, 4);

            return tasasSaldoConTasasMaximas;
        }
    }
}