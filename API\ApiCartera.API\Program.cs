using BancoProyectos.API.Middleware;
using ApiCartera.API.Config;
using ApiCartera.API.Middleware;
using ApiCartera.Application.Config;
using ApiCartera.Infrastructure.Config;
using System.Text;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
// Add services to the container.
Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
var _cors = "ApiCartera-cors";
builder.Services.AddCors(options =>
{
    options.AddPolicy(_cors, builder => builder
    .AllowAnyOrigin()
    .AllowAnyMethod()
    .AllowAnyHeader()
    );
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddApiServices();
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

builder.Inject();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment() || app.Environment.IsEnvironment("Local"))
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseSerilogRequestLogging();

app.UseMiddleware<CustomResponseMiddleware>();
app.UseMiddleware<ExceptionMiddleware>();

app.UseAuthorization();
app.UseCors(_cors);

app.MapControllers();

app.Run();
