﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosTests
    {
        [Fact]
        public async Task CalcularSaldosCartera_PorFinalizacionFechaPlanDePagosSubProductoLargoPlazoAmortizacion()
        {
            var desembolsos = new List<DesembolsoDTO>();
            desembolsos.Add(new DesembolsoDTO
            {
                IdSolicitud = 123456,
                YearGiro = 2024,
                SemestreGiro = 2,
                Fecha = new DateTime(),
                TotalGirar = 1700000,
                ModalidadRubro = TiposModalidadRubro.MATRICULA,
                PorcentajeCarteraPlan = 0.73,
                Marca = TiposBeneficio.NO_APLICA_BENEFICIO,
                ValorAportesIES = 0,
                NoRelacion = 0,
                Periodicidad = 6
            });            
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 2500000,
                IdSolicitud = 123456,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 8500000,
                IdSolicitud = 123456,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
                IdSaldosSolicitudCartera = 150
            };
            var desembolsoRepositoryMock = new Mock<IDesembolsoRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            desembolsoRepositoryMock.Setup(x => x.ObtenerDesembolsosConFechaPlanPagosFinalizada())
                                    .Returns(Task.FromResult(desembolsos));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.CORTO_PLAZO))
                                       .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.LARGO_PLAZO_AMORTIZACION))
                                       .Returns(Task.FromResult(saldosCarteraLargoPlazo));

            var afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService = new AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService(
                    desembolsoRepositoryMock.Object,
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var resultadoList = await afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService.CalcularSaldosCarteraPorFinalizacionFechaPlanDePago();

            Assert.True(resultadoList.Count > 0);
            Assert.Equal(2,resultadoList[0].Saldos.Count);
            Assert.Equal(1441000, resultadoList[0].Saldos[0].Saldo1);
            Assert.Equal(1259000, resultadoList[0].Saldos[0].Saldo4);            
            Assert.Equal(659000, resultadoList[0].Saldos[1].Saldo1);
            Assert.Equal(8041000, resultadoList[0].Saldos[1].Saldo4);
        }        
        
        [Fact]
        public async Task CalcularSaldosCartera_PorFinalizacionFechaPlanDePagosSubProductoLargoPlazoEstudios()
        {
            var desembolsos = new List<DesembolsoDTO>();
            desembolsos.Add(new DesembolsoDTO
            {
                IdSolicitud = 123456,
                YearGiro = 2024,
                SemestreGiro = 2,
                Fecha = new DateTime(),
                TotalGirar = 1700000,
                ModalidadRubro = TiposModalidadRubro.MATRICULA,
                PorcentajeCarteraPlan = 0.73,
                Marca = TiposBeneficio.NO_APLICA_BENEFICIO,
                ValorAportesIES = 0,
                NoRelacion = 0,
                Periodicidad = 6
            });            
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 2500000,
                IdSolicitud = 123456,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 8500000,
                IdSolicitud = 123456,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO,
                IdSaldosSolicitudCartera = 150
            };
            var desembolsoRepositoryMock = new Mock<IDesembolsoRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            desembolsoRepositoryMock.Setup(x => x.ObtenerDesembolsosConFechaPlanPagosFinalizada())
                                    .Returns(Task.FromResult(desembolsos));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.CORTO_PLAZO))
                                       .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.LARGO_PLAZO))
                                       .Returns(Task.FromResult(saldosCarteraLargoPlazo));

            var afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService = new AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService(
                    desembolsoRepositoryMock.Object,
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var resultadoList = await afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService.CalcularSaldosCarteraPorFinalizacionFechaPlanDePago();

            Assert.True(resultadoList.Count > 0);
            Assert.Equal(2,resultadoList[0].Saldos.Count);
            Assert.Equal(1441000, resultadoList[0].Saldos[0].Saldo1);
            Assert.Equal(1259000, resultadoList[0].Saldos[0].Saldo4);            
            Assert.Equal(659000, resultadoList[0].Saldos[1].Saldo1);
            Assert.Equal(8041000, resultadoList[0].Saldos[1].Saldo4);
        }        

        [Fact]
        public async Task CalcularSaldosCartera_PorFinalizacionFechaPlanDePagosPorDeselmbolsoMayorASaldo4()
        {
            var desembolsos = new List<DesembolsoDTO>();
            desembolsos.Add(new DesembolsoDTO
            {
                IdSolicitud = 123456,
                YearGiro = 2024,
                SemestreGiro = 2,
                Fecha = new DateTime(),
                TotalGirar = 2700000,
                ModalidadRubro = TiposModalidadRubro.MATRICULA,
                PorcentajeCarteraPlan = 0.73,
                Marca = TiposBeneficio.NO_APLICA_BENEFICIO,
                ValorAportesIES = 0,
                NoRelacion = 0,
                Periodicidad = 6
            });            
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 500000,
                IdSolicitud = 123456,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 500000,
                IdSolicitud = 123456,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
                IdSaldosSolicitudCartera = 150
            };
            var desembolsoRepositoryMock = new Mock<IDesembolsoRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            desembolsoRepositoryMock.Setup(x => x.ObtenerDesembolsosConFechaPlanPagosFinalizada())
                                    .Returns(Task.FromResult(desembolsos));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.CORTO_PLAZO))
                                       .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.LARGO_PLAZO_AMORTIZACION))
                                       .Returns(Task.FromResult(saldosCarteraLargoPlazo));

            var afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService = new AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService(
                    desembolsoRepositoryMock.Object,
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var resultadoList = await afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService.CalcularSaldosCarteraPorFinalizacionFechaPlanDePago();

            Assert.True(resultadoList.Count > 0);
            Assert.Equal(2,resultadoList[0].Saldos.Count);
            Assert.Equal(2171000, resultadoList[0].Saldos[0].Saldo1);
            Assert.Equal(0, resultadoList[0].Saldos[0].Saldo4);            
            Assert.Equal(929000, resultadoList[0].Saldos[1].Saldo1);
            Assert.Equal(0, resultadoList[0].Saldos[1].Saldo4);
        }

        [Fact]
        public async Task CalcularSaldosCartera_PorFinalizacionFechaPlanDePagosPorDeselmbolsoConMarcaDiferenteASinBeneficio()
        {
            var desembolsos = new List<DesembolsoDTO>();
            desembolsos.Add(new DesembolsoDTO
            {
                IdSolicitud = 123456,
                YearGiro = 2024,
                SemestreGiro = 2,
                Fecha = new DateTime(),
                TotalGirar = 2700000,
                ModalidadRubro = TiposModalidadRubro.MATRICULA,
                PorcentajeCarteraPlan = 0.73,
                Marca = TiposBeneficio.APORTES_IES,
                ValorAportesIES = 0,
                NoRelacion = 0,
                Periodicidad = 6
            });            
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 500000,
                IdSolicitud = 123456,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 500000,
                IdSolicitud = 123456,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
                IdSaldosSolicitudCartera = 150
            };
            var desembolsoRepositoryMock = new Mock<IDesembolsoRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            desembolsoRepositoryMock.Setup(x => x.ObtenerDesembolsosConFechaPlanPagosFinalizada())
                                    .Returns(Task.FromResult(desembolsos));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.CORTO_PLAZO))
                                       .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(It.IsAny<long>(), TiposSubproducto.LARGO_PLAZO_AMORTIZACION))
                                       .Returns(Task.FromResult(saldosCarteraLargoPlazo));

            var afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService = new AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService(
                    desembolsoRepositoryMock.Object,
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var resultadoList = await afectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService.CalcularSaldosCarteraPorFinalizacionFechaPlanDePago();

            Assert.Empty(resultadoList[0].Saldos);
        }
    }
}
