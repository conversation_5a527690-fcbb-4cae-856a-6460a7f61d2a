using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Queries.Departamentos.ObtenerDepartamentos;
using Sarlaft.Application.Features.Parametricas.ViewModels;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Test.Application.Config;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Departamentos
{
    public class ObtenerDepartamentosTest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var mockRepository = new Mock<IGenericRepository<Departamento>>();

            var mapper = MapperConfig.Build();

            var handler = new ObtenerDepartamentosQueryHandler(mockRepository.Object, mapper);
            var query = new ObtenerDepartamentosQuery();
            var DepartamentoesInsertar = new List<Departamento>
            {
                new Departamento
                {
                    Nombre = "Bucaramanga",
                    CodigoDANE = "03",
                },
                new Departamento
                {
                    Nombre = "Piedecuesta",
                    CodigoDANE = "03",
                },
            };


            var DepartamentoesVm = mapper.Map<List<DepartamentoVm>>(DepartamentoesInsertar);

            // Configura el comportamiento esperado para el repositorio y el mapeador
            mockRepository.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(DepartamentoesInsertar);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(result.Count(), DepartamentoesVm.Count());
            Assert.All(result, Departamento => DepartamentoesVm.Any(DepartamentoVm => DepartamentoVm.Nombre == Departamento.Nombre));
            Assert.All(result, Departamento => DepartamentoesInsertar.Any(DepartamentoVm => DepartamentoVm.Nombre == Departamento.Nombre));
        }
    }
}