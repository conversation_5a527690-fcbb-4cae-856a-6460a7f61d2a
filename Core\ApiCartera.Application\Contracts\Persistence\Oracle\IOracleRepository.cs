﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Registros.DTOs;

namespace ApiCartera.Application.Contracts.Persistence.Oracle
{
    public interface IOracleRepository
    {
        Task<ResultadoEjecucion<long>> InsertarSaldosMasivamente(string connectionString, List<SaldosSolicitudCartera> registros);
        Task<ResultadoEjecucion<MovimientosDiariosDTO>> InsertarMovimientosMasivamente(string connectionString, List<MovimientosDiariosDTO> registros);
        Task<List<long>> ObtenerSolicitudesParaDivisionDeSaldos(string oracleConnectionString);

    }
}
