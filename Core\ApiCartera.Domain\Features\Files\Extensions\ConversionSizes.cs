﻿using ApiCartera.Domain.Features.Files.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Domain.Features.Files.Constants
{
    public static class ConversionSizes
    {
        public const int BaseScale = 1024;
        public static double ConvertFromTo(this double value, SizesScale from,  SizesScale to)
        {
            var differenceScale = ValuesScale[from]- ValuesScale[to];
            return value*Math.Pow(BaseScale, differenceScale);
        }
        public static double ConvertBytesTo(this double value,  SizesScale to)
        {
            return ConvertFromTo(value, SizesScale.Byte, to);
        }

        private static Dictionary<SizesScale, int> ValuesScale = new Dictionary<SizesScale, int>()
        {
            { SizesScale.Byte, 1},
            { SizesScale.KiloByte, 2},
            { SizesScale.MegaByte, 3},
            { SizesScale.GigaByte, 4},
        };
    }
}
