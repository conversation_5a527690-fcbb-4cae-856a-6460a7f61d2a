﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Reportes.Services;

namespace ApiCartera.Application.Services.Reportes
{
    public class ReporteAportesIESUniversidadesService(IReporteAportesIESUniversidadesRepository reporteAportesIESUniversidadesRepository) : IReporteAportesIESUniversidadesService
    {
        public async Task<List<AportesIESUniversidadesDTO>> ObtenerReporte(DateTime desde, DateTime hasta, int page)
        {         
            int pageSize = 100;
            List<AportesIESUniversidadesDTO>? reporte = await reporteAportesIESUniversidadesRepository.ObtenerReporte(desde, hasta, pageSize, page);            
            return reporte;
        }
    }
}
