﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.DivisionSaldos;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using Bogus;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.DivisionSaldos
{
    public class CalculadoraSaldosTests
    {
        /*private const int PORCENTAJE_TOTAL_CARTERA_PLAN = 1;
        private readonly List<SaldosSolicitudCarteraDTO> saldosSolicitudesCartera =
        [
            new SaldosSolicitudCarteraDTO
            {
                Id = 1,
                TipoCartera = "ESTUDIOS",
                IdSolicitud = 6272872,
                IdSignature = 627287211,
                IdSolicitante = 1,
                SaldoCapitalVigente = 544892.52,
                CodigoSubproducto = "10001",
                SaldoTotalCapital = 1000000,
                SaldoCapitalVencido = 455107.48,
                CuotasPendientesPorPagar = 0,
                PorcentajeCarteraPlan = 0.3,
                Periodicidad = 6
            },
            new SaldosSolicitudCarteraDTO
            {
                Id = 2,
                TipoCartera = "ESTUDIOS",
                IdSolicitud = 6272872,
                IdSignature = 627287212,
                IdSolicitante = 1,
                SaldoCapitalVigente = 36718781.67,
                CodigoSubproducto = "10002",
                SaldoTotalCapital = 100000000,
                SaldoCapitalVencido = 63281218.33,
                CuotasPendientesPorPagar = 0,
                PorcentajeCarteraPlan = 0.3,
                Periodicidad = 6
            }
        ];
        private readonly List<DesembolsoDTO> desembolsos =
        [
            new DesembolsoDTO 
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                TotalGirar = 5187300,
                Marca = "CIES - CONTRIBUCIÓN IES",
                Fecha = DateTime.Parse("2023-02-23"),
                ValorAportesIES = null,
                Periodicidad = 6
            },
            new DesembolsoDTO
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 2,
                TotalGirar = 5187300,
                Marca = "CIES - CONTRIBUCIÓN IES",
                Fecha = DateTime.Parse("2023-06-18"),
                ValorAportesIES = null,
                Periodicidad = 6
            }
        ];
        private readonly Mock<ISolicitudCarteraActivaRepository> _solicitudCarteraActivaRepositoryMock;
        private readonly CalculadoraSaldos _calculadoraSaldosMock;

        public CalculadoraSaldosTests()
        {
            _solicitudCarteraActivaRepositoryMock = new();
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerPorIdSolicitud(It.IsAny<long>()))
                .Returns(Task.FromResult(saldosSolicitudesCartera));
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsosPorIdSolicitud(6272872))
                .Returns(Task.FromResult(desembolsos));
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(It.IsAny<SaldosCartera>()));
            _calculadoraSaldosMock = new(_solicitudCarteraActivaRepositoryMock.Object);
        }

        [Theory]
        [InlineData(6272872)]
        public async Task ObtenerSaldos_RecibeIdSolicitudExistente_RetornaDivisionSaldos(long idSolicitud)
        {
            var saldosCarteras = await _calculadoraSaldosMock.ObtenerSaldos(idSolicitud);

            Assert.NotNull(saldosCarteras);
            Assert.Equal(2, saldosCarteras.Count);

            var saldosCarteraCortoPlazo = saldosCarteras[0];
            var saldosCarteraLargoPlazo = saldosCarteras[1];
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo1);
            Assert.Equal(544892.52, saldosCarteraCortoPlazo.Saldo2);
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo3);
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo4);
            Assert.Equal(29456561.67, saldosCarteraLargoPlazo.Saldo1);
            Assert.Equal(7262220, saldosCarteraLargoPlazo.Saldo2);
            Assert.Equal(0, saldosCarteraLargoPlazo.Saldo3);
            Assert.Equal(0, saldosCarteraLargoPlazo.Saldo4);
        }

        [Theory]
        [InlineData(6272872)]
        public async Task ObtenerSaldos_RecibeDesembolsoSinBeneficioFechaPlanPagosFinalizada_RetornaDivisionSaldos(long idSolicitud)
        {
            var fakeSaldosSolicitudCartera = new Faker<SaldosSolicitudCarteraDTO>()
                .RuleFor(s => s.IdSolicitud, idSolicitud)
                .RuleFor(s => s.CodigoSubproducto, TiposSubproducto.LARGO_PLAZO)
                .RuleFor(s => s.TipoCartera, TiposCartera.ESTUDIOS)
                .RuleFor(s => s.PorcentajeCarteraPlan, 0.3)
                .RuleFor(s => s.Periodicidad, 6)
                .RuleFor(s => s.SaldoCapitalVigente, f => f.Random.Double(5000000, 10000000));
            var saldosSolicitudCartera = fakeSaldosSolicitudCartera.Generate(1);
            var fakeDesembolso = new Faker<DesembolsoDTO>()
                .RuleFor(d => d.IdSolicitud, idSolicitud)
                .RuleFor(d => d.TotalGirar, f => f.Random.Double(1000000, 4000000))
                .RuleFor(d => d.Marca, TiposBeneficio.NO_APLICA_BENEFICIO)
                .RuleFor(d => d.YearGiro, 2024)
                .RuleFor(d => d.SemestreGiro, f => f.Random.Int(1, 2))
                .RuleFor(d => d.Periodicidad, 6)
                .RuleFor(d => d.Fecha, DateTime.Parse("2023-07-02"));
            var desembolsos = fakeDesembolso.Generate(2);

            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerPorIdSolicitud(idSolicitud))
                .Returns(Task.FromResult(saldosSolicitudCartera));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
                .Returns(Task.FromResult(desembolsos));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(It.IsAny<SaldosCartera>()));
            var calculadoraSaldosMock = new CalculadoraSaldos(solicitudCarteraActivaRepositoryMock.Object);

            var saldo1 = saldosSolicitudCartera[0].SaldoCapitalVigente;

            var saldosCartera = await calculadoraSaldosMock.ObtenerSaldos(idSolicitud);
            var saldosCarteraCortoPlazo = saldosCartera[0];

            Assert.NotNull(saldosCartera);
            Assert.Single(saldosCartera);
            Assert.Equal(Math.Truncate(saldo1), Math.Truncate(saldosCarteraCortoPlazo.Saldo1));
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo2);
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo3);
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo4);
        }

        [Theory]
        [InlineData(6272872)]
        public async Task ObtenerSaldos_RecibeDesembolsoSinBeneficioSinFechaPlanPagosFinalizada_RetornaDivisionSaldos(long idSolicitud)
        {
            var fakeSaldosSolicitudCartera = new Faker<SaldosSolicitudCarteraDTO>()
                .RuleFor(s => s.IdSolicitud, idSolicitud)
                .RuleFor(s => s.CodigoSubproducto, TiposSubproducto.LARGO_PLAZO)
                .RuleFor(s => s.TipoCartera, TiposCartera.ESTUDIOS)
                .RuleFor(s => s.PorcentajeCarteraPlan, 0.3)
                .RuleFor(s => s.Periodicidad, 6)
                .RuleFor(s => s.SaldoCapitalVigente, f => f.Random.Double(5000000, 10000000));
            var saldosSolicitudCartera = fakeSaldosSolicitudCartera.Generate(1);
            var fakeDesembolso = new Faker<DesembolsoDTO>()
                .RuleFor(d => d.IdSolicitud, idSolicitud)
                .RuleFor(d => d.TotalGirar, f => f.Random.Double(1000000, 4000000))
                .RuleFor(d => d.Marca, TiposBeneficio.NO_APLICA_BENEFICIO)
                .RuleFor(d => d.YearGiro, 2024)
                .RuleFor(d => d.SemestreGiro, f => f.Random.Int(1, 2))
                .RuleFor(d => d.Periodicidad, 6)
                .RuleFor(d => d.Fecha, DateTime.Now);
            var desembolsos = fakeDesembolso.Generate(2);

            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerPorIdSolicitud(idSolicitud))
                .Returns(Task.FromResult(saldosSolicitudCartera));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsosPorIdSolicitud(idSolicitud))
                .Returns(Task.FromResult(desembolsos));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(It.IsAny<SaldosCartera>()));
            var calculadoraSaldosMock = new CalculadoraSaldos(solicitudCarteraActivaRepositoryMock.Object);

            var saldo4 = desembolsos.Sum(d => d.TotalGirar * (PORCENTAJE_TOTAL_CARTERA_PLAN - saldosSolicitudCartera[0].PorcentajeCarteraPlan));
            var saldo1 = saldosSolicitudCartera[0].SaldoCapitalVigente - saldo4;
            saldo1 = saldo1 < 0 ? 0 : saldo1;

            var saldosCartera = await calculadoraSaldosMock.ObtenerSaldos(idSolicitud);
            var saldosCarteraCortoPlazo = saldosCartera[0];

            Assert.NotNull(saldosCartera);
            Assert.Single(saldosCartera);
            Assert.Equal(Math.Truncate(saldo1), Math.Truncate(saldosCarteraCortoPlazo.Saldo1));
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo2);
            Assert.Equal(0, saldosCarteraCortoPlazo.Saldo3);
            Assert.Equal(Math.Truncate(saldo4), Math.Truncate(saldosCarteraCortoPlazo.Saldo4));
        }*/
    }
}
