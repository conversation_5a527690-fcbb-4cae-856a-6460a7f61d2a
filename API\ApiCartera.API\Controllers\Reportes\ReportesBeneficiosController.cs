﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Reportes;

[Route("api/[controller]")]
[ApiController]
public class ReportesBeneficiosController(IMediator _mediator) : ControllerBase
{

    [HttpGet("ReporteBeneficioAcuerdo001")]
    [ProducesResponseType<ResponseWrapper<List<BeneficioAcuerdo001Vm>>>(StatusCodes.Status200OK)]
    public async Task<List<BeneficioAcuerdo001Vm>> ReporteBeneficioAcuerdo001([FromQuery] BeneficioAcuerdo001Query request) =>
        await _mediator.Send(request);

    [HttpGet("ReporteContribucionesAportesIES")]
    [ProducesResponseType<ResponseWrapper<List<ContribucionesAportesIESVm>>>(StatusCodes.Status200OK)]
    public async Task<List<ContribucionesAportesIESVm>> ReporteContribucionesAportesIES([FromQuery] ContribucionesAportesIESQuery request) =>
        await _mediator.Send(request);

    [HttpGet("ReporteAportesIESUniversidades")]
    [ProducesResponseType<ResponseWrapper<List<AportesIESUniversidadesVm>>>(StatusCodes.Status200OK)]
    public async Task<List<AportesIESUniversidadesVm>> ReporteAportesIESUniversidades([FromQuery] AportesIESUniversidadesQuery request) =>
        await _mediator.Send(request);

    [HttpGet("ReporteAlertas")]
    [ProducesResponseType<ResponseWrapper<IEnumerable<ReporteAlertasVm>>>(StatusCodes.Status200OK)]
    public async Task<IEnumerable<ReporteAlertasVm>> ReporteAlertas([FromQuery] ReporteAlertasQuery request) =>
        await _mediator.Send(request);

    [HttpGet("ReporteValidacionDatosSolicitud")]
    [ProducesResponseType<ResponseWrapper<List<ValidacionDatosSolicitudVm>>>(StatusCodes.Status200OK)]
    public async Task<List<ValidacionDatosSolicitudVm>> ReporteValidacionDatosSolicitud([FromQuery] ValidacionDatosSolicitudQuery request) =>
        await _mediator.Send(request);
}
