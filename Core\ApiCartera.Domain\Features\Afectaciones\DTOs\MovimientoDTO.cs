﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Afectaciones.DTOs;

public class MovimientoDTO
{

    [JsonProperty("idMovimiento")]
    public int IdMovimiento { get; set; }
    [JsonProperty("fechaMovimiento")]
    public DateTime FechaMovimiento { get; set; }
    [JsonProperty("valorGiro")]
    public double ValorGiro { get; set; }
    [JsonProperty("descripcion")]
    public string Descripcion { get; set; }
    [JsonProperty("idTipoNovedad")]
    public int IdTipoNovedad { get; set; }
    [JsonProperty("codigoNovedad")]
    public string CodigoNovedad { get; set; }
    [JsonProperty("year")]
    public int Year { get; set; }
    [JsonProperty("semestre")]
    public int Semestre { get; set; }
    [JsonProperty("idSolicitud")]
    public long IdSolicitud { get; set; }
    [JsonProperty("idSubproducto")]
    public int IdSubproducto { get; set; }
    [JsonProperty("codigoSubproducto")]
    public string CodigoSubproducto { get; set; }
    [JsonProperty("saldoCapitalVigente")]
    public double SaldoCapitalVigente { get; set; }
    [JsonProperty("saldoTotalCapital")]
    public double SaldoTotalCapital { get; set; }
    [JsonProperty("saldoCapitalVencido")]
    public double SaldoCapitalVencido { get; set; }
    [JsonProperty("noRelacion")]
    public string? NoRelacion { get; set; }
    [JsonProperty("codigoTransaccion")]
    public int? CodigoTransaccion { get; set; }
    [JsonProperty("diasSinMovimiento")]
    public int? DiasSinMovimiento { get; set; }
}
