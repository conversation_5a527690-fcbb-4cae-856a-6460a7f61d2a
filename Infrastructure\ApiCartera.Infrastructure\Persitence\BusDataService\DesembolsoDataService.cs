﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using Microsoft.Extensions.Logging;


namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class DesembolsoDataService(BusService busService, IConfiguration configuration, ILogger<DesembolsoDataService> logger ) :
        GenericCustomRepositoryBusDataService<SaldosCartera>(busService, configuration), IDesembolsoRepository
    {
        public async Task<List<DesembolsoDTO>> ObtenerDesembolsosConFechaPlanPagosFinalizada(int pageNo = 1, int pageSize = 100)
        {
            var url = $"obtenerdesembolsosconfechaplanpagosfinalizada?pageNo={pageNo}&pageSize={pageSize}";
            RespuestaDataServiceDTO<List<DesembolsoDTO>> respuestaDesembolsos = null;

            try
            {
                
                respuestaDesembolsos = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<DesembolsoDTO>>>(url);

                
                if (respuestaDesembolsos.Response != TiposRespuestaDataService.EXITOSA)
                {
                    
                    logger.LogWarning("El servicio en {Url} respondió con un estado interno no exitoso: {Response}", url, respuestaDesembolsos.Response);
                    var validaciones = new List<ValidationFailure>()
                    {
                        new() { ErrorMessage = respuestaDesembolsos.Response }
                    };
                    
                    throw new ValidationException(validaciones); 
                }

                
                return respuestaDesembolsos.Data ?? new List<DesembolsoDTO>(); 

            }
            catch (HttpRequestException httpEx)
            {
                
                logger.LogError(httpEx, "Error en la solicitud HTTP a {Url}. Status Code: {StatusCode}", url, httpEx.StatusCode);
                
                
                throw; 
                
            }
            catch (Exception ex)
            {
                
                logger.LogError(ex, "Error inesperado al obtener desembolsos de {Url}", url);
                throw; 
            }                               
        }

        public async Task<List<DesembolsoDTO>> ObtenerTodosLosDesembolsosConFechaPlanPagosFinalizada()
        {
            var todosLosDesembolsos = new List<DesembolsoDTO>();
            int pageNo = 1;
            const int pageSize = 100;
            List<DesembolsoDTO> desembolsosPagina;
            bool hayMasPaginas = true;

            while (hayMasPaginas)
            {
                try
                {
                    
                    desembolsosPagina = await ObtenerDesembolsosConFechaPlanPagosFinalizada(pageNo, pageSize);

                    
                    if (desembolsosPagina == null) {
                        logger.LogWarning("Se detuvo la obtención de páginas debido a un error en la página {PageNo}", pageNo);
                        
                        hayMasPaginas = false; 
                        
                        continue;
                    }

                    if (desembolsosPagina.Any())
                    {
                        todosLosDesembolsos.AddRange(desembolsosPagina);
                    }

                    
                    if (desembolsosPagina.Count < pageSize)
                    {
                        hayMasPaginas = false;
                    }
                    else
                    {
                        pageNo++; 
                    }
                }
                catch (Exception ex) 
                {
                    logger.LogError(ex, "Error procesando la página {PageNo} de desembolsos. Se detendrá la obtención.", pageNo);
                   
                    hayMasPaginas = false; 
                }
            }
            
            return todosLosDesembolsos;
        }
    }
}
