﻿using ApiCartera.Application.Features.Movimientos.Queries;
using ApiCartera.Application.Features.Movimientos.ViewModels;
using ApiCartera.Domain.Features.Movimientos.DTOs;
using AutoMapper;

namespace ApiCartera.Application.Features.Movimientos.Mappings
{
    public class MovimientosSolicitudMapping: Profile
    {
        public MovimientosSolicitudMapping()
        {
            CreateMap<ConsultaMovimientoDTO, ConsultaMovimientoVm>();
            CreateMap<ObtenerMovimientosCreditosQuery, ParametrosConsultaMovimientoSolicitudDTO>();
        }
    }
}
