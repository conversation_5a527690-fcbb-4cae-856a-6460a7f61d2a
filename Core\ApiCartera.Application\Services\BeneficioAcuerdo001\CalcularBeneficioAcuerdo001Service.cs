﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Application.Shared.ExtensionMethods;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Entities;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.Shared.DTOs;
using ApiCartera.Domain.Features.Shared.Entities;
using ApiCartera.Domain.Features.Shared.Services;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.BeneficioAcuerdo001;

public class CalcularBeneficioAcuerdo001Service(
    ITasaCarteraRepository tasaCarteraRepository,
    ISaldosCarteraRepository saldosCarteraRepository,
    ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
    IInteresesLiquidadosRepository interesesLiquidadosRepository,
    IMovimientoRepository movimientoRepository) : InteresesLiquidadosService, ICalcularBeneficioAcuerdo001Service
{    
    public async Task<InteresesLiquidadosDTO> CalcularBeneficio(CalculoBaneficioAC001DTO movimiento)
    {
        InteresesLiquidadosDTO? interesesLiquidadosDTO = new InteresesLiquidadosDTO();

        bool primerDia = !movimiento.FechaMovimiento.Day.Equals(Constants.PRIMER_DIA_MES);
        bool tipoCarteraEstudios = movimiento.IdTipoCartera.Equals(Constants.IDTIPOCARTERA_ESTUDIOS);
        bool tipoCarteraAmortizacion = movimiento.IdTipoCartera.Equals(Constants.IDTIPOCARTERA_AMORTIZACION);
        SolicitudDTO? solicitud = await solicitudCarteraActivaRepository.ObtenerSolicitudPorId(movimiento.IdSolicitud, movimiento.FechaMovimiento);
        TasasYProporcionesDTO tasasCartera = await CalcularTasasYProporciones(solicitud, movimiento);
        SaldosCarteraDTO? saldosCarteraCP = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, Constants.IDSUBPRODUCTO_CP);
        SaldosCarteraDTO? saldosCarteraLP = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, Constants.IDSUBPRODUCTO_LP);
        CalculoBaneficioAC001DTO? ultimoMovimiento = await movimientoRepository.ObtenerUltimo(movimiento.IdSolicitud, movimiento.FechaMovimiento);

        ValidarFechas(movimiento, ultimoMovimiento);

        int diasSinMovimiento = (movimiento.FechaMovimiento - ultimoMovimiento.FechaMovimiento).Days;
        interesesLiquidadosDTO.DiasCalculo = diasSinMovimiento;

        if (primerDia && tipoCarteraEstudios)
        {
            CalcularBeneficioAcuerdo001Estudios(interesesLiquidadosDTO, solicitud, tasasCartera, saldosCarteraCP, saldosCarteraLP, diasSinMovimiento);
            CalcularAjusteCies(interesesLiquidadosDTO, solicitud, tasasCartera, saldosCarteraLP, diasSinMovimiento);
            CalcularAjusteAies(interesesLiquidadosDTO, solicitud, tasasCartera, saldosCarteraLP, diasSinMovimiento);
        }
        else if (primerDia && tipoCarteraAmortizacion)
        {
            ValidarCuotas(movimiento, solicitud);

            double intereses = tasasCartera.TasaMinimaSaldo1LP.CalcularIntEntre(solicitud.NumTotalCuotas, movimiento.CapitalVigente, movimiento.CuotasPendientes);
            interesesLiquidadosDTO.BeneficioAcuerdo001Saldo1 = intereses * tasasCartera.ProporcionSaldo1LP * diasSinMovimiento;
        }

        AsignarValoresInteresesLiquidadosDTO(movimiento, interesesLiquidadosDTO, solicitud, tasasCartera);

        InteresesLiquidados? interesesLiquidados = MapperInteresesLiquidados(interesesLiquidadosDTO);

        await interesesLiquidadosRepository.Guardar(interesesLiquidados);

        return interesesLiquidadosDTO;
    }

    public void AsignarValoresInteresesLiquidadosDTO(CalculoBaneficioAC001DTO movimiento, InteresesLiquidadosDTO interesesLiquidadosDTO, SolicitudDTO solicitud, TasasYProporcionesDTO tasasCartera)
    {
        int ipcBeneficioPuntos;
        double ipcBeneficioPuntosNominal;
        double limiteAjuste;

        CalcularLimiteAjuste(solicitud, out ipcBeneficioPuntos, out ipcBeneficioPuntosNominal, out limiteAjuste);

        interesesLiquidadosDTO.IdMovimiento = movimiento.IdMovimiento;
        interesesLiquidadosDTO.ValorTasaContratacion = solicitud.TasaContratacion;
        interesesLiquidadosDTO.IpcContratacion = string.Format(Resource1.IpcTipo, solicitud.Puntos);
        interesesLiquidadosDTO.ValorTasaBeneficio = ipcBeneficioPuntosNominal;
        interesesLiquidadosDTO.IpcBeneficio = string.Format(Resource1.IpcTipo, ipcBeneficioPuntos);
        interesesLiquidadosDTO.ValorLimiteAjuste = limiteAjuste;
        interesesLiquidadosDTO.ValorAjusteAcumulado = solicitud.BeneficioAcumulado + interesesLiquidadosDTO.AjusteCIESSaldo2 + interesesLiquidadosDTO.AjusteAportesIESSaldo3;
    }

    public void CalcularLimiteAjuste(SolicitudDTO solicitud, out int ipcBeneficioPuntos, out double ipcBeneficioPuntosNominal, out double limiteAjuste)
    {
        double sumaFactores, porcentajeNominalAcuerdo;
        ipcBeneficioPuntos = solicitud.DiasMora > 0 ? Constants.TASA_IPC_CON_MORA : Constants.TASA_IPC_SIN_MORA;
        double puntosTasaContratacionNominal = Math.Round(Constants.MESES * (Math.Pow((1.0 + (double)solicitud.Puntos / 100), (1.0 / Constants.MESES)) - 1.0) * 100, 4);
        ipcBeneficioPuntosNominal = Math.Round(Constants.MESES * (Math.Pow((1.0 + (double)ipcBeneficioPuntos / 100), (1.0 / Constants.MESES)) - 1.0) * 100, 4);
        sumaFactores = solicitud.SumaFactor2 + solicitud.SumaFactor3;
        porcentajeNominalAcuerdo = Math.Round(ipcBeneficioPuntosNominal / puntosTasaContratacionNominal, 4);
        limiteAjuste = sumaFactores * porcentajeNominalAcuerdo;
    }

    public async Task<TasasYProporcionesDTO> CalcularTasasYProporciones(SolicitudDTO solicitud, CalculoBaneficioAC001DTO movimiento)
    {
        TasasYProporcionesDTO tasasYProporciones = new();
        tasasYProporciones = new ()
        {
            IdMovimiento = movimiento.IdMovimiento,
            IdSolicitud = movimiento.IdSolicitud,
            IdSubproducto = movimiento.IdSubproducto
        };

        bool estaEnMora = (solicitud.DiasMora > 0);
        bool Usolidaria = (solicitud.MarcaAnterior == TiposBeneficio.APORTES_IES || solicitud.Marca == TiposBeneficio.APORTES_IES);

        TasasMinimasCP(solicitud, tasasYProporciones, estaEnMora, Usolidaria);
        TasasMinimasLP(solicitud, tasasYProporciones, estaEnMora, Usolidaria);
        TasasMaximas(solicitud, tasasYProporciones, estaEnMora, Usolidaria);
        ProporcionesCP(solicitud, tasasYProporciones, estaEnMora, Usolidaria);
        ProporcionesLP(solicitud, tasasYProporciones, estaEnMora, Usolidaria);

        await GuardarTasas(tasasYProporciones);

        return tasasYProporciones;
    }

    public async Task GuardarTasas(TasasYProporcionesDTO tasasCartera)
    {
        TasasSaldosCartera nuevosSaldos = MapperTasasCartera(tasasCartera);
        await tasaCarteraRepository.GuardarTasas(nuevosSaldos);
    }

    public TasasSaldosCartera MapperTasasCartera(TasasYProporcionesDTO tasaCartera)
    {
        return new TasasSaldosCartera
        {
            IdSolicitud = tasaCartera.IdSolicitud,
            TasaMinimaSaldo1 = tasaCartera.TasaMinimaSaldo1LP,
            TasaMinimaSaldo2 = tasaCartera.TasaMinimaSaldo2LP,
            TasaMinimaSaldo3 = tasaCartera.TasaMinimaSaldo3LP,
            TasaMinimaSaldo4 = tasaCartera.TasaMinimaSaldo4LP,
            TasaMaximaSaldo1 = tasaCartera.TasaMaximaSaldo1,
            TasaMaximaSaldo2 = tasaCartera.TasaMaximaSaldo2,
            TasaMaximaSaldo3 = tasaCartera.TasaMaximaSaldo3,
            TasaMaximaSaldo4 = tasaCartera.TasaMaximaSaldo4,
            ProporcionSaldo1 = tasaCartera.ProporcionSaldo1LP,
            ProporcionSaldo2 = tasaCartera.ProporcionSaldo2LP,
            ProporcionSaldo3 = tasaCartera.ProporcionSaldo3LP,
            ProporcionSaldo4 = tasaCartera.ProporcionSaldo4LP,
            IdSubproducto = tasaCartera.IdSubproducto,
            IdMovimiento = tasaCartera.IdMovimiento
        };
    }

    public void ProporcionesCP(SolicitudDTO solicitud, TasasYProporcionesDTO tasasYProporciones, bool estaEnMora, bool Usolidaria)
    {
        if (tasasYProporciones.TasaMinimaSaldo1CP > 0 && tasasYProporciones.TasaMaximaSaldo1 > 0)
        {
            tasasYProporciones.ProporcionSaldo1CP = Math.Round(((tasasYProporciones.TasaMaximaSaldo1 / tasasYProporciones.TasaMinimaSaldo1CP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo2CP > 0 && tasasYProporciones.TasaMaximaSaldo2 > 0)
        {
            tasasYProporciones.ProporcionSaldo2CP = Math.Round(((tasasYProporciones.TasaMaximaSaldo2 / tasasYProporciones.TasaMinimaSaldo2CP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo3CP > 0 && tasasYProporciones.TasaMaximaSaldo3 > 0)
        {
            tasasYProporciones.ProporcionSaldo3CP = Math.Round(((tasasYProporciones.TasaMaximaSaldo3 / tasasYProporciones.TasaMinimaSaldo3CP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo4CP > 0 && tasasYProporciones.TasaMaximaSaldo4 > 0)
        {
            tasasYProporciones.ProporcionSaldo4CP = Math.Round(((tasasYProporciones.TasaMaximaSaldo4 / tasasYProporciones.TasaMinimaSaldo4CP) - 1), 4);
        }

        tasasYProporciones.ProporcionSaldo1CP = tasasYProporciones.ProporcionSaldo1CP < 0 ? 0 : tasasYProporciones.ProporcionSaldo1CP;
        tasasYProporciones.ProporcionSaldo2CP = tasasYProporciones.ProporcionSaldo2CP < 0 ? 0 : tasasYProporciones.ProporcionSaldo2CP;
        tasasYProporciones.ProporcionSaldo3CP = tasasYProporciones.ProporcionSaldo3CP < 0 ? 0 : tasasYProporciones.ProporcionSaldo3CP;
        tasasYProporciones.ProporcionSaldo4CP = tasasYProporciones.ProporcionSaldo4CP < 0 ? 0 : tasasYProporciones.ProporcionSaldo4CP;
    }

    public void ProporcionesLP(SolicitudDTO solicitud, TasasYProporcionesDTO tasasYProporciones, bool estaEnMora, bool Usolidaria)
    {
        if (tasasYProporciones.TasaMinimaSaldo1LP > 0 && tasasYProporciones.TasaMaximaSaldo1 > 0)
        {
            tasasYProporciones.ProporcionSaldo1LP = Math.Round(((tasasYProporciones.TasaMaximaSaldo1 / tasasYProporciones.TasaMinimaSaldo1LP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo2LP > 0 && tasasYProporciones.TasaMaximaSaldo2 > 0)
        {
            tasasYProporciones.ProporcionSaldo2LP = Math.Round(((tasasYProporciones.TasaMaximaSaldo2 / tasasYProporciones.TasaMinimaSaldo2LP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo3LP > 0 && tasasYProporciones.TasaMaximaSaldo3 > 0)
        {
            tasasYProporciones.ProporcionSaldo3LP = Math.Round(((tasasYProporciones.TasaMaximaSaldo3 / tasasYProporciones.TasaMinimaSaldo3LP) - 1), 4);
        }

        if (tasasYProporciones.TasaMinimaSaldo4LP > 0 && tasasYProporciones.TasaMaximaSaldo4 > 0)
        {
            tasasYProporciones.ProporcionSaldo4LP = Math.Round(((tasasYProporciones.TasaMaximaSaldo4 / tasasYProporciones.TasaMinimaSaldo4LP) - 1), 4);
        }

        tasasYProporciones.ProporcionSaldo1LP = tasasYProporciones.ProporcionSaldo1LP < 0 ? 0 : tasasYProporciones.ProporcionSaldo1LP;
        tasasYProporciones.ProporcionSaldo2LP = tasasYProporciones.ProporcionSaldo2LP < 0 ? 0 : tasasYProporciones.ProporcionSaldo2LP;
        tasasYProporciones.ProporcionSaldo3LP = tasasYProporciones.ProporcionSaldo3LP < 0 ? 0 : tasasYProporciones.ProporcionSaldo3LP;
        tasasYProporciones.ProporcionSaldo4LP = tasasYProporciones.ProporcionSaldo4LP < 0 ? 0 : tasasYProporciones.ProporcionSaldo4LP;
    }

    public void TasasMaximas(SolicitudDTO solicitud, TasasYProporcionesDTO tasasYProporciones, bool estaEnMora, bool Usolidaria)
    {
        tasasYProporciones.TasaMaximaSaldo1 = Math.Round(solicitud.TasaContratacion, 4);
        tasasYProporciones.TasaMaximaSaldo2 = Usolidaria && estaEnMora ?
            Math.Round(solicitud.IPC4, 4) :
            Usolidaria && !estaEnMora ? Math.Round(solicitud.IPC2, 4) : Math.Round(solicitud.TasaContratacion, 4);
        tasasYProporciones.TasaMaximaSaldo3 = Usolidaria && estaEnMora ?
            Math.Round(solicitud.IPC4, 4) :
            Usolidaria && !estaEnMora ? Math.Round(solicitud.IPC2, 4) : Math.Round(solicitud.TasaContratacion, 4);
        tasasYProporciones.TasaMaximaSaldo4 = Math.Round(solicitud.TasaContratacion, 4);
    }

    public void TasasMinimasLP(SolicitudDTO solicitud, TasasYProporcionesDTO tasasYProporciones, bool estaEnMora, bool Usolidaria)
    {
        tasasYProporciones.TasaMinimaSaldo1LP = estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

        tasasYProporciones.TasaMinimaSaldo2LP = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

        tasasYProporciones.TasaMinimaSaldo3LP = Math.Round(solicitud.IPC, 4);

        tasasYProporciones.TasaMinimaSaldo4LP = Math.Round(solicitud.TasaContratacion, 4);
    }

    public void TasasMinimasCP(SolicitudDTO solicitud, TasasYProporcionesDTO tasasYProporciones, bool estaEnMora, bool Usolidaria)
    {
        tasasYProporciones.TasaMinimaSaldo1CP = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

        tasasYProporciones.TasaMinimaSaldo2CP = Usolidaria ? Math.Round(solicitud.IPC, 4) : estaEnMora ? Math.Round(solicitud.IPC4, 4) : Math.Round(solicitud.IPC2, 4);

        tasasYProporciones.TasaMinimaSaldo3CP = Math.Round(solicitud.IPC, 4);

        tasasYProporciones.TasaMinimaSaldo4CP = Math.Round(solicitud.TasaContratacion, 4);
    }

    public void ValidarCuotas(CalculoBaneficioAC001DTO movimiento, SolicitudDTO solicitud)
    {
        List<ValidationFailure>? validaciones = new List<ValidationFailure>();

        if (solicitud.NumTotalCuotas < movimiento.CuotasPendientes)
        {
            ValidationFailure? validacion = new ValidationFailure()
            {
                PropertyName = $"{nameof(movimiento.CuotasPendientes)}",
                ErrorMessage = ValidationMessages.CuotasPendientesMayorANumTotalCuotas
            };
            validaciones.Add(validacion);
        }
        if (movimiento.CuotasPendientes < 0)
        {
            ValidationFailure? validacion = new ValidationFailure()
            {
                PropertyName = $"{nameof(movimiento.CuotasPendientes)}",
                ErrorMessage = ValidationMessages.CuotasPendientesMenorQueCero
            };
            validaciones.Add(validacion);
        }
        if (validaciones.Any())        
            throw new ValidationException(validaciones);        
    }

    public void ValidarFechas(CalculoBaneficioAC001DTO movimiento, CalculoBaneficioAC001DTO ultimoMovimiento)
    {
        if (ultimoMovimiento.FechaMovimiento.Equals(DateTime.MinValue) ||
                        !(movimiento.FechaMovimiento.Year.Equals(ultimoMovimiento.FechaMovimiento.Year) && movimiento.FechaMovimiento.Month.Equals(ultimoMovimiento.FechaMovimiento.Month)))
        {
            ultimoMovimiento.FechaMovimiento = new DateTime(movimiento.FechaMovimiento.Year, movimiento.FechaMovimiento.Month, Constants.PRIMER_DIA_MES);
        }
    }

    public void CalcularBeneficioAcuerdo001Estudios(InteresesLiquidadosDTO interesesLiquidadosDTO, SolicitudDTO solicitud, TasasYProporcionesDTO tasasCartera, SaldosCarteraDTO saldosCarteraCP, SaldosCarteraDTO saldosCarteraLP, int diasSinMovimiento)
    {
        if ((solicitud.Marca!.Equals(TiposBeneficio.APORTES_IES))!)
        {
            interesesLiquidadosDTO.BeneficioAcuerdo001Saldo1 = saldosCarteraLP.Saldo1 * tasasCartera.TasaMinimaSaldo1LP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo1LP * diasSinMovimiento;            
        }
        else
        {
            interesesLiquidadosDTO.BeneficioAcuerdo001Saldo1 = saldosCarteraLP.Saldo1 * tasasCartera.TasaMinimaSaldo1LP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo1LP * diasSinMovimiento +
                saldosCarteraLP.Saldo2 * tasasCartera.TasaMinimaSaldo2LP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo2LP * diasSinMovimiento +
                saldosCarteraCP.Saldo1 * tasasCartera.TasaMinimaSaldo1CP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo1CP * diasSinMovimiento +
                saldosCarteraCP.Saldo2 * tasasCartera.TasaMinimaSaldo2CP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo2CP * diasSinMovimiento;
        }
    }

    public void CalcularAjusteCies(InteresesLiquidadosDTO interesesLiquidadosDTO, SolicitudDTO solicitud, TasasYProporcionesDTO tasasCartera, SaldosCarteraDTO saldosCarteraLP, int diasSinMovimiento)
    {
        if ((solicitud.Marca!.Equals(TiposBeneficio.APORTES_IES))!)
        {
            interesesLiquidadosDTO.AjusteCIESSaldo2 = saldosCarteraLP.Saldo2 * tasasCartera.TasaMinimaSaldo2LP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo2LP * diasSinMovimiento;
        }
    }

    public void CalcularAjusteAies(InteresesLiquidadosDTO interesesLiquidadosDTO, SolicitudDTO solicitud, TasasYProporcionesDTO tasasCartera, SaldosCarteraDTO saldosCarteraLP, int diasSinMovimiento)
    {
        if ((solicitud.Marca!.Equals(TiposBeneficio.APORTES_IES))!)
        {
            interesesLiquidadosDTO.AjusteAportesIESSaldo3 = saldosCarteraLP.Saldo3 * tasasCartera.TasaMinimaSaldo3LP / Constants.BASE_LIQUIDACION * tasasCartera.ProporcionSaldo3LP * diasSinMovimiento;
        }
    }

    public InteresesLiquidados MapperInteresesLiquidados(InteresesLiquidadosDTO interesesLiquidados)
    {
        return new InteresesLiquidados
        {
            IdSaldosSolicitudCartera = interesesLiquidados.IdSaldosSolicitudCartera,
            IdMovimiento = interesesLiquidados.IdMovimiento,
            BeneficioAcuerdo001Saldo1 = interesesLiquidados.BeneficioAcuerdo001Saldo1,
            AjusteCIESSaldo2 = interesesLiquidados.AjusteCIESSaldo2,
            AjusteAportesIESSaldo3 = interesesLiquidados.AjusteAportesIESSaldo3,
            DiasCalculo = interesesLiquidados.DiasCalculo,
            ValorTasaContratacion = interesesLiquidados.ValorTasaContratacion,
            IpcContratacion = interesesLiquidados.IpcContratacion,
            ValorTasaBeneficio = interesesLiquidados.ValorTasaBeneficio,
            IpcBeneficio = interesesLiquidados.IpcBeneficio,
            ValorLimiteAjuste = interesesLiquidados.ValorLimiteAjuste,
            ValorAjusteAcumulado = interesesLiquidados.ValorAjusteAcumulado
        };
    }
}
