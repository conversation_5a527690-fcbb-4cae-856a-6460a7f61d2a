﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Application.Contracts.Persistence
{
    public interface INuevoscreditosRepository : IGenericRepository<SaldosCartera>
    {
        Task<DataQueryNuevosCreditodDTO> ObtenerDataNuevosCreditos(long idSolicitud, int noRelacion, int idSubProducto);
        Task<bool> ValidarGiroInternacional(long idSolicitud);
    }
}
