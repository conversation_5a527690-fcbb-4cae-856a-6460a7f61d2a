﻿namespace ApiCartera.Domain.Features.DivisionSaldos.DTOs
{
    public class SaldosCarteraDTO : ICloneable
    {
        private int? _idMovimiento;
        private int? _idSaldosSolicitudCartera;

        public int Id { get; set; }
        public double Saldo1 { get; set; }
        public double Saldo2 { get; set; }
        public double Saldo3 { get; set; }
        public double Saldo4 { get; set; }
        public long IdSolicitud { get; set; }
        public int IdSubproducto { get; set; }
        public string CodigoSubproducto { get; set; } = string.Empty;
        public int? IdSaldosSolicitudCartera 
        { 
            get => _idSaldosSolicitudCartera; 
            set => _idSaldosSolicitudCartera = value == 0 ? null : value; 
        }
        public int? IdSaldosCarteraAnt { get; set; }
        public int? IdMovimiento 
        { 
            get => _idMovimiento; 
            set => _idMovimiento = value == 0 ? null : value; 
        }

        public object Clone()
        {
            return this.MemberwiseClone();
        }   
    }
}
