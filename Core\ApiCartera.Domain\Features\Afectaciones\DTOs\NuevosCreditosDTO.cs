﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Domain.Features.Afectaciones.DTOs
{
    public class NuevosCreditosDTO
    {
        //Data del excel
        [JsonProperty("idIcetex")]
        public int IdIcetex { get; set; }

        [JsonProperty("montoMovimientoCapital")]
        public double MontoMovimientoCapital { get; set; }

        [JsonProperty("codigoTipoMovimientoCapital")]
        public int CodigoTipoMovimientoCapital { get; set; }

        [JsonProperty("codigoSubproductoObligacion")]
        public int CodigoSubproductoObligacion { get; set; }

        [JsonProperty("descripcionMovimientoMemo")]
        public string? DescripcionMovimientoMemo { get; set; }

        [JsonProperty("codigoNovedad")]
        public int CodigoNovedad { get; set; }
    }
}
