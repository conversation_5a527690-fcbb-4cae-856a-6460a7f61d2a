﻿using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace ApiCartera.Domain.Features.Shared.Extensions
{
    public static class StringExtensions
    {
        private static Dictionary<char, char> Replacements()=> new Dictionary<char, char>
        {
            {'á', 'a'}, {'é', 'e'}, {'í', 'i'}, {'ó', 'o'}, {'ú', 'u'},
            {'Á', 'A'}, {'É', 'E'}, {'Í', 'I'}, {'Ó', 'O'}, {'Ú', 'U'},
            {'ñ', 'n'}, {'Ñ', 'N'}, 
            {'ä', 'a'}, {'ë', 'e'}, {'ï', 'i'}, {'ö', 'o'}, {'ü', 'u'},
            {'Ä', 'A'}, {'Ë', 'E'}, {'Ï', 'I'}, {'Ö', 'O'}, {'Ü', 'U'},
            {'ç', 'c'}, {'Ç', 'C'},
            {'à', 'a'}, {'è', 'e'}, {'ì', 'i'}, {'ò', 'o'}, {'ù', 'u'},
            {'À', 'A'}, {'È', 'E'}, {'Ì', 'I'}, {'Ò', 'O'}, {'Ù', 'U'},
            {'ã', 'a'}, {'õ', 'o'}, {'Ã', 'A'}, {'Õ', 'O'},
            {'â', 'a'}, {'ê', 'e'}, {'î', 'i'}, {'ô', 'o'}, {'û', 'u'},
            {'Â', 'A'}, {'Ê', 'E'}, {'Î', 'I'}, {'Ô', 'O'}, {'Û', 'U'},
            {'ý', 'y'}, {'ÿ', 'y'}, {'Ý', 'Y'}
        };
        public static string NormalizarTexto(this string texto)
        {
            StringBuilder stringBuilder = new StringBuilder();

            foreach (char c in texto.Normalize(NormalizationForm.FormD))
            {
                if (Replacements().TryGetValue(c, out var replacement))
                {
                    stringBuilder.Append(replacement);
                }
                else if (CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().ToLowerInvariant();
        }
        public static bool EsIgualNormalizado(this string texto, string otroTexto)
        {
            return texto.NormalizarTexto() == otroTexto.NormalizarTexto();
        }
    }
}
