﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Domain.Features.Afectaciones.Services
{
    public interface IAfectacionSaldoReintegro
    {
        Task<SaldosCarteraDTO> AfectacionSaldoReintegro(MovimientoAfectacionDTO dataSaldoReintegro);
    }
}
