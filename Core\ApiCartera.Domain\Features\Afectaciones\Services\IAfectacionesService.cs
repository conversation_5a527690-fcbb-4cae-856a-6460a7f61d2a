﻿using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;

namespace ApiCartera.Domain.Features.Afectaciones.Services
{
    public interface IAfectacionesService
    {
        SaldosCarteraDTO DisminuirSaldosProgresivamente(SaldosCarteraDTO saldosCartera, double valorAfectacion);
        SaldosCarteraDTO ConfigurarSaldosACero(SaldosCarteraDTO saldosCartera);
        SaldosCartera MapearSaldosCartera(SaldosCarteraDTO saldosCartera);
    }
}
