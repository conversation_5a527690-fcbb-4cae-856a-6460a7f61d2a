﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalReversionReintegroService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraService
    {
        private const int ANIO_APORTES_IES = 2024;
        private const int ANIO_CONTRIBUCION_IES = 2023;

        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoDTO movimiento)
        {
            ValidarMovimiento(movimiento);

            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            ValidarSaldosCartera(saldosCartera, movimiento.IdSolicitud);

            //var desembolso = await solicitudCarteraActivaRepository
            //    .ObtenerDesembolsoPorNoRelacion(movimiento.IdSolicitud, movimiento.NoRelacion.Value);

            var NumeroRelacion = int.Parse(movimiento.NoRelacion);

            var desembolso = await solicitudCarteraActivaRepository
                .ObtenerDesembolsoPorNoRelacion(movimiento.IdSolicitud, NumeroRelacion);

            ValidarDesembolso(desembolso);

            var nuevosSaldosCartera = (SaldosCarteraDTO)saldosCartera.Clone();

            var desembolsoTieneVigenciaAnteriorA20241 = desembolso.YearGiro < ANIO_APORTES_IES;
            var desembolsoTieneVigenciaPosterioresA20232 = desembolso.YearGiro > ANIO_CONTRIBUCION_IES;

            var tieneMarcaSinBeneficio = string.IsNullOrEmpty(desembolso.Marca)
                || desembolso.Marca.Equals(TiposBeneficio.NO_APLICA_BENEFICIO)
                || (desembolso.Marca.Equals(TiposBeneficio.APORTES_IES)
                    && (desembolso.ValorAportesIES == null || desembolso.ValorAportesIES == 0));
            var tieneMarcaAportesIES = desembolso.Marca.Equals(TiposBeneficio.APORTES_IES)
                && (desembolso.ValorAportesIES != null && desembolso.ValorAportesIES > 0);

            if (tieneMarcaSinBeneficio && desembolsoTieneVigenciaAnteriorA20241)
            {
                nuevosSaldosCartera.Saldo1 += movimiento.ValorGiro;
            }

            if (desembolso.Marca.Equals(TiposBeneficio.CONTRIBUCION_IES))
            {
                nuevosSaldosCartera.Saldo2 += movimiento.ValorGiro;
            }

            if (tieneMarcaAportesIES)
            {
                nuevosSaldosCartera.Saldo3 += movimiento.ValorGiro;
            }

            if (tieneMarcaSinBeneficio && desembolsoTieneVigenciaPosterioresA20232)
            {
                nuevosSaldosCartera.Saldo4 += movimiento.ValorGiro;
            }

            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimiento;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
            var saldos = this.MapearSaldosCartera(nuevosSaldosCartera);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldos);

            return nuevosSaldosCartera;
        }

        private static void ValidarDesembolso(DesembolsoDTO desembolso)
        {
            if (desembolso == null)
            {
                throw new ValidationException(
                [
                    new()
                    {
                        PropertyName = nameof(desembolso.IdSolicitud),
                        ErrorMessage = $"{ValidationMessages.Desembolso} {desembolso.IdSolicitud}"
                    }
                ]);
            }
            var desembolsoValidador = new DesembolsoValidator();
            desembolsoValidador.ValidateAndThrowCustomValidationException(desembolso);
        }
    }
}
