﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Shared.Entities;

public class InteresesLiquidados : Entity
{
    [JsonProperty("idSaldosSolicitudCartera")] 
    public int IdSaldosSolicitudCartera { get; set; }
    [JsonProperty("idMovimiento")] 
    public int IdMovimiento { get; set; }
    [JsonProperty("beneficioAcuerdo001Saldo1")] 
    public double? BeneficioAcuerdo001Saldo1 { get; set; }
    [JsonProperty("ajusteCIESSaldo2")] 
    public double? AjusteCIESSaldo2 { get; set; }
    [JsonProperty("ajusteAportesIESSaldo3")] 
    public double? AjusteAportesIESSaldo3 { get; set; }
    public int DiasCalculo { get; set; }
    [JsonProperty("valorTasaContratacion")]
    public double? ValorTasaContratacion { get; set; }
    [JsonProperty("ipcContratacion")]
    public string? IpcContratacion { get; set; }
    [JsonProperty("valorTasaBeneficio")]
    public double? ValorTasaBeneficio { get; set; }
    [JsonProperty("ipcBeneficio")]
    public string? IpcBeneficio { get; set; }
    [JsonProperty("valorLimiteAjuste")]
    public double? ValorLimiteAjuste { get; set; }
    [JsonProperty("valorAjusteAcumulado")]
    public double? ValorAjusteAcumulado { get; set; }
}
