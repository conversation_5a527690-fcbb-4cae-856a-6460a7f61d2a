﻿using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;

namespace ApiCartera.Application.Contracts.Persistence
{
    public interface ISaldosCarteraRepository : IGenericRepository<SaldosCartera>
    {
        Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorId(int id);
        Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitud(long idSolicitud, int idSubproducto);
        Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitud(long idSolicitud, int idSubproducto, DateTime fecha);
        Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(long idSolicitud, string codigoSubproducto);
    }
}
