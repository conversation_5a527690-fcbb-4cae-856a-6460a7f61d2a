﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Entities;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Movimientos.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using System.Globalization;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class MovimientoDataService(BusService busService) : GenericRepositoryBusDataService<Movimiento>(busService), IMovimientoRepository
    {
        public async Task<List<ConsultaMovimientoDTO>> ConsultarMovimientoCredito(ParametrosConsultaMovimientoSolicitudDTO consultarMovimientoSolicitud)
        {
            var validaciones = new List<ValidationFailure>();
            var url = $"movimiento-credito?fechaInicio={consultarMovimientoSolicitud.FechaInicio.ToString("yyyy-MM-ddTHH:mm:ssZ")}&fechaFin={consultarMovimientoSolicitud.FechaFin.ToString("yyyy-MM-ddTHH:mm:ssZ")}";

            if (consultarMovimientoSolicitud.IdSolicitud.HasValue)
            {
                url += $"&idSolicitud={consultarMovimientoSolicitud.IdSolicitud}";
            }
            if (consultarMovimientoSolicitud.IdSignature.HasValue)
            {
                url += $"&idSignature={consultarMovimientoSolicitud.IdSignature}";
            }
            if (consultarMovimientoSolicitud.Documento.HasValue)
            {
                url += $"&documento={consultarMovimientoSolicitud.Documento}";
            }
            else
            {
                validaciones.Add(new()
                {
                    PropertyName = $"{nameof(consultarMovimientoSolicitud.IdSolicitud)} - {nameof(consultarMovimientoSolicitud.IdSignature)} - {nameof(consultarMovimientoSolicitud.Documento)}",
                    ErrorMessage = "No se proporciono ningún valor para los parametros IdSolicitud, IdSignature o Documento"
                });
                throw new ValidationException(validaciones);
            }

            var respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<ConsultaMovimientoDTO>>>(url);

            if (respuesta.Response != TiposRespuestaDataService.EXITOSA)
            {
                validaciones.Add(new()
                {
                    PropertyName = nameof(url),
                    ErrorMessage = respuesta.Response
                });
                throw new ValidationException(validaciones);
            }
            return respuesta.Data;
        }

        public async Task<CalculoBaneficioAC001DTO> ObtenerUltimo(long idSolicitud, DateTime fechaMovimiento)
        {
            var url = $"ultimomovimiento?idSolicitud={idSolicitud}&fechaMovimientoActual={fechaMovimiento.ToString("yyyy-MM-dd")}";
            var respuestaObtenerUltimo = await busService.Services.Generico.Get<RespuestaDataServiceDTO<CalculoBaneficioAC001DTO>>(url);
            if (respuestaObtenerUltimo.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(idSolicitud)} - {nameof(fechaMovimiento)}",
                        ErrorMessage = respuestaObtenerUltimo.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaObtenerUltimo.Data;
        }

        public async Task ActualizarMovimiento(MovimientoDTO movimiento)
        {
            var url = $"actualizarmovimiento";
            var respuestaActualizarMovimiento = await busService.Services.Generico.Post<RespuestaDataServiceDTO<dynamic>, dynamic>(url, movimiento);

            if (respuestaActualizarMovimiento.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                    {
                        new()
                        {
                            PropertyName = nameof(movimiento),
                            ErrorMessage = respuestaActualizarMovimiento.Response
                        }
                    };
                throw new ValidationException(validaciones);
            }
        }
    }
}
