﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService.DivisionSaldos
{
    public class ActualizarControlSaldosDataService(BusService busService, IConfiguration configuration) :
        GenericCustomRepositoryBusDataService<ControlDivisionSaldos>(busService, configuration), IActualizarControlDivisionSaldos
    {
        
        public async Task ActualizarControlDivisionSaldos(int estado, string tipoFallo, int? idSaldosSolicitudCartera)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            bool validacion = false;
            string? url = $"mbt/actualiza/control-divisiones";
            var body = new
            {
                estado = estado,
                tipoFallo = tipoFallo,
                idSaldosSolicitudCartera = idSaldosSolicitudCartera
            };
            RespuestaDataServiceDTO<dynamic>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Post<RespuestaDataServiceDTO<dynamic>, dynamic>(url, body);
                    contador = maxReintentos + 1;
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                    contador++;
                    if (contador == 4)
                        validacion = true;
                }
            }
            if (validacion)
            {
                List<ValidationFailure>? validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(ActualizarControlDivisionSaldos)} {Constants.DATASERVICE}",
                        ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                    }
                };
                throw new ValidationException(validaciones);
            }         
        }        
    }
}
