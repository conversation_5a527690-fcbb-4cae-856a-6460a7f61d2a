using AutoMapper;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Queries.DANE.ObtenerHistoricoDANE;
using Sarlaft.Application.Features.Parametricas.ViewModels;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.DANE
{
    public class ObtenerHistoricoDANETest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var mockRepository = new Mock<IDANEHistoricoRepository>();
            var mockMapper = new Mock<IMapper>();

            var handler = new ObtenerHistoricoDANEQueryHandler(mockRepository.Object, mockMapper.Object);

            var query = new ObtenerHistoricoDANEQuery();
            var fileName = "test.pdf";
            List<DANEHistorico> daneHistorico = new()
            {
                new ()
                {
                    Id = EntityExtensionTest.GenerateId(),
                    NombreArchivo = fileName,
                    UbicacionRelativa = $"DANE/{fileName}",
                    FechaCreacion = DateTime.Now
                },

                new ()
                {
                    Id = EntityExtensionTest.GenerateId(),
                    NombreArchivo = fileName,
                    UbicacionRelativa = $"DANE/{fileName}",
                    FechaCreacion = DateTime.Now
                },
            };
            var daneHistoricoVm = daneHistorico.Select(dane => new DANEHistoricoVm
            {
                Id = dane.Id,
                FechaCreacion = dane.FechaCreacion.ToString(),
                NombreArchivo = dane.NombreArchivo,
            }).ToList() ?? new List<DANEHistoricoVm>();

            // Configura el comportamiento esperado para el repositorio y el mapeador
            mockRepository.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(daneHistorico);
            mockMapper.Setup(storage => storage.Map<List<DANEHistoricoVm>>(daneHistorico)).Returns(daneHistoricoVm);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(result.Count(), daneHistoricoVm.Count());
        }
    }
}