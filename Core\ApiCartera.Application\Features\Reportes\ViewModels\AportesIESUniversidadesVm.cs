﻿namespace ApiCartera.Application.Features.Reportes.ViewModels;

public record AportesIESUniversidadesVm(
    int? CodigoSNIES,
    long? RelacionGiro,
    string? NombreCompleto,
    long? Documento,
    int? IdSolicitud,
    string? TipoLineaCredito,
    string? TipoSubLineaCredito,
    string? TipoCartera,
    string? ModalidadCredito,
    string? Sisben,
    int? Estrato,
    double? ValorSubsidioIES,
    double? ValorIESLargoPlazo,
    double? ValorIESCortoPlazo,
    double? ValorIcetexSubsidio,
    double? ValorIcetexCredito,
    double? ValorAlianzaSubsidio,
    double? ValorAlianzaCredito,
    double? ValorPrima,
    double? ValorTotal,
    double? TotalGirar,
    int? YearGiro,
    int? SemestreGiro,
    string? FechaGiro,
    string? EstadoResolucion,
    double? ValorLegRenovado,
    double? PorcentajeIPC,
    double? Puntos,
    double? PorcentajePuntosEquivalentes,
    double? ValorExigible,
    double? ValorNoExigible,
    int? Periodicidad,
    string? TipoAnexo,
    string? TipoBeneficio,
    double? PorcentajeAporteIES,
    double? ValorAporteIES,
    double? PorcentajeAporteIcetex,
    double? ValorAporteIcetex,
    double? PorcentajeFondoSostenibilidad,
    double? SubfondoIES,
    double? ValorPorcPiloRezagado,
    int? ConteoGirosConAporte,
    double? SaldoCapital,
    int? DiasInteres,
    double? Factor2,
    double? Factor3,
    string? EsComplementario,
    string? FechaGiroAnteriorAporte,
    string? FechaGiroConAporte,
    long? NitIES
);
