﻿using System.Text.Json.Serialization;

namespace ApiCartera.API.Wrappers
{
    public class ResponseWrapper<T>
    {
        public ResponseWrapper(int statusCode, T? data, string message = "",bool success = false, IDictionary<string, string[]>? errors = null)
        {
            Data = data;
            Message = message;
            StatusCode = statusCode;
            Success = success;
            Errors = errors;
        }
        [JsonPropertyName("data")]
        public T? Data { get; set; }

        [JsonPropertyName("statusCode")]
        public int StatusCode { get; set; }

        [JsonPropertyName("success")]
        public bool Success { get; set; }
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
        [JsonPropertyName("errors")]
        public IDictionary<string, string[]>? Errors { get; set; }
    }
}
