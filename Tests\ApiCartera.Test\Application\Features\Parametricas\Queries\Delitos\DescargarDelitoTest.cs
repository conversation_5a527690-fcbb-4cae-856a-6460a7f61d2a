using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Application.Features.Parametricas.Queries.Delitos.DescargarDelito;
using Sarlaft.Domain.Features.Files.Entities;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Infrastructure.Persitence.Memory;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Delitos
{
    public class DescargarDelitoTest
    {
        private byte[] fileBytesTest => Enumerable.Repeat((byte)0x55, 20).ToArray();
        [Fact]
        public async Task ObtenerCorrecta()
        {
            IGenericRepository<Delito> mockRepository = new GenericRepositoryMemory<Delito>();
            var mockStorage = new Mock<IStorageService>();

            var handler = new DescargarDelitoQueryHandler(mockRepository, mockStorage.Object);

            var fileName = "test.pdf";
            var delito = new Delito
            {
                Nombre = "Secuestro",
                TipoDelito = Sarlaft.Domain.Features.Parametricas.Enums.TipoDelitoEnum.LA,
                NombreArchivo = fileName,
                UbicacionRelativa = $"Delito/{fileName}",
                NombreArchivoOriginal = fileName
            };
            await mockRepository.CrearAsync(delito);
            var query = new DescargarDelitoQuery(delito.Id);

            var file = new CustomFile(fileBytesTest, fileName, ApplicationTypes.JSON, 6);
            mockStorage.Setup(storage => storage.Get(delito.UbicacionRelativa)).Returns(file);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(file, result);
        }


    }
}