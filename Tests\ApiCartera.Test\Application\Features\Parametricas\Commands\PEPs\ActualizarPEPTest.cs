using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Commands.PEPs.ActualizarPEP;
using Sarlaft.Domain.Features.Parametricas.Entities;

namespace Sarlaft.Test.Application.Features.Parametricas.Commands.PEPs
{
    public class ActualizarPEPTest
    {
        [Fact]
        public async Task ActualizarPEP_Correcta()
        {
            var services = DependencyContainer.Get();
            var mockRepository = new Mock<IGenericRepository<PEP>>();
            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<PEP>()
                {
                    new(){ Id= 1, Categoria="profesional", Valor=1},
                });
            services.AddScoped(_ => mockRepository.Object);

            using (var scope = services.BuildServiceProvider().CreateScope())
            {
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var command = new ActualizarPEPCommand(1,"tecnico", 1);

                // Ejecuta el comando utilizando MediatR
                var resultado = await mediator.Send(command);

                mockRepository.Verify(repo => repo.ActualizarAsync(It.IsAny<PEP>()));
                Assert.Equal(resultado, command.Id);
            }
        }

        [Fact]
        public async Task ActualizarPEP_Validaciones_YaExistente()
        {
            //Arrange
            var mockRepository = new Mock<IGenericRepository<PEP>>();
            var validator = new ActualizarPEPCommandValidator(mockRepository.Object);
            var command = new ActualizarPEPCommand(1,"m",1);

            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<PEP>()
                {
                    new(){ Id= 2, Categoria="m", Valor=1},
                });

            //Act
            var validatorResult = await validator.ValidateAsync(command);

            //Assert
            Assert.False(validatorResult.IsValid);
            Assert.True(validatorResult.Errors.Any());
            Assert.True(validatorResult.Errors.Count() == 2);
        }
    }
}