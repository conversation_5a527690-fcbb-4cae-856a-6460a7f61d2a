﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("BENEFICIOS")]
public class BeneficiosEntity : IEntity<string>   
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int IdBeneficio { get; set; }
    public string? Descripcion { get; set; }
}
