﻿using ApiCartera.Application.Contracts.Storage;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Files.Entities;
using ApiCartera.Domain.Features.Files.Enums;
using ApiCartera.Domain.Features.Files.Messages;

namespace ApiCartera.Domain.Features.Files.Extensions
{
    public static class ApplicationFileTypeExtensions
    {
        public static List<ApplicationFileType> ApplicationFileTypes = new List<ApplicationFileType>
        {
            new ApplicationFileType(ApplicationTypes.PDF, FormatFile.PDF, FileExtensions.PDF),
            new ApplicationFileType(ApplicationTypes.JSON, FormatFile.JSON, FileExtensions.JSON),
            new ApplicationFileType(ApplicationTypes.OctectStream, FormatFile.Binary, FileExtensions.OctectStream),
            new ApplicationFileType(ApplicationTypes.Excel, FormatFile.Excel, FileExtensions.Excel),
            new ApplicationFileType(ApplicationTypes.ExcelNuevo, FormatFile.ExcelNuevo, FileExtensions.ExcelNuevo),
        };

        public static List<ApplicationFileType> GetApplicationTypes(this List<FormatFile> formatFile)
        {
            return ApplicationFileTypes
                .Where(applicationFileType => formatFile.Any(formatFile => formatFile == applicationFileType.FormatFile))
                .ToList();
        }
        public static string GetExtensionConcat(this List<ApplicationFileType> formatFile, string parameterConcat=",")
        {
            return string.Join(parameterConcat, formatFile.Select(applicationFileType => applicationFileType.Extension));
        }


        public static string GetFileNameChange(this string currentFullName, string newNameWithoutExtension)
        {
            return $"{newNameWithoutExtension}{Path.GetExtension(currentFullName)}";
        }

        public static ApplicationFileType FindByApplicationType(string applicationType) =>
            ApplicationFileTypes.FirstOrDefault(app => app.ApplicationType.ToLowerInvariant() == applicationType.ToLowerInvariant()) ??
            throw new NotFoundException(string.Format(FileValidationMessages.NoSeEncontroFormatFileType, nameof(ApplicationFileType.ApplicationType),applicationType));
        public static ApplicationFileType FindByExtension(string extension) =>
            ApplicationFileTypes.FirstOrDefault(app => app.Extension.ToLowerInvariant() == extension.ToLowerInvariant()) ??
           throw new NotFoundException(string.Format(FileValidationMessages.NoSeEncontroFormatFileType, nameof(ApplicationFileType.Extension), extension));
        public static ApplicationFileType FindByFormatFile(FormatFile formatFile) =>
            ApplicationFileTypes.FirstOrDefault(app => app.FormatFile == formatFile) ??
            throw new NotFoundException(string.Format(FileValidationMessages.NoSeEncontroFormatFileType, nameof(ApplicationFileType.FormatFile), formatFile));
    }
}
