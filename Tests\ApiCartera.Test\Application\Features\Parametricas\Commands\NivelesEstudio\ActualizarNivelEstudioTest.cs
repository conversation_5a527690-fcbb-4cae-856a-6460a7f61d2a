using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Commands.NivelesEstudio.ActualizarNivelEstudio;
using Sarlaft.Domain.Features.Parametricas.Entities;

namespace Sarlaft.Test.Application.Features.Parametricas.Commands.NivelesEstudio
{
    public class ActualizarNivelEstudioTest
    {
        [Fact]
        public async Task ActualizarNivelEstudio_Correcta()
        {
            var services = DependencyContainer.Get();
            var mockRepository = new Mock<IGenericRepository<NivelEstudio>>();
            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<NivelEstudio>()
                {
                    new(){ Id= 1, Nombre="profesional", CodigoNivel=1},
                });
            services.AddScoped(_ => mockRepository.Object);

            using (var scope = services.BuildServiceProvider().CreateScope())
            {
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var command = new ActualizarNivelEstudioCommand(1,"tecnico", 1);

                // Ejecuta el comando utilizando MediatR
                var resultado = await mediator.Send(command);

                mockRepository.Verify(repo => repo.ActualizarAsync(It.IsAny<NivelEstudio>()));
                //Assert.Equal(1, mockRepository.Invocations.Count());
                Assert.Equal(resultado, command.Id);
            }
        }

        [Fact]
        public async Task ActualizarNivelEstudio_Validaciones_YaExistente()
        {
            //Arrange
            var mockRepository = new Mock<IGenericRepository<NivelEstudio>>();
            var validator = new ActualizarNivelEstudioCommandValidator(mockRepository.Object);
            var command = new ActualizarNivelEstudioCommand(1,"tecnico",1);

            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<NivelEstudio>()
                {
                    new(){ Id= 2, Nombre="tecnico", CodigoNivel=1},
                });

            //Act
            var validatorResult = await validator.ValidateAsync(command);

            //Assert
            Assert.False(validatorResult.IsValid);
            Assert.True(validatorResult.Errors.Any());
            Assert.True(validatorResult.Errors.Count() == 2);
        }
    }
}