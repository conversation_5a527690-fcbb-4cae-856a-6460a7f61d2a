﻿using ApiCartera.Application.Contracts.Persistence.Registros;
using CsvHelper;
using CsvHelper.Configuration;
using System.Reflection;
using CsvHelper.Configuration.Attributes;
using Renci.SshNet;
using System.Globalization;

namespace ApiCartera.Infrastructure.Registros
{
    public class SFTPRepository : ISFTPRepository
    {
        public List<T> LeerArchivo<T>(string host, string username, string password, string remoteFilePath)
        {
            List<T> registros;
            int port = 22;
            using var sftp = new SftpClient(host, port, username, password);
            try
            {
                sftp.Connect();

                using MemoryStream memoryStream = new();
                sftp.DownloadFile(remoteFilePath, memoryStream);
                memoryStream.Position = 0;

                var config = new CsvConfiguration(new CultureInfo("es-ES")) 
                {
                    Delimiter = "|",
                    HasHeaderRecord = true
                };

                using StreamReader reader = new(memoryStream);
                using CsvReader csv = new(reader, config);

                csv.Read();
                csv.ReadHeader();                 
                csv.ValidateHeader<T>();

                registros = csv.GetRecords<T>().ToList();

                return registros;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error: {ex.Message}");
                throw;
            }
            finally
            {
                sftp?.Disconnect();
                sftp?.Dispose();
            }
        }

        public List<T> LeerArchivoMovimientoDiario<T>(string host, string username, string password, string remoteFilePath)
        {
            List<T> registros;
            int port = 22;
            using var sftp = new SftpClient(host, port, username, password);
            try
            {
                sftp.Connect();

                using MemoryStream memoryStream = new();
                sftp.DownloadFile(remoteFilePath, memoryStream);
                memoryStream.Position = 0;

                
                using StreamReader reader = new(memoryStream);
                
                var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    Delimiter = "|",
                    HasHeaderRecord = true
                };
                
                using CsvReader csv = new(reader, config);
                
                
                csv.Read();
                csv.ReadHeader();
                
                var expectedColumns = typeof(T).GetProperties()
                    .Count(p => p.GetCustomAttributes(typeof(NameAttribute), false).Any());
                    
                if (csv.HeaderRecord?.Length != expectedColumns)
                {
                    throw new InvalidOperationException(
                        $"Cantidad de columnas incorrecta. Esperadas: {expectedColumns}, Encontradas: {csv.HeaderRecord?.Length}");
                }
                
                try {
                    csv.ValidateHeader<T>();
                }
                catch (HeaderValidationException ex)
                {
                    //throw new InvalidOperationException(
                    //    $"Los nombres de las columnas no coinciden con el modelo esperado: {ex.Message}", ex);
                    throw new InvalidOperationException("Los nombres de las columnas no coinciden con el modelo esperado");
                }

                registros = csv.GetRecords<T>().ToList();
                return registros;
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"Error de validación del archivo CSV: {ex.Message}");
                throw; 
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error inesperado al leer el archivo desde SFTP: {ex.Message}");
                throw;
            }
            finally
            {
                sftp?.Disconnect();
                sftp?.Dispose();
            }
        }
    }
}
