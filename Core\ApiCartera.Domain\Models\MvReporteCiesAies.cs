﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models;

[EntityName("VM_REPORTE_CIES_AIES")]
public class MvReporteCiesAies : IEntity<string>
{
    public string Id
    {
        get => IdSolicitud.ToString();
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int IdSolicitud { get; set; }
    public int IdSignature { get; set; }
    public string? Linea { get; set; }
    public string? Sublinea { get; set; }
    public string? CodigoSNIES { get; set; }
    public string? TipoCartera { get; set; }
    public string? ModalidadCredito { get; set; }
    public string? PeriodoGiro { get; set; }
    public string? Anexo { get; set; }
    public double PorcentajeAsumeIES { get; set; }
    public double PorcentajeAsumeIcetex { get; set; }
    public DateOnly? FechaGiro { get; set; }
    public DateOnly? FechaGeneracion { get; set; }
    public double DiasCalculo { get; set; }
    public double CapitalExigible { get; set; }
    public double CapitalNoExigible { get; set; }
    public double Saldo1 { get; set; }
    public double AjusteSaldo1 { get; set; }
    public double Saldo2 { get; set; }
    public double AjusteSaldo2 { get; set; }
    public double Saldo3 { get; set; }
    public double AjusteSaldo3 { get; set; }
    public double Saldo4 { get; set; }
    public double AjusteSaldo4 { get; set; }
    public double ValorTasaContratacion { get; set; }
    public string? IPCContratacion { get; set; }
    public double TasaMinimaSaldo1LargoPlazo { get; set; }
    public double TasaMinimaSaldo2LargoPlazo { get; set; }
    public double TasaMinimaSaldo3LargoPlazo { get; set; }
    public double TasaMinimaSaldo4LargoPlazo { get; set; }
    public double TasaMaximaSaldo1LargoPlazo { get; set; }
    public double TasaMaximaSaldo2LargoPlazo { get; set; }
    public double TasaMaximaSaldo3LargoPlazo { get; set; }
    public double TasaMaximaSaldo4LargoPlazo { get; set; }
    public double Factor1 { get; set; }
    public double Factor2 { get; set; }
    public double Factor3 { get; set; }
    public double Factor4 { get; set; }
    public double ResultadoFactor2Factor3 { get; set; }
    public double ValorAporteAplicado { get; set; }
    public double ValorAporteIES { get; set; }
    public double ValorAjuste { get; set; }
    public string? ValidadorAjuste { get; set; }
    public double SaldoAporte { get; set; }
    public DateOnly? FechaFinEjecucion { get; set; }
}
