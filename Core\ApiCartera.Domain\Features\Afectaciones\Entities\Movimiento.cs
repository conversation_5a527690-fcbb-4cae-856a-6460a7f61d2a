﻿using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Domain.Features.Afectaciones.Entities
{
    public class Movimiento : Entity
    {
        public DateTime FechaMovimiento { get; set; }
        public double ValorGiro { get; set; }
        public string Descripcion { get; set; }
        public int IdTipoNovedad { get; set; }
        public int Year { get; set; }
        public int Semestre { get; set; }
        public long IdSolicitud { get; set; }
        public int IdSubproducto { get; set; }
        public int? NoRelacion { get; set; }
        public int? CodigoTransaccion { get; set; }
        public double SaldoCapitalVigente { get; set; }
        public double SaldoTotalCapital { get; set; }
        public double SaldoCapitalVencido { get; set; }
    }
}
