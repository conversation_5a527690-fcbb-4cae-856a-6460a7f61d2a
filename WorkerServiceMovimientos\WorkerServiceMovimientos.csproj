<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-WorkerServiceMovimientos-946439a1-71e7-444a-8978-ec67964f4a3c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ApiCartera.Application\ApiCartera.Application.csproj" />
    <ProjectReference Include="..\Core\ApiCartera.Domain\ApiCartera.Domain.csproj" />
    <ProjectReference Include="..\Infrastructure\ApiCartera.Infrastructure\ApiCartera.Infrastructure.csproj" />
  </ItemGroup>
</Project>
