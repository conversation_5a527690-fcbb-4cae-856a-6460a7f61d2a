﻿namespace ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;

public class TasasYProporcionesDTO
{
    public int Id { get; set; }
    public long IdSolicitud { get; set; }
    public double TasaMinimaSaldo1CP { get; set; }
    public double TasaMinimaSaldo2CP { get; set; }
    public double TasaMinimaSaldo3CP { get; set; }
    public double TasaMinimaSaldo4CP { get; set; }
    public double TasaMinimaSaldo1LP { get; set; }
    public double TasaMinimaSaldo2LP { get; set; }
    public double TasaMinimaSaldo3LP { get; set; }
    public double TasaMinimaSaldo4LP { get; set; }
    public double TasaMaximaSaldo1 { get; set; }
    public double TasaMaximaSaldo2 { get; set; }
    public double TasaMaximaSaldo3 { get; set; }
    public double TasaMaximaSaldo4 { get; set; }
    public double ProporcionSaldo1CP { get; set; }
    public double ProporcionSaldo2CP { get; set; }
    public double ProporcionSaldo3CP { get; set; }
    public double ProporcionSaldo4CP { get; set; }
    public double ProporcionSaldo1LP { get; set; }
    public double ProporcionSaldo2LP { get; set; }
    public double ProporcionSaldo3LP { get; set; }
    public double ProporcionSaldo4LP { get; set; }
    public int IdSubproducto { get; set; }
    public int IdMovimiento { get; set; }
}
