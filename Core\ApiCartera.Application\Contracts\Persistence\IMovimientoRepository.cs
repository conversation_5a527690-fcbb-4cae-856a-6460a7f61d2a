﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Entities;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.Movimientos.DTOs;

namespace ApiCartera.Application.Contracts.Persistence
{
    public interface IMovimientoRepository : IGenericRepository<Movimiento>
    {
        Task<CalculoBaneficioAC001DTO> ObtenerUltimo(long idSolicitud, DateTime fechaMovimientoActual);

        Task<List<ConsultaMovimientoDTO>> ConsultarMovimientoCredito(ParametrosConsultaMovimientoSolicitudDTO consultarMovimientoSolicitud);

        Task ActualizarMovimiento(MovimientoDTO movimiento);
    }
}
