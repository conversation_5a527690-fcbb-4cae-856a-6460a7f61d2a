﻿using ApiCartera.API.Config.GenericInjections;
using ApiCartera.API.Config.ORM;

namespace ApiCartera.API.Config;

public static class Injections
{
    public static void Inject(this WebApplicationBuilder webApplicationBuilder)
    {
        webApplicationBuilder.Services.SqlConfiguration(webApplicationBuilder.Configuration);
        webApplicationBuilder.Services.OracleConfiguration(webApplicationBuilder.Configuration);
        webApplicationBuilder.Services.AddRepositoryDependency();
        //webApplicationBuilder.Services.AddUsersDependency();
        webApplicationBuilder.Services.AddControllers();
        webApplicationBuilder.Services.AddEndpointsApiExplorer();
        webApplicationBuilder.Services.AddSwaggerGen();
    }
}
