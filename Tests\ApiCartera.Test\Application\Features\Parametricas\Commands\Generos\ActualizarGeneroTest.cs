using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Commands.Generos.ActualizarGenero;
using Sarlaft.Domain.Features.Parametricas.Entities;

namespace Sarlaft.Test.Application.Features.Parametricas.Commands.Generos
{
    public class ActualizarGeneroTest
    {
        [Fact]
        public async Task ActualizarGenero_Correcta()
        {
            var services = DependencyContainer.Get();
            var mockRepository = new Mock<IGenericRepository<Genero>>();
            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<Genero>()
                {
                    new(){ Id= 1, Nombre="profesional", Valor=1},
                });
            services.AddScoped(_ => mockRepository.Object);

            using (var scope = services.BuildServiceProvider().CreateScope())
            {
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var command = new ActualizarGeneroCommand(1,"tecnico", 1);

                // Ejecuta el comando utilizando MediatR
                var resultado = await mediator.Send(command);

                mockRepository.Verify(repo => repo.ActualizarAsync(It.IsAny<Genero>()));
                Assert.Equal(resultado, command.Id);
            }
        }

        [Fact]
        public async Task ActualizarGenero_Validaciones_YaExistente()
        {
            //Arrange
            var mockRepository = new Mock<IGenericRepository<Genero>>();
            var validator = new ActualizarGeneroCommandValidator(mockRepository.Object);
            var command = new ActualizarGeneroCommand(1,"m",1);

            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<Genero>()
                {
                    new(){ Id= 2, Nombre="m", Valor=1},
                });

            //Act
            var validatorResult = await validator.ValidateAsync(command);

            //Assert
            Assert.False(validatorResult.IsValid);
            Assert.True(validatorResult.Errors.Any());
            Assert.True(validatorResult.Errors.Count() == 2);
        }
    }
}