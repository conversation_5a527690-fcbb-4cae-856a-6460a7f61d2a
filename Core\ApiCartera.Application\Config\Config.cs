﻿using ApiCartera.Application.Behaviours;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Interfaces.implementacionFabrica;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Interfaces;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Application.Services.BeneficioAcuerdo001;
using ApiCartera.Application.Services.DivisionSaldos;
using ApiCartera.Application.Services.Movimientos;
using ApiCartera.Application.Services.Reportes;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using ApiCartera.Domain.Features.Movimientos.Services;
using ApiCartera.Domain.Features.Reportes.Services;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace ApiCartera.Application.Config;

public static class Config
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();
        services.AddAutoMapper(assembly);
        services.AddValidatorsFromAssembly(assembly);
        services.AddMediatR(config => config.RegisterServicesFromAssembly(assembly));

        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));
        services.AddScoped<IAfectacionesSaldoACapitalPorAdjudicacionService, AfectacionesSaldoACapitalPorAdjudicacionService>();
        services.AddScoped<ICalcularTasasCarteraService, CalcularTasasCarteraService>();
        services.AddScoped<IDetallesCreditoServices, DetallesCreditoServices>();
        services.AddScoped<ICalculadoraSaldosService, CalculadoraSaldos>();
        services.AddScoped<IAfectacionesService, AfectacionesSaldoACapitalReintegroService>();
        //services.AddScoped<ICalcularSaldosCarteraService, AfectacionesSaldoACapitalPorDesembolsoService>();
        services.AddScoped<ICalcularSaldosCarteraPorFinalizacionFechaPlanDePagoService, AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService>();
        services.AddScoped<IMovimientoSolicitudService, MovimientoSolicitudService>();
        services.AddScoped<ICalcularBeneficioAcuerdo001Service, CalcularBeneficioAcuerdo001Service>();
        services.AddScoped<IReporteAportesIESUniversidadesService, ReporteAportesIESUniversidadesService>();
        services.AddScoped<IReporteBeneficioAcuerdo001Service, ReporteBeneficioAcuerdo001Service>();
        services.AddScoped<IReporteContribucionesAportesIESService, ReporteContribucionesAportesIESService>();
        services.AddScoped<IReporteAlertasService, ReporteAlertasService>();
        services.AddScoped<IReporteValidacionDatosSolicitudService, ReporteValidacionDatosSolicitudService>();
        services.AddTransient<ISegmentacionBeneficioAcuerdo001Factory, SegmentacionBeneficioAcuerdo001QueryFactory>();
        services.AddScoped<ISegmentacionBeneficioAcuerdo001Service, SegmentacionBeneficioAcuerdo001Service>();
        services.AddScoped<IAfectacionesIdentificacionNuevosCreditos, AfectacionesIdentificacionNuevosCreditos>();

        services.AddScoped<IAfectacionSaldoReintegro, AfectacionSaldoCapitalReintegroService>();
			services.AddScoped<ICalcularSaldosCarteraAfectacionService, AfectacionesSaldosACapitalAfectacionService>();

        return services;
    }
}
