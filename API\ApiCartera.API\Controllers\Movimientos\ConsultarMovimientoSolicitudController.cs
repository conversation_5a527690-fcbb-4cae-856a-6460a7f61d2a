﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Movimientos.Queries;
using ApiCartera.Application.Features.Movimientos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Movimientos
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConsultarMovimientoSolicitudController(IMediator _mediator) : ControllerBase
    {
        [HttpGet("ObtenerMovimientosCreditos")]
        [ProducesResponseType<ResponseWrapper<List<ConsultaMovimientoVm>>>(StatusCodes.Status200OK)]
        public async Task<List<ConsultaMovimientoVm>> ObtenerMovimientosCreditos([FromQuery] ObtenerMovimientosCreditosQuery request) =>
            await _mediator.Send(request);
    }
}
