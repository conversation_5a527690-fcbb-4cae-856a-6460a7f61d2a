﻿namespace ApiCartera.Application.Features.Reportes.ViewModels;

public record BeneficioAcuerdo001Vm(
    int IdSolicitud,
    int IdSignature,
    string CodigoSubproducto,
    string Ccn,
    string Documento,
    string NombreCompleto,
    string Sisben,
    int Estrato,
    string Genero,
    string Ciudad,
    string Departamento,
    string CodigoDepartamento,
    int CodigoLinea,
    string Linea,
    string Sublinea,
    string CodSniesInst,
    string Ies,
    string TipoCartera,
    string ModalidadCredito,
    decimal CapitalExigible,
    decimal CapitalNoExigible,
    string TipoBeneficio,
    decimal Valor_Tasa_Contratacion,  
    string IpcContratacion,          
    decimal Valor_Tasa_Beneficio,     
    string Ipc_Beneficio,            
    decimal Saldo1,
    decimal Saldo2,
    decimal Saldo3,
    decimal Saldo4,
    string FechaGeneracion,
    decimal CapitalVigente,
    decimal CapitalVencido,
    int CuotasPendientesPlanPagos,
    decimal Calculo_Saldo1,         
    decimal Calculo_Saldo2,
    decimal Calculo_Saldo3,
    decimal Calculo_Saldo4
);
