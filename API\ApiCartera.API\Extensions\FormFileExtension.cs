﻿using Microsoft.AspNetCore.Mvc;
using ApiCartera.Application.Contracts.Storage;
using ApiCartera.Domain.Features.Files.Entities;
using System.Net.Mime;

namespace ApiCartera.API.Extensions
{
    public static class FormFileExtension
    {
        public static async Task<CustomFile> ToCustomFile(this IFormFile file)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                if (file == null)
                    return null;
                await file.CopyToAsync(memoryStream);

                byte[] fileBytes = memoryStream.ToArray();

                return new CustomFile(fileBytes, file.FileName, file.ContentType, file.Length);
            }            
        }

        public static FileContentResult ToFileContentResult(this CustomFile customFile)
        {
            var fileContent = customFile.Type ?? ApplicationTypes.OctectStream;
            var fileContentResult = new FileContentResult(customFile.File, fileContent)
            {
                FileDownloadName = customFile.FullName,
            };
            return fileContentResult;
        }
    }
}
