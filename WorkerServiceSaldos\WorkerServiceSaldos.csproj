<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-WorkerServiceSaldos-da1d3077-0007-43aa-8782-529d0d77ba50</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.4.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ApiCartera.Application\ApiCartera.Application.csproj" />
    <ProjectReference Include="..\Core\ApiCartera.Domain\ApiCartera.Domain.csproj" />
    <ProjectReference Include="..\Infrastructure\ApiCartera.Infrastructure\ApiCartera.Infrastructure.csproj" />
  </ItemGroup>
</Project>
