﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;

namespace ApiCartera.Infrastructure.Persitence.BusDataService;

public class ReporteAportesIESUniversidadesDataService(BusService busService) : GenericRepositoryBusDataService<Solicitud>(busService), IReporteAportesIESUniversidadesRepository
{
    public async Task<List<AportesIESUniversidadesDTO>> ObtenerReporte(DateTime desde, DateTime hasta, int pageSize, int pageNo)
    {
        int maxReintentos = Constants.MAX_REINTENTOS;
        int contador = Constants.CONTADOR;
        var url = $"reporteaportesiesuniversidades?desde={desde.ToString("dd-MM-yyyy")}&hasta={hasta.ToString("dd-MM-yyyy")}&pageSize={pageSize}&pageNo={pageNo}";
        RespuestaDataServiceDTO<List<AportesIESUniversidadesDTO>>? respuesta = null;

        while (contador <= maxReintentos)
        {
            try
            {
                respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<AportesIESUniversidadesDTO>>>(url);

                if (respuesta.Response == TiposRespuestaDataService.EXITOSA)
                {
                    return respuesta.Data;
                }                
            }
            catch
            {
                await Task.Delay(Constants.DELAY_REINTENTOS);
            }
            contador++;
        }

        var validaciones = new List<ValidationFailure>()
        {
            new()
            {
                PropertyName = $"{nameof(ObtenerReporte)} {Constants.DATASERVICE}",
                ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
            }
        };
        throw new ValidationException(validaciones);
    }
}
