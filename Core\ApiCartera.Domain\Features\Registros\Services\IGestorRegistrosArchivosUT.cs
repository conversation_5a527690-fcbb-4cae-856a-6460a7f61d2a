﻿using ApiCartera.Domain.Features.Registros.DTOs;

namespace ApiCartera.Domain.Features.Registros.Services
{
    public interface IGestorRegistrosArchivosUT
    {
        Task ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, string oracleConnectionString);
        Task<ResultadoEjecucion<long>> ProcesarRegistrosMovimientos(SftpSettingsDTO sftpSettings, string oracleConnectionString);
    }
}
