﻿using ICETEX.Librerias.BusServicios;
using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Usuarios.Entities;
using ApiCartera.Infrastructure.Persitence.BusDataService;

namespace ApiCartera.Infrastructure.Persitence.DataService
{
    public class UsuarioPermitidoDataService(BusService busService) : GenericRepositoryBusDataService<UsuarioPermitido>(busService), IUsuarioPermitidoRepository
    {
    }
}
