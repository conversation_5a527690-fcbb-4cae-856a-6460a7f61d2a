﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Reportes.DTOs
{
    public class ContribucionesAportesIESDTO
    {
        [JsonProperty("idSolicitud")]
        public int IdSolicitud { get; set; }

        [JsonProperty("idSignature")]
        public int? IdSignature { get; set; }

        [JsonProperty("linea")]
        public string? Linea { get; set; }

        [JsonProperty("sublinea")]
        public string? Sublinea { get; set; }

        [JsonProperty("codigoSnies")]
        public string? CodigoSNIES { get; set; }

        [JsonProperty("tipoCartera")]
        public string? TipoCartera { get; set; }

        [JsonProperty("modalidadCredito")]
        public string? ModalidadCredito { get; set; }

        [JsonProperty("periodoGiro")]
        public string? PeriodoGiro { get; set; }

        [JsonProperty("anexo")]
        public string? Anexo { get; set; }

        [JsonProperty("porcentajeAsumeIes")]
        public double? PorcentajeAsumeIES { get; set; }

        [JsonProperty("porcentajeAsumeIcetex")]
        public double? PorcentajeAsumeIcetex { get; set; }

        [JsonProperty("fechaGiro")]
        public DateTime? FechaGiro { get; set; }

        [JsonProperty("fechaGeneracion")]
        public DateTime? FechaGeneracion { get; set; }

        [JsonProperty("diasCalculo")]
        public double DiasCalculo { get; set; }

        [JsonProperty("capitalExigible")]
        public double? CapitalExigible { get; set; }

        [JsonProperty("capitalNoExigible")]
        public double? CapitalNoExigible { get; set; }

        [JsonProperty("saldo1")]
        public double? Saldo1 { get; set; }

        [JsonProperty("ajusteSaldo1")]
        public double? AjusteSaldo1 { get; set; }

        [JsonProperty("saldo2")]
        public double? Saldo2 { get; set; }

        [JsonProperty("ajusteSaldo2")]
        public double? AjusteSaldo2 { get; set; }

        [JsonProperty("saldo3")]
        public double? Saldo3 { get; set; }

        [JsonProperty("ajusteSaldo3")]
        public double? AjusteSaldo3 { get; set; }

        [JsonProperty("saldo4")]
        public double? Saldo4 { get; set; }

        [JsonProperty("ajusteSaldo4")]
        public double? AjusteSaldo4 { get; set; }

        [JsonProperty("valorTasaContratacion")]
        public double? ValorTasaContratacion { get; set; }

        [JsonProperty("ipcContratacion")]
        public double? IPCContratacion { get; set; }

        [JsonProperty("tasaMinimaSaldo1LargoPlazo")]
        public double? TasaMinimaSaldo1LargoPlazo { get; set; }

        [JsonProperty("tasaMinimaSaldo2LargoPlazo")]
        public double? TasaMinimaSaldo2LargoPlazo { get; set; }

        [JsonProperty("tasaMinimaSaldo3LargoPlazo")]
        public double? TasaMinimaSaldo3LargoPlazo { get; set; }

        [JsonProperty("tasaMinimaSaldo4LargoPlazo")]
        public double? TasaMinimaSaldo4LargoPlazo { get; set; }

        [JsonProperty("tasaMaximaSaldo1LargoPlazo")]
        public double? TasaMaximaSaldo1LargoPlazo { get; set; }

        [JsonProperty("tasaMaximaSaldo2LargoPlazo")]
        public double? TasaMaximaSaldo2LargoPlazo { get; set; }

        [JsonProperty("tasaMaximaSaldo3LargoPlazo")]
        public double? TasaMaximaSaldo3LargoPlazo { get; set; }

        [JsonProperty("tasaMaximaSaldo4LargoPlazo")]
        public double? TasaMaximaSaldo4LargoPlazo { get; set; }

        [JsonProperty("factor1")]
        public double? Factor1 { get; set; }

        [JsonProperty("factor2")]
        public double? Factor2 { get; set; }

        [JsonProperty("factor3")]
        public double? Factor3 { get; set; }

        [JsonProperty("resultadoFactor2Factor3")]
        public double? ResultadoFactor2Factor3 { get; set; }

        [JsonProperty("valorAporteAplicado")]
        public double? ValorAporteAplicado { get; set; }

        [JsonProperty("valorAporteIes")]
        public double? ValorAporteIES { get; set; }

        [JsonProperty("valorAjuste")]
        public double? ValorAjuste { get; set; }

        [JsonProperty("validadorAjuste")]
        public string? ValidadorAjuste { get; set; }

        [JsonProperty("saldoAporte")]
        public double? SaldoAporte { get; set; }

        [JsonProperty("fechaFinEjecucion")]
        public DateTime? FechaFinEjecucion { get; set; }
    }
}
