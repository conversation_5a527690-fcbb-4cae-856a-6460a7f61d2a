﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.BeneficioAcuerdo001;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.DTOs;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.BeneficioAcuerdo001;

public class CalcularBeneficioAcuerdo001ServiceTest
{
    Mock<ICalcularTasasCarteraService>? _calcularTasasCarteraServiceMock;
    Mock<ISaldosCarteraRepository>? _saldosCarteraRepositoryMock;
    Mock<ISolicitudCarteraActivaRepository>? _solicitudCarteraActivaRepositoryMock;
    Mock<IInteresesLiquidadosRepository>? _interesesLiquidadosRepositoryMock;
    Mock<IMovimientoRepository>? _movimientoRepositoryMock;
    CalcularBeneficioAcuerdo001Service _obtenerBeneficio;
    CalculoBaneficioAC001DTO _movimiento;
    CalculoBaneficioAC001DTO? _movimientoUno;
    CalculoBaneficioAC001DTO? _movimientoDos;
    SolicitudDTO? _solicitudDTO;

    public CalcularBeneficioAcuerdo001ServiceTest()
    {
        _solicitudDTO = new SolicitudDTO();
        _movimiento = new CalculoBaneficioAC001DTO { IdMovimiento = 285, FechaMovimiento = new DateTime(2025, 02, 08), IdSolicitud = 5, IdSubproducto = 1 };
        _movimientoUno = new CalculoBaneficioAC001DTO { IdMovimiento = 285, FechaMovimiento = new DateTime(2025, 02, 01), IdSolicitud = 5, IdSubproducto = 1, IdTipoCartera = 1};
        _movimientoDos = new CalculoBaneficioAC001DTO { IdMovimiento = 285, FechaMovimiento = new DateTime(2025, 02, 15), IdSolicitud = 5, IdSubproducto = 2 , IdTipoCartera = 1};        
        _calcularTasasCarteraServiceMock = new Mock<ICalcularTasasCarteraService>();
        _saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
        _solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
        _interesesLiquidadosRepositoryMock = new Mock<IInteresesLiquidadosRepository>();
        _movimientoRepositoryMock = new Mock<IMovimientoRepository>();

        _obtenerBeneficio = new CalcularBeneficioAcuerdo001Service(
            _saldosCarteraRepositoryMock.Object,
       _solicitudCarteraActivaRepositoryMock.Object,
_interesesLiquidadosRepositoryMock.Object,
_movimientoRepositoryMock.Object
        );
    }

    [Fact]
    public async Task CalcularBeneficio_FechaMovimientoEsPrimerDiaDeMes()
    {
        InteresesLiquidadosDTO? result = await _obtenerBeneficio.CalcularBeneficio(_movimientoUno!);
        Assert.NotNull(result);
        Assert.IsType<InteresesLiquidadosDTO>(result);
        Assert.Equal(0, result.Id);
    }

    [Theory]
    [InlineData("AIES - APORTES IES", 1237.25)]
    [InlineData("OTRA MARCA", 2474.5)]
    public async Task CalcularBeneficioAporteIesEstudios(string marca, decimal beneficioAC001)
    {
        InteresesLiquidadosDTO? interesesLiquidadosDTO = new InteresesLiquidadosDTO();
        var tasas = new TasasSaldosCarteraDTO
        { TasaMinimaSaldo1 = 0.0707, TasaMaximaSaldo2 = 0.1377, ProporcionSaldo1 = 0.9, ProporcionSaldo2 = 0.9 };
        var saldoCP = new SaldosCarteraDTO
        { Saldo1 = 1000000, Saldo2 = 2000000};
        var saldoLP = new SaldosCarteraDTO
        { Saldo1 = 1000000, Saldo2 = 2000000 };
        _solicitudDTO!.Marca = marca;

        _solicitudCarteraActivaRepositoryMock!.Setup(x => x.ObtenerSolicitudPorId(_movimientoDos!.IdSolicitud))!.ReturnsAsync(_solicitudDTO);

        _calcularTasasCarteraServiceMock!.Setup(x => x.CalcularTasasYProporcionSaldos(_movimientoDos!.IdSolicitud, _movimientoDos.IdSubproducto)).ReturnsAsync(tasas);

        _saldosCarteraRepositoryMock!.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(_movimientoDos!.IdSolicitud, 1)).ReturnsAsync(saldoCP);

        _saldosCarteraRepositoryMock!.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(_movimientoDos!.IdSolicitud, 2)).ReturnsAsync(saldoLP);

        _movimientoRepositoryMock!.Setup(x => x.ObtenerUltimo(_movimientoDos!.IdSolicitud, _movimientoDos!.FechaMovimiento)).ReturnsAsync(_movimiento);

        InteresesLiquidadosDTO? result = await _obtenerBeneficio.CalcularBeneficio(_movimientoDos!);

        Assert.NotNull(result);
        Assert.IsType<InteresesLiquidadosDTO>(result);
        Assert.Equal((double)beneficioAC001, Math.Round(result.BeneficioAcuerdo001Saldo1, 2));
    }

    [Fact]
    public void CalcularBeneficioAc001EtapaEstudiosSaldo1()
    {
        InteresesLiquidadosDTO interesesLiquidados = new InteresesLiquidadosDTO();
        SolicitudDTO solicitud = new SolicitudDTO { Marca = "AIES - APORTES IES" };
        TasasSaldosCarteraDTO tasasCartera = new TasasSaldosCarteraDTO { TasaMinimaSaldo1 = 0.05, ProporcionSaldo1 = 0.7 };
        SaldosCarteraDTO saldosCarteraLP = new SaldosCarteraDTO { Saldo1 = 1000000 };
        SaldosCarteraDTO saldosCarteraCP = new SaldosCarteraDTO { Saldo1 = 1000000 };
        int diasSinMovimiento = 6;

        _obtenerBeneficio.CalcularBeneficioAcuerdo001Estudios(interesesLiquidados, solicitud, tasasCartera,saldosCarteraCP, saldosCarteraLP, diasSinMovimiento);

        Assert.Equal(583.33, Math.Round(interesesLiquidados.BeneficioAcuerdo001Saldo1, 2));
    }

    [Fact]
    public void CalcularAjusteCiesSaldo2()
    {
        InteresesLiquidadosDTO interesesLiquidados = new InteresesLiquidadosDTO();
        SolicitudDTO solicitud = new SolicitudDTO { Marca = "AIES - APORTES IES" };
        TasasSaldosCarteraDTO tasasCartera = new TasasSaldosCarteraDTO { TasaMinimaSaldo2 = 0.05, ProporcionSaldo2 = 0.7 };
        SaldosCarteraDTO saldosCarteraLP = new SaldosCarteraDTO { Saldo2 = 1000000 };
        int diasSinMovimiento = 6;

        _obtenerBeneficio.CalcularAjusteCies(interesesLiquidados, solicitud, tasasCartera, saldosCarteraLP, diasSinMovimiento);

        Assert.Equal(583.33, Math.Round(interesesLiquidados.AjusteCIESSaldo2, 2));
    }

    [Fact]
    public void CalcularAjusteAiesSaldo3()
    {
        InteresesLiquidadosDTO interesesLiquidados = new InteresesLiquidadosDTO();
        SolicitudDTO solicitud = new SolicitudDTO { Marca = "AIES - APORTES IES" };
        TasasSaldosCarteraDTO tasasCartera = new TasasSaldosCarteraDTO { TasaMinimaSaldo3 = 0.05, ProporcionSaldo3 = 0.7 };
        SaldosCarteraDTO saldosCarteraLP = new SaldosCarteraDTO { Saldo3 = 1000000 };
        int diasSinMovimiento = 6;

        _obtenerBeneficio.CalcularAjusteAies(interesesLiquidados, solicitud, tasasCartera, saldosCarteraLP, diasSinMovimiento);

        Assert.Equal(583.33, Math.Round(interesesLiquidados.AjusteAportesIESSaldo3, 2));
    }
}
