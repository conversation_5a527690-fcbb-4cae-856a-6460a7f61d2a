﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models;

[EntityName("VM_REPORTE_ALERTAS")]
public class MvReporteAlertasEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int? IdSolicitud { get; set; }
    public long? Num_Resolucion { get; set; }
    public int? Year { get; set; }
    public int? Periodo { get; set; }
    public int? Identificacion_Beneficio { get; set; }
    public string? Causa_Alerta { get; set; }
    public string? Estado_Ajuste { get; set; }
    public string? Fecha_Ajuste { get; set; }
    public decimal? Valor_Contribucion { get; set; }
    public string? Linea { get; set; }
    public string? Sublinea { get; set; }
    public string? CodSnies_Inst { get; set; }
    public string? Ies { get; set; }
    public string? Convenio { get; set; }
    public string? TipoGiro { get; set; }
    public string? Modalidad_Credito { get; set; }
    public string? Estado_Actual { get; set; }
    public string? EsComplementario { get; set; }
    public DateTime? Fecha { get; set; }
}
