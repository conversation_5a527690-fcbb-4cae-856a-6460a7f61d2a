﻿using FluentValidation;

namespace ApiCartera.Application.Exceptions
{
    public static class FluentValidationExtensions
    {
        public static void ValidateAndThrowCustomValidationException<T>(this IValidator<T> validator, T instance)
        {
            var res = validator.Validate(instance);

            if (!res.IsValid)
            {
                throw new ValidationException(res.Errors);
            }
        }
    }
}
