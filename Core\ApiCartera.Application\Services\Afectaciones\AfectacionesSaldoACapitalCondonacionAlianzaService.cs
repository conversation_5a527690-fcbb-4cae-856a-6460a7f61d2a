﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalCondonacionAlianzaService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            ValidarMovimientoAfectacion(movimiento);
            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCartera = await saldosCarteraRepository
            .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);

            ValidarSaldosCartera(saldosCartera, movimiento.IdIcetex);
            var nuevosSaldosCartera = (SaldosCarteraDTO) saldosCartera.Clone();
          
            //Ajuste realizado el 02/10/2024
            //se permitirá realizar el proceso solo cuando movimiento.IdSubproducto sea igual a 2, 6,9,10,39
            if (idSubproducto != 1) {
                nuevosSaldosCartera = this.DisminuirSaldosProgresivamente(nuevosSaldosCartera, movimiento.MontoMovimientoCapital);
                nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
                nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
                var saldos = this.MapearSaldosCarteraAfectaciones(nuevosSaldosCartera);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldos);
            }


            return nuevosSaldosCartera;
            
        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }
    }
}
