﻿namespace ApiCartera.Domain.Features.DivisionSaldos.DTOs;

public class DesembolsoDTO
{
    public long IdSolicitud { get; set; }//
    public int YearGiro { get; set; }//
    public int SemestreGiro { get; set; }//
    public DateTime? Fecha { get; set; }
    public double TotalGirar { get; set; }
    public string ModalidadRubro { get; set; } = string.Empty;
    public double PorcentajeCarteraPlan { get; set; }
    public int? Periodicidad { get; set; }
    public string? Marca { get; set; }
    public double? ValorAportesIES { get; set; }
    public int? NoRelacion { get; set; }
    public string? Estado { get; set; }
    public int Puntos { get; set; }
}
