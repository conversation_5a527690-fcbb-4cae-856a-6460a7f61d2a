﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalReversionReintegroServiceTests
    {
        public AfectacionesSaldoACapitalReversionReintegroServiceTests()
        {
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosReintegros), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoSinBeneficiosVigenciaAnteriorA2024_RetornaSaldo1Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 9600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 14400000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2023-02-02"),
                TotalGirar = 24000000,
                Marca = "NA - NO APLICA BENEFICIO",
                ValorAportesIES = null,
                NoRelacion = 35000,
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));
            var afectacionesSaldoACapitalReversionReintegroService = new AfectacionesSaldoACapitalReversionReintegroService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionReintegroService.CalcularSaldosCartera(movimiento);
            var saldo1Aumentado = respuestaSaldosCartera.Saldo1 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Aumentado, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosReintegros), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoSinBeneficiosVigenciaPosteriorA2023_RetornaSaldo4Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 0,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 9600000,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 0,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 14400000,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "NA - NO APLICA BENEFICIO",
                ValorAportesIES = null,
                NoRelacion = 35000,
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));
            var afectacionesSaldoACapitalReversionReintegroService = new AfectacionesSaldoACapitalReversionReintegroService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionReintegroService.CalcularSaldosCartera(movimiento);
            var saldo4Aumentado = respuestaSaldosCartera.Saldo4 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(respuestaSaldosCartera.Saldo1, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(saldo4Aumentado, resultado.Saldo4);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosReintegros), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoCIES_RetornaSaldo2Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 0,
                Saldo2 = 544892.52,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 29456561.67,
                Saldo2 = 7262220,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2023-02-02"),
                TotalGirar = 5187300,
                Marca = "CIES - CONTRIBUCIÓN IES",
                ValorAportesIES = null,
                NoRelacion = 35000,
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));
            var afectacionesSaldoACapitalReversionReintegroService = new AfectacionesSaldoACapitalReversionReintegroService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionReintegroService.CalcularSaldosCartera(movimiento);
            var saldo2Aumentado = respuestaSaldosCartera.Saldo2 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(respuestaSaldosCartera.Saldo1, resultado.Saldo1);
            Assert.Equal(saldo2Aumentado, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosReintegros), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoAPORTESIES_RetornaSaldo3Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 0,
                Saldo2 = 0,
                Saldo3 = 544892.52,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 29456561.67,
                Saldo2 = 0,
                Saldo3 = 7262220,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2023-02-02"),
                TotalGirar = 5187300,
                Marca = "AIES - APORTES IES",
                ValorAportesIES = 5187300,
                NoRelacion = 35000,
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));
            var afectacionesSaldoACapitalReversionReintegroService = new AfectacionesSaldoACapitalReversionReintegroService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionReintegroService.CalcularSaldosCartera(movimiento);
            var saldo3Aumentado = respuestaSaldosCartera.Saldo3 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(respuestaSaldosCartera.Saldo1, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(saldo3Aumentado, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
    }
}
