﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Entities;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class TasaCarteraDataService(BusService busService, IConfiguration configuration) :
        GenericCustomRepositoryBusDataService<SaldosCartera>(busService, configuration), ITasaCarteraRepository
    {
        public async Task GuardarTasas(TasasSaldosCartera tasaCartera)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            bool validacion = false;
            string? url = "registro-tasascartera";
            RespuestaDataServiceDTO<dynamic>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Post<RespuestaDataServiceDTO<dynamic>, dynamic>(url, tasaCartera);
                    contador = maxReintentos + 1;
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                    contador++;
                    if (contador == 4)
                        validacion = true;
                }
            }
            if (validacion)
            {
                List<ValidationFailure>? validaciones = new List<ValidationFailure>()
            {
                new()
                {
                    PropertyName = $"{nameof(GuardarTasas)} {Constants.DATASERVICE}",
                    ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                }
            };
                throw new ValidationException(validaciones);
            }            
        }

        public async Task<TasasSaldosCarteraDTO> ObtenerTasasPorIdSolicitudYSubproducto(long idSolicitud, string codigoSubproducto)
        {
            var url = $"obtenertasassaldos?idSolicitud={idSolicitud}&codigoSubproducto={codigoSubproducto}";

            var respuestaObtenerTasas = await busService.Services.Generico.Get<RespuestaDataServiceDTO<TasasSaldosCarteraDTO>>(url);

            if (respuestaObtenerTasas.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(idSolicitud)} - {nameof(codigoSubproducto)}",
                        ErrorMessage = respuestaObtenerTasas.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaObtenerTasas.Data;
        }
    }
}
