﻿using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;

namespace ApiCartera.Application.Contracts.Persistence;

public interface ISolicitudCarteraActivaRepository : IGenericRepository<SaldosSolicitudCartera>
{
    Task<List<SaldosSolicitudCarteraDTO>> ObtenerPorIdSolicitud(long idSolicitud);
    Task<List<DesembolsoDTO>> ObtenerDesembolsosPorIdSolicitud(long idSolicitud);
    Task<DesembolsoDTO> ObtenerDesembolsoPorNoRelacion(long idSolicitud, int noRelacion);
    Task<DesembolsoDTO> ObtenerDesembolsoPorNoRelacionGenerica(long idSolicitud, int noRelacion, string modalidadRubro);
    Task GuardarSaldos(SaldosCartera saldosSolicitud);
    Task<SolicitudDTO> ObtenerSolicitudPorId(long idSolicitud, DateTime FechaMovimiento);
    Task<List<int>> ObtenerSegmentacionBeneficioAcuerdo001();
}