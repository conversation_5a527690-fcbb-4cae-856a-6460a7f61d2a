﻿using ApiCartera.Domain.Features.Shared.DTOs;
using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Domain.Features.Shared.Services;

public class InteresesLiquidadosService
{
    public static InteresesLiquidados MapearInteresesLiquidados(InteresesLiquidadosDTO interesesLiquidados)
    {
        return new InteresesLiquidados()
        {
            IdSaldosSolicitudCartera = interesesLiquidados.IdSaldosSolicitudCartera,
            IdMovimiento = interesesLiquidados.IdMovimiento,
            BeneficioAcuerdo001Saldo1 = interesesLiquidados.BeneficioAcuerdo001Saldo1,
            AjusteCIESSaldo2 = interesesLiquidados.AjusteCIESSaldo2,
            AjusteAportesIESSaldo3 = interesesLiquidados.AjusteAportesIESSaldo3
        };
    }
}
