﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{

    public class ReporteValidacionDatosSolicitudDataService(BusService busService) : GenericRepositoryBusDataService<Solicitud>(busService), IReporteValidacionDatosSolicitudRepository
    {
        public async Task<List<ValidacionDatosSolicitudDTO>> ObtenerReporte(DateTime desde, DateTime hasta)
        {
            var url = $"reportevalidacionsaldos?desde={desde.ToString("yyyy-MM-dd")}&hasta={hasta.ToString("yyyy-MM-dd")}";

            var respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<ValidacionDatosSolicitudDTO>>>(url);

            if (respuesta.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(desde)} y {nameof(hasta)}",
                        ErrorMessage = respuesta.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuesta.Data;

        }
    }
}
