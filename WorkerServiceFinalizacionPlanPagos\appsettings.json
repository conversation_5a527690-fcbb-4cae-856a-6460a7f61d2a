{"ApiCarteraUrl": "http://10.1.18.124:7000/api/", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "/Logs/WorkerServicesFinalizacionPlanPagos-.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "Worker Service Finalizacion Plan Pagos", "Environment": "Development"}}}