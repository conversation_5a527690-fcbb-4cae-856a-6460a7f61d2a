﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Contracts.Storage;
using ApiCartera.Domain.Features.Usuarios.Entities;

namespace ApiCartera.Infrastructure.Persitence.Memory
{
    public class UsuarioPermitidoMemory : GenericRepositoryMemory<UsuarioPermitido> , IUsuarioPermitidoRepository
    {
        public UsuarioPermitidoMemory(IStorageService storageService)
        {
            if (!Repository.Any())
            {
                Repository.Add(new UsuarioPermitido()
                {
                    Email = "<EMAIL>",
                    Id = 1
                });
            }
        }

       
    }
}
