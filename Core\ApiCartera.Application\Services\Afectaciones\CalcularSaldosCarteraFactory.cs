﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.Afectaciones.Constants;
using ApiCartera.Domain.Features.Afectaciones.Services;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class CalcularSaldosCarteraFactory
    {
        private readonly Dictionary<string, ICalcularSaldosCarteraService> _calculadoresSaldosCartera;

        public CalcularSaldosCarteraFactory(
            ISaldosCarteraRepository saldosCarteraRepository,
            ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository)
        {
            _calculadoresSaldosCartera = new Dictionary<string, ICalcularSaldosCarteraService>
            {
                { TiposNovedadAfectacion.REINTEGRO, new AfectacionesSaldoACapitalReintegroService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.REVERSION_REINTEGRO, new AfectacionesSaldoACapitalReversionReintegroService(saldosCarteraRepository, solicitudCarteraActivaRepository) },                                
                { TiposNovedadAfectacion.REVERSION_RECAUDO_ASOBANCARIA, new AfectacionesSaldoACapitalReversionRecaudoService(saldosCarteraRepository, solicitudCarteraActivaRepository) },
                { TiposNovedadAfectacion.REVERSION_RECAUDO_MANUAL, new AfectacionesSaldoACapitalReversionRecaudoService(saldosCarteraRepository, solicitudCarteraActivaRepository) }
                
            };
        }

        public ICalcularSaldosCarteraService ObtenerCalculadoraSaldos(string codigoNovedad)
        {
            if (_calculadoresSaldosCartera.TryGetValue(codigoNovedad, out var calcularSaldosCartera))
            {
                return calcularSaldosCartera;
            }

            var validaciones = new List<ValidationFailure>()
            {
                new() 
                { 
                    PropertyName = nameof(codigoNovedad), 
                    ErrorMessage = $"No se encontró un código de novedad asociado a este valor: {codigoNovedad}" 
                }
            };

            throw new ValidationException(validaciones);
        }
    }
}
