﻿using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation;

namespace ApiCartera.Application.Validators
{
    public class DesembolsoValidator : AbstractValidator<DesembolsoDTO>
    {
        public DesembolsoValidator()
        {
            RuleFor(x => x.TotalGirar)
                .NotNull()
                .GreaterThan(0)
                .WithMessage(ValidationMessages.DesembolsoTotalGirar);
            RuleFor(x => x.Periodicidad).NotNull().NotEmpty().WithMessage(ValidationMessages.DesembolsoPeriodicidad);

        }
    }
}
