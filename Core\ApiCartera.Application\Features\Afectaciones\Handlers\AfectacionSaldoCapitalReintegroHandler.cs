﻿using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionSaldoCapitalReintegroHandler(
        IAfectacionSaldoReintegro _afectacionSaldoReintegro,
        IMapper _mapper) : IRequestHandler<AfectacionSaldoCapitalReintegroCommand, SaldoCarteraVm>
    {
        public async Task<SaldoCarteraVm> Handle(AfectacionSaldoCapitalReintegroCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _afectacionSaldoReintegro.AfectacionSaldoReintegro(request.dataSaldoReintegro);
            return _mapper.Map<SaldoCarteraVm>(resultado);
        }
    }
}
