﻿namespace ApiCartera.Application.Features.Movimientos.ViewModels
{
    public class ConsultaMovimientoVm
    {
        public long IdSolicitud { get; set; }
        public long IdSignature { get; set; }
        public int Documento { get; set; }
        public string Linea { get; set; }
        public string Sublinea { get; set; }
        public string TipoBeneficio { get; set; }
        public double SaldoCapitalVigente { get; set; }
        public double CapitalVigenteExigible { get; set; }
        public double CapitalVigenteNoExigible { get; set; }
        public DateTime FechaInicio { get; set; }
        public DateTime FechaFin { get; set; }
        public double DiasCalculo { get; set; }
        public string Descripcion { get; set; }
        public DateTime FechaCorte { get; set; }
        public double Saldo1 { get; set; }
        public double Saldo2 { get; set; }
        public double Saldo3 { get; set; }
        public double Saldo4 { get; set; }
        public double TasaContratacion { get; set; }
        public double TasaMinimaSaldo1 { get; set; }
        public double TasaMinimaSaldo2 { get; set; }
        public double TasaMinimaSaldo3 { get; set; }
        public double TasaMinimaSaldo4 { get; set; }
        public double TasaMaximaSaldo1 { get; set; }
        public double TasaMaximaSaldo2 { get; set; }
        public double TasaMaximaSaldo3 { get; set; }
        public double TasaMaximaSaldo4 { get; set; }
        public double CalculoBeneficioAcuerdo001Saldo1 { get; set; }
        public double CalculoBeneficioAcuerdo001Saldo2 { get; set; }
        public double CalculoBeneficioAcuerdo001Saldo3 { get; set; }
        public double CalculoBeneficioAcuerdo001Saldo4 { get; set; }
        public double CalculoAjusteCIESSaldo1 { get; set; }
        public double CalculoAjusteCIESSaldo2 { get; set; }
        public double CalculoAjusteCIESSaldo3 { get; set; }
        public double CalculoAjusteCIESSaldo4 { get; set; }
        public double CalculoAjusteAportesIESSaldo1 { get; set; }
        public double CalculoAjusteAportesIESSaldo2 { get; set; }
        public double CalculoAjusteAportesIESSaldo3 { get; set; }
        public double CalculoAjusteAportesIESSaldo4 { get; set; }
    }
}
