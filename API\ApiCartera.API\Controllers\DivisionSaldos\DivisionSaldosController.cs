﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Application.Features.DivisionSaldos.Queries;

namespace ApiCartera.API.Controllers;

[Route("api/[controller]")]
[ApiController]
public class DivisionSaldosController(IMediator _mediator) : ControllerBase
{
    [HttpPost("ObtenerDivisionSaldosCarteraIniciales")]
    [ProducesResponseType<ResponseWrapper<List<SaldoCarteraVm>>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> ObtenerSaldosCarteraIniciales([FromBody] CalcularSaldosQuery request)
    {
        var resultado = await _mediator.Send(request);
        return Ok();
    }
}
