﻿namespace ApiCartera.Domain.Features.DivisionSaldos.DTOs
{
    public class SaldosSolicitudCarteraDTO
    {
        public int Id { get; set; }
        public string TipoCartera { get; set; }
        public long IdSolicitud { get; set; }
        public long IdSignature { get; set; }
        public long IdSolicitante { get; set; }
        public int IdSubproducto { get; set; }
        public string CodigoSubproducto { get; set; }
        public double SaldoCapitalVigente { get; set; }
        public double SaldoTotalCapital { get; set; }
        public double SaldoCapitalVencido { get; set; }
        public int CuotasPendientesPorPagar { get; set; }
        public double PorcentajeCarteraPlan { get; set; }
        public int Periodicidad { get; set; }
        public double SumatoriaDesembolsosContribucionIES { get; set; }
        public double SumatoriaDesembolsosAportesIES { get; set; }
        public double SumatoriaDesembolsosSinBeneficiosSinFinalizarPlanPagos { get; set; }
        public double SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado { get; set; }
    }
}
