﻿using Sarlaft.Domain.Exceptions;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Parametricas.Enums;
using Sarlaft.Domain.Features.Parametricas.Messages;

namespace Sarlaft.Test.Domain.Features.Parametricas
{
    public class IndiceLAFTTest
    {
        private readonly List<DelitoDetalle> delitoDetalles = new List<DelitoDetalle>
            {
                new DelitoDetalle
                {
                    Cantidad = 2,
                    Delito = new Delito{ TipoDelito = TipoDelitoEnum.LA_FT}
                },
                new DelitoDetalle
                {
                    Cantidad = 15,
                    Delito = new Delito{ TipoDelito = TipoDelitoEnum.FT}
                },
                new DelitoDetalle
                {
                    Cantidad = 1,
                    Delito = new Delito{ TipoDelito = TipoDelitoEnum.LA}
                },
            };

        [Fact]
        public void ObtenerIndiceLAFT_Correcta()
        {
            var ciudad = new Ciudad
            {
                Poblacion = 20018,
                Nombre = "Bucaramanga",
                Departamento = new Departamento
                {
                    Nombre = "Santander",
                    CodigoDANE = "02"
                }
            };

            var indiceLAFT = IndiceLAFTCiudad.Create(ciudad, delitoDetalles);

            Assert.Equal(3, indiceLAFT.ConteoLA);
            Assert.Equal(17, indiceLAFT.ConteoFT);
            Assert.Equal(1.5, indiceLAFT.IndiceLA,0.1);
            Assert.Equal(8.5, indiceLAFT.IndiceFT,0.1);
        }

        [Fact]
        public void ObtenerIndiceLAFT_CiudadSinNombre()
        {
            var ciudad = new Ciudad
            {
                Poblacion = 20018,
            };

            var exception = Assert.Throws<NullException>(() => IndiceLAFTCiudad.Create(ciudad, delitoDetalles));
            Assert.Equal(IndiceLAFTMessages.CiudadNombreVacia, exception.Message);
        }

        [Fact]
        public void ObtenerIndiceLAFT_DepartamentoNulo()
        {
            var ciudad = new Ciudad
            {
                Poblacion = 20018,
                Nombre = "Bucaramanga",
            };

            var exception = Assert.Throws<NullException>(() => IndiceLAFTCiudad.Create(ciudad, delitoDetalles));
            Assert.Equal(IndiceLAFTMessages.DepartamentoNulo, exception.Message);
        }

    }
}
