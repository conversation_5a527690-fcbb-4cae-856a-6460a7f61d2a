﻿using CsvHelper.Configuration.Attributes;

namespace ApiCartera.Domain.Features.Registros.Models
{
    public class RegistroMovimiento
    {
        [Name("ID.ICETEX   ")]
        public string IdIcetex { get; set; }
        [Name("ID.SIGNATURE")]
        public string IdSignature { get; set; }
        [Name("SDO TOTAL CAPITAL VIGENTE")]
        public double SaldoCapitalVigente { get; set; }
        [Name("FECHA EFECTIVAMOVIMIENTO")]
        public string FechaEfectivaMovimiento { get; set; }
        [Name("FECHA POSTEO")]
        public string FechaPosteo { get; set; }
        [Name("MONTO MOVIMIENTO DE CAPITAL")]
        public double MontoMovimientoDeCapital { get; set; }
        [Name("CODIGO TIPO MOVTO DE CAPITAL")]
        public string CodigoTipoMovimientoDeCapital { get; set; }
        [Name("NOMBRE TIPO MOVTO DE CAPITAL")]
        public string NombreTipoMovimientoDeCapital { get; set; }
        [Name("COD. SUBPRODUCTO X OBLIGACION")]
        public string CodigoSubproductoPorObligacion { get; set; }
        [Name("SALDO TOTAL DE CAPITAL")]
        public double SaldoTotalCapital { get; set; }
        [Name("SALDO CAPITAL VENCIDO")]
        public double SaldoCapitalVencido { get; set; }
        [Name("DESCRIPCION MOVTO MEMO")]
        public string DescripcionMovimiento { get; set; }
        [Name("COD.NOVEDAD")]
        public string CodigoNovedad { get; set; }
        [Name("TIPO CARTERA")]
        public string TipoCartera { get; set; }
        [Name("CUOTAS PENDIENTES POR PAGAR")]
        public int CuotasPendientesPorPagar { get; set; }
    }
}
