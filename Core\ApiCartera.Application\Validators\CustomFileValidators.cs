﻿using FluentValidation;
using ApiCartera.Domain.Features.Files.Constants;
using ApiCartera.Domain.Features.Files.Entities;
using ApiCartera.Domain.Features.Files.Enums;
using ApiCartera.Domain.Features.Files.Extensions;
using ApiCartera.Domain.Features.Files.Messages;

namespace ApiCartera.Application.Validators
{
    public static class CustomFileValidatorsExtension
    {
        public static IRuleBuilderOptions<T, CustomFile> MaximunSize<T>(this IRuleBuilder<T, CustomFile> ruleBuilder, int maxSize, SizesScale sizesScale)
        {
            return ruleBuilder.Must(file => file.SizeBytes.GetValueOrDefault().ConvertBytesTo(sizesScale) < maxSize)
                .WithMessage(string.Format(FileValidationMessages.MaximoPesoArchivo, maxSize, sizesScale.ToString()));
        }
        public static IRuleBuilderOptions<T, CustomFile> FileTypesAllowed<T>(this IRuleBuilder<T, CustomFile> ruleBuilder, List<FormatFile> formatFiles)
        {
            var applicationTypes = formatFiles.GetApplicationTypes();
            return ruleBuilder.Must(file => applicationTypes.Any(app => app.ApplicationType == file.Type))
                .WithMessage(string.Format(FileValidationMessages.FormatoArchivo, applicationTypes.GetExtensionConcat()));
        }
    }
}
