﻿using System.Text.Json.Serialization;

namespace ApiCartera.Domain.Features.Shared.DTOs
{
    public class RespuestaAPICarteraDTO<T>
    {
        [JsonPropertyName("data")]
        public T Data { get; set; }

        [JsonPropertyName("statusCode")]
        public int StatusCode { get; set; }

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("errors")]
        public IDictionary<string, string[]>? Errors { get; set; }
    }
}
