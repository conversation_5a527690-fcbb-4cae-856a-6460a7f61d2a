﻿using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Application.Contracts.Persistence
{
    public interface IGenericRepository<TEntity> where TEntity : Entity, new()
    {
        Task<int> CrearAsync(TEntity entity);
        Task<TEntity> ActualizarAsync(TEntity entity);
        Task<List<TEntity>> ObtenerTodosAsync();
        Task<TEntity> ObtenerPorIdAsync(int id);
        Task EliminarPorIdAsync(int id);
    }
}
