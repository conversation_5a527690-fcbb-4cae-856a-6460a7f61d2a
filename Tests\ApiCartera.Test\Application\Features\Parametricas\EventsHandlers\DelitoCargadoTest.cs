using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Events;
using Sarlaft.Application.Features.Parametricas.Events.Handlers;
using Sarlaft.Application.Features.Parametricas.ViewModels;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Parametricas.ValueObjects;
using Sarlaft.Infrastructure.Persitence.Memory;

namespace Sarlaft.Test.Application.Features.Parametricas.EventsHandlers
{
    public class DelitoCargadoTest
    {
        private List<DelitoExcel> excel = new()
            {
                new()
                {
                    Municipio = "Bucaramanga",
                    Departamento = "Santander",
                    CodigoDANE = "12031",
                    FechaHecho = new DateTime(2024,2,10),
                    Cantidad = 15
                },
                new()
                {
                    Municipio = "Floridablanca",
                    Departamento = "Santander",
                    CodigoDANE = "12032",
                    FechaHecho = DateTime.UtcNow,
                    Cantidad = 12
                },
            };


        private List<Ciudad> ciudades = new()
            {
                new Ciudad
                {
                    Nombre = "Bucaramanga",
                    CodigoDANE = "031",
                    CodigoDANECompleto = "12031",
                    Poblacion = 23298
                },
                new Ciudad
                {
                    Nombre = "Floridablanca",
                    CodigoDANE = "032",
                    CodigoDANECompleto = "12032",
                    Poblacion = 1235
                },
            };

        [Fact]
        public async Task DelitoCreado_Crear_Correcto()
        {
            //Arrange
            IGenericRepository<Ciudad> repositoryCiudad = new GenericRepositoryMemory<Ciudad>();
            IGenericRepository<DelitoDetalle> repositoryDelitoDetalle = new GenericRepositoryMemory<DelitoDetalle>();
            var id = new Random().Next(1,10);
            var command = new DelitoCargado(id, excel);
            var handler = new DelitoCargadoHandler(repositoryCiudad, repositoryDelitoDetalle);

            ciudades.ForEach(async ciudad => await repositoryCiudad.CrearAsync(ciudad));

            //Act
            await handler.Handle(command, CancellationToken.None);

            //Assert
            var delitosDetalleInsertados = await repositoryDelitoDetalle.ObtenerTodosAsync();
            Assert.True(delitosDetalleInsertados.All(delitoDetalleInsertado => delitoDetalleInsertado.Id != default));
            Assert.True(excel.All(registro => delitosDetalleInsertados.Any(delitoDetalleInsertado => registro.FechaHecho == delitoDetalleInsertado.FechaHecho)));
            Assert.True(delitosDetalleInsertados.GroupBy(detalle => detalle.CiudadId)
               .All(group => group.Count() == 1));
        }

        [Fact]
        public async Task DelitoCreado_Actualizar_Correcto()
        { //Arrange
            IGenericRepository<Ciudad> repositoryCiudad = new GenericRepositoryMemory<Ciudad>();
            IGenericRepository<DelitoDetalle> repositoryDelitoDetalle = new GenericRepositoryMemory<DelitoDetalle>();
            var id = new Random().Next(1, 10);
            var command = new DelitoCargado(id, excel);
            var handler = new DelitoCargadoHandler(repositoryCiudad, repositoryDelitoDetalle);

            ciudades.ForEach(async ciudad => await repositoryCiudad.CrearAsync(ciudad));
            var delitoDetalle = new DelitoDetalle
            {
                Cantidad = 10,
                CiudadId = ciudades.First().Id,
                FechaHecho = excel.First().FechaHecho,
            };
            await repositoryDelitoDetalle.CrearAsync(delitoDetalle);

            //Act
            await handler.Handle(command, CancellationToken.None);

            //Assert
            var delitosDetalleInsertados = await repositoryDelitoDetalle.ObtenerTodosAsync();
            Assert.True(delitosDetalleInsertados.All(delitoDetalleInsertado => delitoDetalleInsertado.Id != default));
            Assert.True(excel.All(registro => delitosDetalleInsertados.Any(delitoDetalleInsertado => registro.FechaHecho == delitoDetalleInsertado.FechaHecho)));
        }


    }
}