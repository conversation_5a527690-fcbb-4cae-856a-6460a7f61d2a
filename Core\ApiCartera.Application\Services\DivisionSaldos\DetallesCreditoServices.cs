﻿using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Models.CICETEX;
using ApiCartera.Domain.ValueObjects;

namespace ApiCartera.Application.Services.DivisionSaldos;

public class DetallesCreditoServices (
    IRepository<SolicitudEntity, string> sRepository,
    IRepository<SolicitudBeneficioEntity, string> sbRepository,
    IRepository<MbtSaldosSolicitudCarteraEntity, string> sscRepository,
    IRepository<MbtSubProductosEntity, string> spRepository,
    IRepository<TipoSubLineaCreditoEntity, string> tslcRepository,
    IRepository<BeneficiosEntity, string> beneficiosRepository,
    IRepository<InstitucionGiroDetalleEntity, string> institucionGiroDetalleRepository,
    IRepository<InstitucionGiroEntity, string> institucionGiroRepository,
    IRepository<TipoDuracionEntity, string> tipoDuracionRepository,
    IRepository<TipoProgramaEntity, string> tipoProgramaRepository
    ) : IDetallesCreditoServices
{
    private readonly IRepository<SolicitudEntity, string> _sRepository = sRepository;
    private readonly IRepository<SolicitudBeneficioEntity, string> _sbRepository = sbRepository;
    private readonly IRepository<MbtSaldosSolicitudCarteraEntity, string> _sscRepository = sscRepository;
    private readonly IRepository<MbtSubProductosEntity, string> _spRepository = spRepository;
    private readonly IRepository<TipoSubLineaCreditoEntity, string> _tslcRepository = tslcRepository;
    private readonly IRepository<BeneficiosEntity, string> _beneficiosRepository = beneficiosRepository;
    private readonly IRepository<InstitucionGiroDetalleEntity, string> _institucionGiroDetalleRepository = institucionGiroDetalleRepository;
    private readonly IRepository<InstitucionGiroEntity, string> _institucionGiroRepository = institucionGiroRepository;
    private readonly IRepository<TipoDuracionEntity, string> _tipoDuracionRepository = tipoDuracionRepository;
    private readonly IRepository<TipoProgramaEntity, string> _tipoProgramaRepository = tipoProgramaRepository;

    public async Task<List<DesembolsoDTO>> ObtenerDesembolsosPorIdSolicitud(long idSolicitud)
    {
        var institucionGiroDetalleInfo = await ObtenerInstitucionGiroDetallePorIdSolicitud(idSolicitud);

        var institucionGiroInfo = await ObtenerInstitucionGiroPorListaNoRelacion(institucionGiroDetalleInfo.Select(giro => giro.NoRelacion));

        var sbInfoList = await ObtenerSolicitudBeneficioPorIdSolicitud(idSolicitud);

        var sbInfo = new List<SolicitudBeneficioEntity> { sbInfoList
            .OrderByDescending(item => item.Fecha).FirstOrDefault()! };

        var solicitudInfoList = new List<SolicitudEntity> { await ObtenerSolicitudPorIdSolicitud(idSolicitud) };

        var tipoProgramaInfoList = new List<TipoProgramaEntity> { (await ObtenerPeriodicidad(solicitudInfoList[0])).Item2 };
        var tipoDuracionInfoList = new List<TipoDuracionEntity> { (await ObtenerPeriodicidad(solicitudInfoList[0])).Item1 };

        var tipoSubLineaCreditoInfo = await ObtenerTipoSubLineaCreditoPorIdTipoLineaYSubLinea(solicitudInfoList[0].IdTipoLinea, solicitudInfoList[0].IdTipoSubLinea);

        var beneficiosInfo = await _beneficiosRepository.GetAllAsync(DatabaseType.Oracle);

        IEnumerable<DesembolsoDTO>? desembolsos = from igd in institucionGiroDetalleInfo
                    join ig in institucionGiroInfo on igd.NoRelacion equals ig.NoRelacion
                    join sb in sbInfoList on (ig.YearsGiro, ig.SemestreGiro) equals (sb.Years, sb.Semestre) into leftJoinSb
                        from sb in leftJoinSb.DefaultIfEmpty()
                    join s in solicitudInfoList on igd.IdSolicitud equals s.IdSolicitud
                    join tslc in tipoSubLineaCreditoInfo on (s.IdTipoLinea, s.IdTipoSubLinea) equals (tslc.IdTipoLinea, tslc.IdTipoSublinea)
                    join tp in tipoProgramaInfoList on s.CodSnies_Prog equals tp.CodSnies_Prog
                    join td in tipoDuracionInfoList on tp.Dur_Codigo equals td.Dur_Codigo
                    join b in beneficiosInfo on (sb != null ? sb.IdBeneficio : 0) equals b.IdBeneficio into leftJoinB
                        from b in leftJoinB.DefaultIfEmpty() 
                    join sb1 in sbInfo on s.IdSolicitud equals sb1.IdSolicitud
                    select new DesembolsoDTO
                    {
                        IdSolicitud = igd.IdSolicitud,
                        YearGiro = ig.YearsGiro,
                        SemestreGiro = ig.SemestreGiro,
                        Fecha = igd.Fecha,
                        TotalGirar = igd.TotalGirar,
                        ModalidadRubro = ig.Modalidad_Rubro != null ? ig.Modalidad_Rubro! : "",
                        PorcentajeCarteraPlan = tslc.PorcCarteraPlan,
                        Periodicidad = td.MesesPeriodo,
                        Marca = b != null ? b.Descripcion : "",
                        ValorAportesIES = sb != null ? sb.ValorIes : 0,
                        NoRelacion = igd.NoRelacion,
                        Estado = igd.Estado,
                        Puntos = sb1.Puntos
                    };
        var desembolsosList = desembolsos.ToList();
        return desembolsos.ToList();
    }

    public async Task<List<SaldosSolicitudCarteraDTO>> ObtenerSaldosPorIdSolicitud(long idSolicitud)
    {
        var solicitudInfo = await ObtenerSolicitudPorIdSolicitud(idSolicitud);

        var sscInfo = await _sscRepository.FindByConditionsAsync(
            equalConditions: new Dictionary<string, object> {
                {"IDSOLICITUD", idSolicitud.ToString()}
            } ?? throw new Exception("Id no encontrado."),
            likeConditions: new Dictionary<string, object>(),
            betweenConditions: new Dictionary<string, (object, object)>(),
            inConditions: new Dictionary<string, IEnumerable<object>>(),
            logicalOperator: "AND") ?? throw new Exception("Id no encontrado.");

        var (tipoDuracionInfo, tipoPrograma) = await ObtenerPeriodicidad(solicitudInfo);

        var spInfo = await _spRepository.FindByIdAsync("IDSUBPRODUCTO", sscInfo.FirstOrDefault()!.IdSubproducto.ToString()) ?? throw new Exception("Id no encontrado.");

        var tslcInfo = await ObtenerTipoSubLineaCreditoPorIdTipoLineaYSubLinea(solicitudInfo.IdTipoLinea, solicitudInfo.IdTipoSubLinea);

        var UniqueTslcInfo = tslcInfo.FirstOrDefault();

        var saldos = MapearNuevosSaldos(solicitudInfo, sscInfo, tipoDuracionInfo, spInfo, UniqueTslcInfo);

        return saldos;
    }

    private async Task<(TipoDuracionEntity, TipoProgramaEntity)> ObtenerPeriodicidad(SolicitudEntity solicitudInfo)
    {
        var tipoPrograma = await _tipoProgramaRepository.FindByIdAsync("CODSNIES_PROG", solicitudInfo.CodSnies_Prog!) ?? new TipoProgramaEntity { Dur_Codigo = null };

        var tipoDuracion = await _tipoDuracionRepository.FindByIdAsync("DUR_CODIGO", tipoPrograma.Dur_Codigo!) ?? new TipoDuracionEntity { MesesPeriodo = 0 };
        return (tipoDuracion, tipoPrograma);
    }

    private async Task<SolicitudEntity> ObtenerSolicitudPorIdSolicitud(long idSolicitud)
    {
        return await _sRepository.FindByIdAsync("IDSOLICITUD", idSolicitud.ToString()) ?? throw new Exception("Id no encontrado.");
    }

    private async Task<List<TipoSubLineaCreditoEntity>> ObtenerTipoSubLineaCreditoPorIdTipoLineaYSubLinea(int idTipoLinea, int idTipoSubLinea)
    {
        return await _tslcRepository.FindByConditionsAsync(
                    equalConditions: new Dictionary<string, object> {
                {"IDTIPOLINEA", idTipoLinea.ToString()},
                {"IDTIPOSUBLINEA", idTipoSubLinea.ToString()}
                    },
                    likeConditions: new Dictionary<string, object>(),
                    betweenConditions: new Dictionary<string, (object, object)>(),
                    inConditions: new Dictionary<string, IEnumerable<object>>(),
                    logicalOperator: "AND") ?? throw new Exception("Id no encontrado.");
    }

    private async Task<List<InstitucionGiroEntity>> ObtenerInstitucionGiroPorListaNoRelacion(IEnumerable<int?> listaNorelacion)
    {
        var noRelacionObjects = listaNorelacion.Select(x => (object)x);
        var institucionGiroInfo = await _institucionGiroRepository.FindByConditionsAsync(
                    equalConditions: new Dictionary<string, object>(),
                    likeConditions: new Dictionary<string, object>(),
                    betweenConditions: new Dictionary<string, (object, object)>(),
                    inConditions: new Dictionary<string, IEnumerable<object>>
                    {
                        { "NORELACION", noRelacionObjects!}
                    },
                    logicalOperator: "AND");

        return institucionGiroInfo;
    }

    private async Task<List<InstitucionGiroDetalleEntity>> ObtenerInstitucionGiroDetallePorIdSolicitud(long idSolicitud)
    {
        return await _institucionGiroDetalleRepository.FindByConditionsAsync(
                    equalConditions: new Dictionary<string, object> {
                { "IDSOLICITUD", idSolicitud }
                    },
                    likeConditions: new Dictionary<string, object>(),
                    betweenConditions: new Dictionary<string, (object, object)>(),
                    inConditions: new Dictionary<string, IEnumerable<object>>(),
                    logicalOperator: "AND"
                    );
    }

    private async Task<List<SolicitudBeneficioEntity>> ObtenerSolicitudBeneficioPorIdSolicitud(long idSolicitud)
    {
        return await _sbRepository.FindByConditionsAsync(
            equalConditions: new Dictionary<string, object>
            {
                { "IDSOLICITUD", idSolicitud.ToString()}
            },
            likeConditions: new Dictionary<string, object>(),
            betweenConditions: new Dictionary<string, (object, object)>(),
            inConditions: new Dictionary<string, IEnumerable<object>>(),
            logicalOperator: "AND"
            ) ?? throw new Exception("Id no encontrado.");
    }

    private List<SaldosSolicitudCarteraDTO> MapearNuevosSaldos(SolicitudEntity solicitud, List<MbtSaldosSolicitudCarteraEntity>? mbtSolicitud, TipoDuracionEntity tipoDuracion, MbtSubProductosEntity? subProducto, TipoSubLineaCreditoEntity? tipoSubLinea)
    {
        List<SaldosSolicitudCarteraDTO>? saldos = new List<SaldosSolicitudCarteraDTO>();

        foreach(MbtSaldosSolicitudCarteraEntity item in mbtSolicitud)
        {
            SaldosSolicitudCarteraDTO saldo = new SaldosSolicitudCarteraDTO()
            {
                Id = item.IdSaldosSolicitudCartera,
                TipoCartera = item.IdTipoCartera == 1 ? Constants.TIPOCARTERA_ESTUDIOS : Constants.TIPOCARTERA_AMORTIZACION,
                IdSolicitud = solicitud.IdSolicitud,
                IdSignature = item.IdSignature,
                IdSolicitante = long.Parse(solicitud.IdSolicitante!),
                IdSubproducto = item.IdSubproducto,
                CodigoSubproducto = ObtenerCodigoSuproducto(item.IdSubproducto),
                SaldoCapitalVigente = item.SaldoCapitalVigente,
                SaldoTotalCapital = item.SaldoTotalCapital,
                SaldoCapitalVencido = item.SaldoCapitalVencido,
                CuotasPendientesPorPagar = item.CuotasPendientesPagar,
                PorcentajeCarteraPlan = tipoSubLinea!.PorcCarteraPlan,
                Periodicidad = tipoDuracion.MesesPeriodo
            };
            saldos.Add(saldo);
        }
        return saldos;
    }

    private string ObtenerCodigoSuproducto(int idSubproducto)
    {
        string? codigoSubproducto = idSubproducto switch
        {
            1 => TiposSubproducto.CORTO_PLAZO,
            2 => TiposSubproducto.LARGO_PLAZO,
            6 => TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
            9 => TiposSubproducto.ALIANZAS_LP_RP_AMORTIZACION,
            10 => TiposSubproducto.ALIANZAS_LP_RP,
            12 => TiposSubproducto.ALIANZAS_REEMBOLSABLE_LP_RA,
            34 => TiposSubproducto.ORI_CONDONABLE,
            36 => TiposSubproducto.ORI_CONDONABLE_R_TERCEROS,
            37 => TiposSubproducto.ORI_REEMBOLSABLE_RT,
            _ => ""
        };

        return codigoSubproducto;
    }
}
