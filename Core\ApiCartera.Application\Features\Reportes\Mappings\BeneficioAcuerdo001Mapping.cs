﻿using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Models;
using AutoMapper;

namespace ApiCartera.Application.Features.Reportes.Mappings;

public class BeneficioAcuerdo001Mapping: Profile
{
    public BeneficioAcuerdo001Mapping()
    {
        CreateMap<MvReporteBeneficioAc001, BeneficioAcuerdo001Vm>()
            .ForMember(dest => dest.FechaGeneracion,
            opt => opt.MapFrom(src => src.FechaGeneracion.ToString("dd/MM/yyyy").Substring(0,10)));
    }
}
