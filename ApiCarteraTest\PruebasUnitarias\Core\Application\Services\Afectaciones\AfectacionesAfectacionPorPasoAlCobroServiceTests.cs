﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesAfectacionPorPasoAlCobroServiceTests
    {
        private readonly SaldosCarteraDTO saldoCartera = new()
        {
            Saldo1 = 8500000,
            Saldo2 = 0,
            Saldo3 = 400000,
            Saldo4 = 0,
            IdSolicitud = 6272827,
            IdSubproducto = 6,
            CodigoSubproducto = "1101",
            IdSaldosSolicitudCartera = 3
        };

        private readonly AfectacionesAfectacionPorPasoAlCobroService _afectacionesAfectacionPorPasoAlCobroService;
        private readonly Mock<ISaldosCarteraRepository> _saldosCarteraRepositoryMock;
        private readonly Mock<ISolicitudCarteraActivaRepository> _solicitudCarteraActivaRepositoryMock;

        public AfectacionesAfectacionPorPasoAlCobroServiceTests()
        {
            _saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            _saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(saldoCartera));
            _solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            _afectacionesAfectacionPorPasoAlCobroService = new AfectacionesAfectacionPorPasoAlCobroService(
                _saldosCarteraRepositoryMock.Object,
                _solicitudCarteraActivaRepositoryMock.Object);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosPorPasoAlCobro), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoDesembolsos_RetornaNuevosSaldos(MovimientoDTO movimiento)
        {
            var resultado = await _afectacionesAfectacionPorPasoAlCobroService.CalcularSaldosCartera(movimiento);
            Assert.NotNull(resultado);
            Assert.Equal(resultado.Saldo1, movimiento.ValorGiro);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(0, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
            Assert.NotEqual(8500000, resultado.Saldo1);
        }
    }
}
