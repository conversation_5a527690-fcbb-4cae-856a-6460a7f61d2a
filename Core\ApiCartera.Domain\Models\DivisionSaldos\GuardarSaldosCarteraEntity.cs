﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.DivisionSaldos;

[EntityName("MBT_SALDOS_CARTERA")]
public class GuardarSaldosCarteraEntity : IEntity<string>
{
    private int? _idSaldosSolicitudCartera;
    private int? _idMovimiento;

    public string Id
    {
        get => IdSubproducto.ToString();
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public double Saldo1 { get; set; }
    public double Saldo2 { get; set; }
    public double Saldo3 { get; set; }
    public double Saldo4 { get; set; }
    public int IdSubproducto { get; set; }
    public int? IdSaldosSolicitudCartera
    {
        get => _idSaldosSolicitudCartera;
        set => _idSaldosSolicitudCartera = value == 0 ? null : value;
    }
    public int? IdSaldosCarteraAnt { get; set; } = null;
    public int? IdMovimiento 
    { 
        get => _idMovimiento;
        set => _idMovimiento = value == 0 ? null : value;
    }
}
