﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using System.Text.RegularExpressions;
using ApiCartera.Application.Extensions;
using FluentValidation.Results;
using ApiCartera.Application.Exceptions;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesIdentificacionNuevosCreditos(ISaldosCarteraRepository saldosCarteraRepository,
                                                          ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
                                                          INuevoscreditosRepository nuevoscreditosRepository) : AfectacionesService, IAfectacionesIdentificacionNuevosCreditos
    {
        public async Task<SaldosCarteraDTO> IdentificacionNuevosCreditos(MovimientoAfectacionDTO dataNuevosCreditosDTO)
        {
            SaldosCarteraDTO dataresult = new SaldosCarteraDTO();
            int codConsult = dataNuevosCreditosDTO.CodigoSubProducto;
            int idSubProducto = dataNuevosCreditosDTO.ObtenerIdSubproducto();
            NuevosCreditosDTO nuevosCreditosDTO = new NuevosCreditosDTO()
            {
                IdIcetex = dataNuevosCreditosDTO.IdIcetex,
                MontoMovimientoCapital = dataNuevosCreditosDTO.MontoMovimientoCapital,
                CodigoTipoMovimientoCapital = dataNuevosCreditosDTO.CodigoTipoMovimientoCapital,
                CodigoSubproductoObligacion = dataNuevosCreditosDTO.CodigoSubProducto,
                DescripcionMovimientoMemo = dataNuevosCreditosDTO.DescripcionMovimientoMemo,
                CodigoNovedad = int.Parse(dataNuevosCreditosDTO.CodigoNovedad)
            };

            if ((nuevosCreditosDTO.CodigoTipoMovimientoCapital == 33) && (nuevosCreditosDTO.CodigoNovedad == 6) && idSubProducto != 0)
            {
                nuevosCreditosDTO.DescripcionMovimientoMemo = ValidarYExtraerNumero(nuevosCreditosDTO.DescripcionMovimientoMemo!).ToString();
                var dataPeticion = await nuevoscreditosRepository.ObtenerDataNuevosCreditos(nuevosCreditosDTO.IdIcetex, Convert.ToInt32(nuevosCreditosDTO.DescripcionMovimientoMemo), codConsult);
                dataPeticion.idSubProducto = idSubProducto;
                dataresult = await ValidacionesData(nuevosCreditosDTO, dataPeticion);
            }
            else
            {
                dataresult.CodigoSubproducto = "La validacion de CodigoTipoMovimientoCapital y idSubProducto, no es valida.";
            }

            return dataresult;
        }

        public static int ValidarYExtraerNumero(string input)
        {

            if (string.IsNullOrWhiteSpace(input))
                return 0;

            bool starts06 = input.StartsWith("06 ");
            bool starts006 = input.StartsWith("006 ");

            if (!starts06 && !starts006)
                return 0;

            try
            {
                string numberStr;
                if (starts06)
                {

                    var match = Regex.Match(input, @"06\s+Res\.?\s*(\d+)");
                    if (!match.Success)
                        return 0;

                    numberStr = match.Groups[1].Value;
                }
                else
                {

                    var match = Regex.Match(input, @"006\s+Res\.?\s*(\d+)");
                    if (!match.Success)
                        return 0;

                    numberStr = match.Groups[1].Value;
                }

                if (int.TryParse(numberStr, out int result))
                    return result;

                return 0;
            }
            catch
            {
                return 0;
            }
        }


        public async Task<SaldosCarteraDTO> ValidacionesData(NuevosCreditosDTO nuevosCreditosDTO, DataQueryNuevosCreditodDTO dataQueryNuevosCreditodDTO)
        {
            SaldosCarteraDTO saldosCarteraDTO = new()
            {
                IdSaldosSolicitudCartera = dataQueryNuevosCreditodDTO.IdSaldosSolicitudCartera,
                IdMovimiento = dataQueryNuevosCreditodDTO.IdMovimiento,
                IdSubproducto = dataQueryNuevosCreditodDTO.idSubProducto
            };

            bool numData = await nuevoscreditosRepository.ValidarGiroInternacional(nuevosCreditosDTO.IdIcetex);

            if (numData)
            {
                saldosCarteraDTO.Saldo1 = nuevosCreditosDTO.MontoMovimientoCapital;

                var saldosMatricula = this.MapearSaldosCarteraAfectaciones(saldosCarteraDTO);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldosMatricula);

                return saldosCarteraDTO;
            }

            if (dataQueryNuevosCreditodDTO.ModalidadRubro != TiposBeneficio.MATRICULA)
            {
                saldosCarteraDTO.Saldo1 = nuevosCreditosDTO.MontoMovimientoCapital;

                var saldosNew = this.MapearSaldosCarteraAfectaciones(saldosCarteraDTO);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldosNew);

                return saldosCarteraDTO;
            }

            if (dataQueryNuevosCreditodDTO.ModalidadRubro == TiposBeneficio.MATRICULA)
            {
                switch (dataQueryNuevosCreditodDTO.InstitucionGiro)
                {
                    case TiposBeneficio.APORTES_IES:

                        saldosCarteraDTO.Saldo3 = nuevosCreditosDTO.MontoMovimientoCapital;
                        var saldosAportesIES = this.MapearSaldosCarteraAfectaciones(saldosCarteraDTO);
                        await solicitudCarteraActivaRepository.GuardarSaldos(saldosAportesIES);

                        break;

                    case TiposBeneficio.NO_APLICA_BENEFICIO:

                        saldosCarteraDTO.Saldo4 = nuevosCreditosDTO.MontoMovimientoCapital;
                        var saldosNoAplica = this.MapearSaldosCarteraAfectaciones(saldosCarteraDTO);
                        await solicitudCarteraActivaRepository.GuardarSaldos(saldosNoAplica);

                        break;

                    default:
                        throw new ValidationException(
                        [
                            new ValidationFailure()
                                    {
                                        PropertyName = nameof(MovimientoAfectacionDTO.MontoMovimientoCapital),
                                        ErrorMessage = "El valor de InstitucionGiro no es valido para la modalidad MATRICULA."
                                    }
                        ]);
                }
                return saldosCarteraDTO;
            }
            else
            {
                throw new ValidationException(
                    [
                        new ValidationFailure()
                        {
                            PropertyName = nameof(MovimientoAfectacionDTO.MontoMovimientoCapital),
                            ErrorMessage = "Se ha presentado un error, ninguno de los escenarios validados son correctos para este registro."
                        }
                    ]);
            }
        }
    }
}
