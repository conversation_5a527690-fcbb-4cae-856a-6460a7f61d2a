﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalReintegroService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraService
    {
        private const int ANIO_APORTES_IES = 2024;
        private const int ANIO_CONTRIBUCION_IES = 2023;

        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoDTO movimiento)
        {
            ValidarMovimiento(movimiento);

            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);

            ValidarSaldosCartera(saldosCartera, movimiento.IdSolicitud);

            //Ajuste por el tipo de campo NoRelacion

            var NumeroRelacion = int.Parse(movimiento.NoRelacion);
            
            //if (!movimiento.NoRelacion.HasValue)
            if (NumeroRelacion != 0)
            {
                throw new ValidationException(
                [
                    new()
                    {
                        PropertyName = nameof(movimiento.NoRelacion),
                        ErrorMessage = ValidationMessages.MovimientosNoRelacion
                    }
                ]);
            }

            //var desembolso = await solicitudCarteraActivaRepository
            //    .ObtenerDesembolsoPorNoRelacion(movimiento.IdSolicitud, movimiento.NoRelacion.Value);

            var desembolso = await solicitudCarteraActivaRepository
               .ObtenerDesembolsoPorNoRelacion(movimiento.IdSolicitud, NumeroRelacion);

            ValidarDesembolsoSolicitud(desembolso);
            var nuevosSaldosCartera = (SaldosCarteraDTO) saldosCartera.Clone();

            var desembolsoTieneVigenciaAnteriorA20241 = desembolso.YearGiro < ANIO_APORTES_IES;
            var desembolsoTieneVigenciaPosterioresA20232 = desembolso.YearGiro > ANIO_CONTRIBUCION_IES;

            var tieneMarcaSinBeneficio = string.IsNullOrEmpty(desembolso.Marca) 
                || desembolso.Marca.Equals(TiposBeneficio.NO_APLICA_BENEFICIO)
                || (desembolso.Marca.Equals(TiposBeneficio.APORTES_IES) 
                    && (desembolso.ValorAportesIES == null || desembolso.ValorAportesIES == 0));
            var tieneMarcaAportesIES = desembolso.Marca.Equals(TiposBeneficio.APORTES_IES)
                && (desembolso.ValorAportesIES != null && desembolso.ValorAportesIES > 0);

            if (tieneMarcaSinBeneficio && desembolsoTieneVigenciaAnteriorA20241)
            {
                nuevosSaldosCartera.Saldo1 -= movimiento.ValorGiro;
            }
          
            if (desembolso.Marca.Equals(TiposBeneficio.CONTRIBUCION_IES))
            {
                nuevosSaldosCartera.Saldo2 -= movimiento.ValorGiro;
            }

            if (tieneMarcaAportesIES)
            {
                nuevosSaldosCartera.Saldo3 -= movimiento.ValorGiro;
            }

            if (tieneMarcaSinBeneficio && desembolsoTieneVigenciaPosterioresA20232)
            {
                nuevosSaldosCartera.Saldo4 -= movimiento.ValorGiro;
            }

            nuevosSaldosCartera = ValidarSaldosACero(nuevosSaldosCartera);
            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimiento;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
            var saldos = this.MapearSaldosCartera(nuevosSaldosCartera);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldos);

            return nuevosSaldosCartera;
        }

        public static void ValidarDesembolsoSolicitud(DesembolsoDTO desembolso)
        {
            var desembolsosSolicitudValidator = new DesembolsoValidator();
            desembolsosSolicitudValidator.ValidateAndThrowCustomValidationException(desembolso);
        }

        private static SaldosCarteraDTO ValidarSaldosACero(SaldosCarteraDTO saldosCartera)
        {
            saldosCartera.Saldo1 = saldosCartera.Saldo1 > 0 ? saldosCartera.Saldo1 : 0;
            saldosCartera.Saldo2 = saldosCartera.Saldo2 > 0 ? saldosCartera.Saldo2 : 0;
            saldosCartera.Saldo3 = saldosCartera.Saldo3 > 0 ? saldosCartera.Saldo3 : 0;
            saldosCartera.Saldo4 = saldosCartera.Saldo4 > 0 ? saldosCartera.Saldo4 : 0;

            return saldosCartera;
        }
    }
}
