using Microsoft.VisualStudio.TestPlatform.ObjectModel;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Domain.Exceptions;
using Sarlaft.Domain.Features.Files.Enums;
using Sarlaft.Domain.Features.Files.Extensions;

namespace Sarlaft.Test.Domain.Features.Files
{
    public class ApplicationFileTypeExtensionsTest
    {
        [Fact]
        public void GetApplicationTypesTest()
        {
            List<FormatFile> formatFiles = new()
            {
                FormatFile.JSON,
                FormatFile.PDF
            };

            var applicationTypes = formatFiles.GetApplicationTypes();

            Assert.Equal(formatFiles.Count, applicationTypes.Count);
            Assert.All(applicationTypes, applicationType => formatFiles.Contains(applicationType.FormatFile));
        }
        [Fact]
        public void GetExtensionsTest()
        {
            List<FormatFile> formatFiles = new()
            {
                FormatFile.JSON,
                FormatFile.PDF
            };

            var applicationTypes = formatFiles.GetApplicationTypes();
            var extensions = applicationTypes.GetExtensionConcat();
            var splitExtensions = extensions.Split(",");

            Assert.Equal(splitExtensions.Count(),2);
            Assert.True(extensions.Count() > 0);
        }

        [Fact]
        public void FindByApplicationTypeTest_Correcto()
        {
            var stringType = ApplicationTypes.PDF;

            var applicationTypes = ApplicationFileTypeExtensions.FindByApplicationType(stringType);


            Assert.NotNull(applicationTypes);
            Assert.Equal(applicationTypes.ApplicationType, stringType);
        }

        [Fact]
        public void FindByApplicationTypeTest_Fallida()
        {
            var stringType = "test";

            Assert.Throws<NotFoundException>(()=> ApplicationFileTypeExtensions.FindByApplicationType(stringType));
        }

        [Fact]
        public void FindByFormatFileTest_Correcto()
        {
            var formatFile = FormatFile.PDF;

            var applicationTypes = ApplicationFileTypeExtensions.FindByFormatFile(formatFile);


            Assert.NotNull(applicationTypes);
            Assert.Equal(applicationTypes.FormatFile, formatFile);
        }

        [Fact]
        public void FindByExtensionTest_Correcto()
        {
            var formatFile = FileExtensions.PDF;

            var applicationTypes = ApplicationFileTypeExtensions.FindByExtension(formatFile);


            Assert.NotNull(applicationTypes);
            Assert.Equal(applicationTypes.Extension, formatFile);
        }

        [Fact]
        public void FindByExtensionTest_Fallida()
        {
            string formatFile = ".docx";

            Assert.Throws<NotFoundException>(()=> ApplicationFileTypeExtensions.FindByExtension(formatFile));
        }
    }
}