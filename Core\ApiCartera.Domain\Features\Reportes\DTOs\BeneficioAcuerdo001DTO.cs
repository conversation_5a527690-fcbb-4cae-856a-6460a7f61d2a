﻿using Newtonsoft.Json;

namespace ApiCartera.Domain.Features.Reportes.DTOs
{
    public class BeneficioAcuerdo001DTO
    {
        [JsonProperty("idSolicitud")]
        public int IdSolicitud { get; set; }

        [JsonProperty("idSignature")]
        public int IdSignature { get; set; }

        [JsonProperty("codigoSubproducto")]
        public string CodigoSubproducto { get; set; }

        [JsonProperty("CCN")]
        public string CCN { get; set; }

        [JsonProperty("documento")]
        public string Documento { get; set; }

        [JsonProperty("nombreCompleto")]
        public string NombreCompleto { get; set; }

        [JsonProperty("sisben")]
        public string <PERSON>sben { get; set; }

        [JsonProperty("estrato")]
        public int Estrato { get; set; }

        [JsonProperty("genero")]
        public string Genero { get; set; }

        [JsonProperty("ciudad")]
        public string Ciudad { get; set; }

        [JsonProperty("departamento")]
        public string Departamento { get; set; }

        [JsonProperty("codigoDepartamento")]
        public string CodigoDepartamento { get; set; }

        [JsonProperty("codigoLinea")]
        public int CodigoLinea { get; set; }

        [JsonProperty("linea")]
        public string Linea { get; set; }

        [JsonProperty("sublinea")]
        public string Sublinea { get; set; }

        [JsonProperty("codigoSnies")]
        public string CodigoSNIES { get; set; }

        [JsonProperty("ies")]
        public string IES { get; set; }

        [JsonProperty("tipoCartera")]
        public string TipoCartera { get; set; }

        [JsonProperty("modalidadCredito")]
        public string ModalidadCredito { get; set; }

        [JsonProperty("capitalExigible")]
        public double CapitalExigible { get; set; }

        [JsonProperty("capitalNoExigible")]
        public double CapitalNoExigible { get; set; }

        [JsonProperty("tipoBeneficio")]
        public string TipoBeneficio { get; set; }

        [JsonProperty("valorTasaContratacion")]
        public double ValorTasaContratacion { get; set; }

        [JsonProperty("ipcContratacion")]
        public double IPCContratacion { get; set; }

        [JsonProperty("valorTasaBeneficio")]
        public double ValorTasaBeneficio { get; set; }

        [JsonProperty("ipcBeneficio")]
        public string IPCBeneficio { get; set; }

        [JsonProperty("saldo1")]
        public double Saldo1 { get; set; }

        [JsonProperty("saldo2")]
        public double Saldo2 { get; set; }

        [JsonProperty("saldo3")]
        public double Saldo3 { get; set; }

        [JsonProperty("saldo4")]
        public double Saldo4 { get; set; }

        [JsonProperty("fechaGeneracion")]
        public DateTime FechaGeneracion { get; set; }

        [JsonProperty("capitalVigente")]
        public double CapitalVigente { get; set; }

        [JsonProperty("capitalVencido")]
        public double CapitalVencido { get; set; }

        [JsonProperty("cuotasPendientesPlanPagos")]
        public int CuotasPendientesPlanPagos { get; set; }

        [JsonProperty("cs1")]
        public double CalculoBeneficioAcuerdo001Saldo1 { get; set; }

        [JsonProperty("cs2")]
        public double CalculoBeneficioAcuerdo001Saldo2 { get; set; }

        [JsonProperty("cs3")]
        public double CalculoBeneficioAcuerdo001Saldo3 { get; set; }

        [JsonProperty("cs4")]
        public double CalculoBeneficioAcuerdo001Saldo4 { get; set; }
    }
}
