﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Reportes.Handlers;

public class ReporteAlertasHandler(
    IReporteAlertasService reporte,
    IMapper mapper
    ) : IRequestHandler<ReporteAlertasQuery, IEnumerable<ReporteAlertasVm>>
{
    private readonly IReporteAlertasService _reporte = reporte;
    private readonly IMapper _mapper = mapper;

    public async Task<IEnumerable<ReporteAlertasVm>> Handle(ReporteAlertasQuery request, CancellationToken cancellationToken)
    {
        var resultado = await _reporte.ObtenerReporte(request.desde, request.hasta, request.page); 
        return _mapper.Map<IEnumerable<ReporteAlertasVm>>(resultado);
    }
}
