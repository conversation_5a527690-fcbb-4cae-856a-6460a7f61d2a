﻿using Newtonsoft.Json;
namespace ApiCartera.Domain.Features.Afectaciones.DTOs
{
    public class DataNuevosCreditosDTO
    {
        [JsonProperty("idIcetex")]
        public int IdIcetex { get; set; }

        [JsonProperty("idSignature")]
        public int IdSignature { get; set; }

        [JsonProperty("sdoTotalCapitalVigente")]
        public decimal SdoTotalCapitalVigente { get; set; }

        [JsonProperty("fechaEfectivaMovimiento")]
        public string FechaEfectivaMovimiento { get; set; }

        [JsonProperty("fechaPosteo")]
        public string FechaPosteo { get; set; }

        [JsonProperty("montoMovimientoCapital")]
        public double MontoMovimientoCapital { get; set; }

        [JsonProperty("codigoTipoMovimientoCapital")]
        public int CodigoTipoMovimientoCapital { get; set; }

        [JsonProperty("nombreTipoMovimientoCapital")]
        public string? NombreTipoMovimientoCapital { get; set; }

        [JsonProperty("codigoSubProducto")]
        public int CodigoSubProducto { get; set; }

        [JsonProperty("saldoTotalCapital")]
        public decimal SaldoTotalCapital { get; set; }

        [JsonProperty("saldoCapitalVencido")]
        public decimal SaldoCapitalVencido { get; set; }

        [JsonProperty("descripcionMovimientoMemo")]
        public string? DescripcionMovimientoMemo { get; set; }

        [JsonProperty("codigoNovedad")]
        public string CodigoNovedad { get; set; }

        [JsonProperty("tipoCartera")]
        public string? TipoCartera { get; set; }

        [JsonProperty("cuotasPendientesPorPagar")]
        public int CuotasPendientesPorPagar { get; set; }
    }
}
