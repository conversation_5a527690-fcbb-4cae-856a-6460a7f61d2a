﻿using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Domain.Features.Reportes.Services;
using ApiCartera.Domain.Models;

namespace ApiCartera.Application.Services.Reportes;

public class ReporteAlertasService(
    IRepository<MvReporteAlertasEntity, string> repository
    ) : IReporteAlertasService
{
    private readonly IRepository<MvReporteAlertasEntity, string> _repository = repository;

    public async Task<IEnumerable<MvReporteAlertasEntity>> ObtenerReporte(DateTime desde, DateTime hasta, int page)
    {
        var resultado = await _repository.ExecuteReportViewPaginatedAsync<MvReporteAlertasEntity>("VM_REPORTE_ALERTAS", "FECHA", desde.ToString("dd/MM/yyyy"), hasta.ToString("dd/MM/yyyy"), 500, page);

        return resultado;
    }
}
