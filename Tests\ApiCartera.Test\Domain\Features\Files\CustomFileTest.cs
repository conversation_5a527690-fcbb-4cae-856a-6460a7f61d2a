﻿using Sarlaft.Domain.Features.Files.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sarlaft.Test.Domain.Features.Files
{
    public class CustomFileTest
    {
        private byte[] fileBytesTest => Enumerable.Repeat((byte)0x55, 20).ToArray();
        [Fact]
        public void CreateCustomAndModifyNameFile()
        {

            var customFile = new CustomFile(fileBytesTest, "prueba.pdf");
            var nuevoNombre = "nuevo";


            customFile.ModifyName(nuevoNombre);

            Assert.Equal(nuevoNombre, customFile.Name);
        }
    }
}
