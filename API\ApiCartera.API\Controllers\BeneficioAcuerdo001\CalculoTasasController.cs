﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Commands;
using ApiCartera.Application.Features.BeneficioAcuerdo001.ViewModels;

using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.BeneficioAcuerdo001
{
    [Route("api/[controller]")]
    [ApiController]
    public class CalculoTasasController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("CalcularTasas")]
        [ProducesResponseType<ResponseWrapper<TasaCarteraVm>>(StatusCodes.Status200OK)]
        public async Task<TasaCarteraVm> CalcularTasas([FromQuery] CalculoTasasCommand request) =>
            await _mediator.Send(request);
    }
}
