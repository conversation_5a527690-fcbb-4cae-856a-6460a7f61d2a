﻿using ApiCartera.Application.Contracts.Persistence.Http;
using ApiCartera.Domain.Features.Shared.DTOs;
using Microsoft.Extensions.Configuration;
using System.Text;
using System.Text.Json;

namespace ApiCartera.Infrastructure.Http
{
    public class HttpRequestRepository : IHttpRequestRepository
    {
        private readonly HttpClient _httpClient = new();
        private readonly JsonSerializerOptions _jsonSerializerOptions = new()
        {
            PropertyNameCaseInsensitive = true
        };

        public HttpRequestRepository(IConfiguration configuration)
        {
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri(configuration.GetSection("ApiCarteraUrl").Value!),
                Timeout = TimeSpan.FromMinutes(10)
            };
        }

        public List<T> GetAsync<T>(string url)
        {
            try
            {
                var response = _httpClient.GetAsync(url).Result;

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = response.Content.ReadAsStringAsync().Result;
                    var getResponse = JsonSerializer.Deserialize<RespuestaAPICarteraDTO<List<T>>>(responseContent, _jsonSerializerOptions);

                    return getResponse.Data;
                }
                else
                {
                    Console.WriteLine(response.StatusCode);
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"Ocurrió un error en la petición http: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error: {ex.Message}");
                return null;
            }
        }

        public async Task<T?> PostAsync<T>(string url, object body)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var postResponse = JsonSerializer.Deserialize<RespuestaAPICarteraDTO<T>>(responseContent, _jsonSerializerOptions);

                    if (postResponse != null)
                    {
                        return postResponse.Data;
                    }
                    else
                    {
                        Console.WriteLine("La respuesta del servidor es nula o no se pudo deserializar.");
                        return default;
                    }
                }
                else
                {
                    Console.WriteLine(response.StatusCode);
                    return default;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"Ocurrió un error en la petición http: {ex.Message}");
                return default;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error: {ex.Message}");
                return default;
            }
        }
    }
}
