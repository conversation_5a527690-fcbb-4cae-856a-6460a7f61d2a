﻿using ApiCartera.Application.Resources;

namespace ApiCartera.Application.Shared.ExtensionMethods;

public static class DoubleExtension
{
    public static double CalcularIntEntre(this double tasaAnual, int numTotalCuotas,double capitalVigente, int cuotasPendientes)
    {
        double intereses = 0;
        double va = capitalVigente;
        double r = tasaAnual / Constants.BASE_LIQUIDACION;
        int n = numTotalCuotas;
        int k = n - cuotasPendientes - 1;
        double pmt;
        double sk;

        pmt = (va * r) / (1 - Math.Pow(1 + r, -n));
        sk = pmt * (1 - Math.Pow(1 + r, -(n - k))) / r;
        intereses = sk * r;

        return intereses;
    }
}
