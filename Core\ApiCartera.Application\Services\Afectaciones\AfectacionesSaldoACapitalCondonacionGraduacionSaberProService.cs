﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalCondonacionGraduacionSaberProService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            ValidarMovimientoAfectacion(movimiento);
            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);

            //if (saldosCartera.IdSaldosSolicitudCartera == 0)          
            //    saldosCartera.IdSaldosSolicitudCartera = null;

            ValidarSaldosCartera(saldosCartera, movimiento.IdIcetex);
            var nuevosSaldosCartera = (SaldosCarteraDTO) saldosCartera.Clone();
            
            nuevosSaldosCartera = this.DisminuirSaldosProgresivamente(nuevosSaldosCartera, movimiento.MontoMovimientoCapital);
            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
            //nuevosSaldosCartera.IdSaldosSolicitudCartera = 0;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
            var saldos = MapearSaldosCarteraAfectaciones(nuevosSaldosCartera);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldos);

            return nuevosSaldosCartera;
        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }

    }
}
