﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using System.Text.RegularExpressions;

namespace ApiCartera.Application.Extensions
{
    public static class MovimientoAfectacionExtensions
    {
        public static int ObtenerIdSubproducto(this MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null) return 0;

            return movimiento.CodigoSubProducto switch
            {
                10001 => 1,
                10002 => 2,
                10006 => 6,
                20000 => 9,
                20001 => 10,
                60001 => 34,
                60002 => 35,
                60003 => 36,
                60004 => 37,
                60005 => 38,
                _ => 0
            };
        }


        public static long? ObtenerIdOrigenNovacion(this MovimientoAfectacionDTO movimiento)
        {
            if (string.IsNullOrEmpty(movimiento?.DescripcionMovimientoMemo))
                return null;

            var match = Regex.Match(movimiento.DescripcionMovimientoMemo, @"desde:\s*(\d+)");
            if (match.Success && match.Groups.Count > 1)
            {
                if (long.TryParse(match.Groups[1].Value, out long idOrigen))
                    return idOrigen;
            }

            return null;
        }


        public static long? ObtenerIdDestinoNovacion(this MovimientoAfectacionDTO movimiento)
        {
            if (string.IsNullOrEmpty(movimiento?.DescripcionMovimientoMemo))
                return null;

            var match = Regex.Match(movimiento.DescripcionMovimientoMemo, @"hacia:\s*(\d+)");
            if (match.Success && match.Groups.Count > 1)
            {
                if (long.TryParse(match.Groups[1].Value, out long idDestino))
                    return idDestino;
            }

            return null;
        }

        public static (long? IdOrigen, long? IdDestino) ObtenerIdsNovacion(this MovimientoAfectacionDTO movimiento)
        {
            return (
                ObtenerIdOrigenNovacion(movimiento),
                ObtenerIdDestinoNovacion(movimiento)
            );
        }

        public static (int IdSubproducto, string TipoCore) ObtenerInfoSubproducto(this MovimientoAfectacionDTO dto)
        {
            return dto.CodigoSubProducto switch
            {
                10001 => (1, "C"),
                10002 => (2, "L"),
                10006 => (6, "L"),
                20000 => (9, "L"),
                20001 => (10, "L"),
                60004 => (37, "L"),
                _ => (0, string.Empty)
            };
        }

    }
}
