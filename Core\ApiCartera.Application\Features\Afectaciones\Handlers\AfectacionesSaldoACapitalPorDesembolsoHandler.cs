﻿using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionesSaldoACapitalPorDesembolsoHandler(
        ICalcularSaldosCarteraAfectacionService _calcularSaldosCarteraService,
        IMapper _mapper) : IRequestHandler<AfectacionesSaldoACapitalPorDesembolsoCommand, SaldoCarteraVm>
    {
        public async Task<SaldoCarteraVm> Handle(AfectacionesSaldoACapitalPorDesembolsoCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _calcularSaldosCarteraService.CalcularSaldosCartera(request.Movimiento);
            return _mapper.Map<SaldoCarteraVm>(resultado);
        }
    }
}
