﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Domain.Features.Afectaciones.DTOs
{
    public class DataQueryNuevosCreditodDTO
    {
        public int CodSniesInst { get; set; }
        public int YearGiro { get; set; }
        public int SemestreGiro { get; set; }
        public string? ModalidadRubro { get; set; }
        public int NoRelacion { get; set; }
        public int IdSolicitud { get; set; }
        public int IdBeneficio { get; set; }
        public int YearsBeneficio { get; set; }
        public int SemestreBeneficio { get; set; }
        public string? InstitucionGiro { get; set; }
        public int IdMovimiento { get; set; }
        public int idSubProducto { get; set; }
        public int? IdSaldosSolicitudCartera { get; set; } = null;
        public int? VALORAPORTEIES { get; set; } = null;
    }
}
