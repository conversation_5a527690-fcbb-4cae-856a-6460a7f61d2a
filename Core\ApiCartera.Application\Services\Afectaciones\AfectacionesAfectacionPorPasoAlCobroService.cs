﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using FluentValidation.Results;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesAfectacionPorPasoAlCobroService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService

    {

        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            var idSubproductoCortoPlazo = 1;
            var idSubproductoLargoPlazo = 2;            
            var idSaldosCarteraAnterior = 0;
            var validacionSaldos = 0;

            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCarteraCortoPlazo = await saldosCarteraRepository
               .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproductoCortoPlazo);
            if (idSubproducto == idSubproductoCortoPlazo) {
                ValidarSaldosCartera(saldosCarteraCortoPlazo, movimiento.IdIcetex);
            } 


            var saldosCarteraLargoPlazo = await saldosCarteraRepository
               .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproductoLargoPlazo);
            if (idSubproducto == idSubproductoLargoPlazo)
            {
                ValidarSaldosCartera(saldosCarteraLargoPlazo, movimiento.IdIcetex);
            }


            var sumaSaldosCortoPlazo = SumaTotalSaldos(saldosCarteraCortoPlazo.Saldo1, saldosCarteraCortoPlazo.Saldo2, saldosCarteraCortoPlazo.Saldo3, saldosCarteraCortoPlazo.Saldo4);

            var sumaSaldosLargoPlazo = SumaTotalSaldos(saldosCarteraLargoPlazo.Saldo1, saldosCarteraLargoPlazo.Saldo2, saldosCarteraLargoPlazo.Saldo3, saldosCarteraLargoPlazo.Saldo4);

            if (sumaSaldosCortoPlazo > 0) { idSaldosCarteraAnterior = saldosCarteraCortoPlazo.Id; }
            else { idSaldosCarteraAnterior = saldosCarteraLargoPlazo.Id; }


            var saldosCarteraLargoPlazoAmortizacionDTO = new SaldosCarteraDTO()
            {
                Saldo1 = sumaSaldosCortoPlazo + sumaSaldosLargoPlazo,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = movimiento.IdIcetex,
                IdSubproducto = 6,
                IdSaldosCarteraAnt = idSaldosCarteraAnterior,
                IdMovimiento = movimiento.IdMovimientoDiario
            };


            if (sumaSaldosCortoPlazo > 0)
            {
                var nuevosSaldosCarteraCortoPlazo = (SaldosCarteraDTO)saldosCarteraCortoPlazo.Clone();
                nuevosSaldosCarteraCortoPlazo = this.ConfigurarSaldosACero(nuevosSaldosCarteraCortoPlazo);
                nuevosSaldosCarteraCortoPlazo.IdSaldosSolicitudCartera = null;
                nuevosSaldosCarteraCortoPlazo.IdMovimiento = movimiento.IdMovimientoDiario;
                nuevosSaldosCarteraCortoPlazo.IdSaldosCarteraAnt = saldosCarteraCortoPlazo.Id;
                var saldosCortoPlazo = this.MapearSaldosCarteraAfectaciones(nuevosSaldosCarteraCortoPlazo);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldosCortoPlazo);
            }

            if (sumaSaldosLargoPlazo > 0)
            {
                var nuevosSaldosCarteraLargoPlazo = (SaldosCarteraDTO)saldosCarteraLargoPlazo.Clone();
                nuevosSaldosCarteraLargoPlazo = this.ConfigurarSaldosACero(nuevosSaldosCarteraLargoPlazo);
                nuevosSaldosCarteraLargoPlazo.IdSaldosSolicitudCartera = null;
                nuevosSaldosCarteraLargoPlazo.IdMovimiento = movimiento.IdMovimientoDiario;
                nuevosSaldosCarteraLargoPlazo.IdSaldosCarteraAnt = saldosCarteraLargoPlazo.Id;
                var saldosLargoPlazo = this.MapearSaldosCarteraAfectaciones(nuevosSaldosCarteraLargoPlazo);
                await solicitudCarteraActivaRepository.GuardarSaldos(saldosLargoPlazo);
            }

            var saldosLargoPlazoAmortizacion = this.MapearSaldosCarteraAfectaciones(saldosCarteraLargoPlazoAmortizacionDTO);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldosLargoPlazoAmortizacion);

            return saldosCarteraLargoPlazoAmortizacionDTO;

        }

        private double SumaTotalSaldos(double saldo1, double saldo2, double saldo3, double saldo4)
        {
            return saldo1 + saldo2 + saldo3 + saldo4;
        }

    }
}