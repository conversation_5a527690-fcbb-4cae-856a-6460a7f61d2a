﻿using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Entities;

namespace ApiCartera.Application.Contracts.Persistence
{
    public interface ITasaCarteraRepository 
    {
        Task<TasasSaldosCarteraDTO> ObtenerTasasPorIdSolicitudYSubproducto(long idSolicitud, string codigoSubproducto);
        Task GuardarTasas(TasasSaldosCartera tasaCartera);
    }
}
