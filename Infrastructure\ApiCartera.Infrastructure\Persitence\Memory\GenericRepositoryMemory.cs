﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Infrastructure.Persitence.Memory
{
    public class GenericRepositoryMemory<TEntity>: IGenericRepository<TEntity> where TEntity: Entity, new()
    {
       
        protected static List<TEntity> Repository = new ();

        public Task<int> CrearAsync(TEntity entity)
        {
            entity.Id = Repository.Count() + 1;
            entity.FechaCreacion = entity.FechaUltimaActualizacion = DateTime.Now;
            Repository.Add(entity);
            return Task.FromResult(entity.Id);
        }

        public Task<TEntity> ActualizarAsync(TEntity entity)
        {
            entity.FechaUltimaActualizacion = DateTime.Now;
            Repository.Remove(Repository.First(element => element.Id == entity.Id));
            Repository.Add(entity);
            return Task.FromResult(entity);
        }

        public Task<List<TEntity>> ObtenerTodosAsync()
        {
            return Task.FromResult(Repository);
        }

        public Task<TEntity> ObtenerPorIdAsync(int id)
        {
            return Task.FromResult(Repository.FirstOrDefault(delito => delito.Id == id) ?? throw new NotFoundException());
        }

        public Task EliminarPorIdAsync(int id)
        {
            Repository.Remove(Repository.First(element => element.Id == id));
            return Task.CompletedTask;
        }
    }
}
