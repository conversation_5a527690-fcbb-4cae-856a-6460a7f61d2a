﻿namespace ApiCartera.Application.Resources;

public class Constants
{
    public const string ESTADO_DESEMBOLSO_EN_FIRME = "EN FIRME";
    public const string ESTADO_ALERTA_EN_FIRME = "ALERTA EN FIRME";
    public const string ESTADO_EXITOSO = "EXITOSO";
    public const string TIPO_BENEFICIO_DESCONOCIDO = "TIPO DE BENEFICIO DESCONOCIDO";
    public const string ALIANZAS_REEMBOLSABLE_LP_RA = "ALIANZAS REEMBOLSABLE LP RA";
    public const string ORI_CONDONABLE = "ORI CONDONABLE";
    public const string ORI_CONDONABLE_R_TERCEROS = "ORI CONDONABLE R TERCEROS";
    public const string ORI_REEMBOLSABLE_RT = "ORI REEMBOLSABLE RT";
    public const string PERIODICIDAD_NULL = "PERIODICIDAD NULL";
    public const int BASE_LIQUIDACION = 360;
    public const int IDSUBPRODUCTO_CP = 1;
    public const int IDSUBPRODUCTO_LP = 2;
    public const int PRIMER_DIA_MES = 1;
    public const int CONTADOR = 1;
    public const int MAX_REINTENTOS = 3;
    public const int DELAY_REINTENTOS = 1000;
    public const string DATASERVICE = "dataservice";
    public const int IDTIPOCARTERA_ESTUDIOS = 1;
    public const int IDTIPOCARTERA_AMORTIZACION = 2;
    public static DateTime FECHA_INICIAL_PROCESO_WORKER = new DateTime(2024, 02, 05);
    public const double MESES = 12.0;
    public const int TASA_IPC_SIN_MORA = 2;
    public const int TASA_IPC_CON_MORA = 4;
    public const string TIPOCARTERA_ESTUDIOS = "ESTUDIOS";
    public const string TIPOCARTERA_AMORTIZACION = "AMORTIZACION";
}
