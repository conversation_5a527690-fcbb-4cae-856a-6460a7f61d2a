﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.DTOs;
using ApiCartera.Infrastructure.Persitence.BusDataService;
using ApiCartera.Application.Exceptions;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence
{
    public class NuevosCreditosDataService(BusService busService, IConfiguration configuration) :
        GenericCustomRepositoryBusDataService<SaldosCartera>(busService, configuration), INuevoscreditosRepository
    {
        public async Task<DataQueryNuevosCreditodDTO> ObtenerDataNuevosCreditos(long idSolicitud, int noRelacion, int idSubProducto)
        {
            try
            {
                var url = $"mbt/nuevo-ingreso?idSolicitud={idSolicitud}&noRelacion={noRelacion}&idSubProducto={idSubProducto}";
                var respuestaDataNuevosCreditos = await busService.Services.Generico.Get<RespuestaListDataServiceDTO<DataQueryNuevosCreditodDTO>>(url);

                if (respuestaDataNuevosCreditos.Data == null || !respuestaDataNuevosCreditos.Data.Any())
                {
                    throw new ValidationException(new[]
                    {
                        new ValidationFailure()
                        {
                            PropertyName = $"{nameof(idSolicitud)} {nameof(noRelacion)}",
                            ErrorMessage = respuestaDataNuevosCreditos.Response
                        }
                    });
                }

                var datos = respuestaDataNuevosCreditos.Data;

                return datos.First();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                throw new ValidationException([
                    new ValidationFailure()
                    {
                        PropertyName = $"{nameof(idSolicitud)} {nameof(noRelacion)}",
                        ErrorMessage = ex.ToString()
                    }]);
            }
        }

        public async Task<bool> ValidarGiroInternacional(long idSolicitud)
        {
            try
            {
                var url = $"mbt/giro-internacional?idSolicitud={idSolicitud}";
                var respuestaGiroInternacional = await busService.Services.Generico.Get<RespuestaDataServiceDTO<GiroInternacionalDTO>>(url);


                return bool.Parse(respuestaGiroInternacional.Data.status);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return false;
            }
        }
    }
}
 