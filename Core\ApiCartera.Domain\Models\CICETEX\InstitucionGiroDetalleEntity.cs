﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("INSTITUCIONGIRODETALLE")]
public class InstitucionGiroDetalleEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public long IdSolicitud { get; set; }
    public DateTime? Fecha { get; set; }
    public double TotalGirar { get; set; }
    public int? NoRelacion { get; set; }
    public string? Estado { get; set; }
}
