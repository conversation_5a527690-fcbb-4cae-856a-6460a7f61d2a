﻿using FluentValidation.Results;
using ApiCartera.Domain.Exceptions;

namespace ApiCartera.Application.Exceptions
{
    public class ValidationException : BaseException
    {
        public ValidationException():base("Se presentaron errores de validación")
        {
            Errors = new Dictionary<string,string[]>();
        }

        public ValidationException(IEnumerable<ValidationFailure> failures) : this()
        {
            Errors = failures
                .GroupBy(e => e.PropertyName, e => e.ErrorMessage)
                .ToDictionary(failureGroup => failureGroup.Key, failureGroup => failureGroup.ToArray());
        }

        public IDictionary<string, string[]> Errors { get; }
    }
}
