﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation.Results;
using System.Globalization;
using System.Text.RegularExpressions;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionSaldoCapitalReintegroService(ISaldosCarteraRepository saldosCarteraRepository,
                                                        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
                                                        INuevoscreditosRepository nuevoscreditosRepository) : AfectacionesService, IAfectacionSaldoReintegro
    {

        public async Task<SaldosCarteraDTO> AfectacionSaldoReintegro(MovimientoAfectacionDTO dataAfectacionReintegroDTO)
        {
            SaldosCarteraDTO dataresult = new SaldosCarteraDTO();

            var (idSubProducto, core) = dataAfectacionReintegroDTO.ObtenerInfoSubproducto();

            DateTime fecha;
            string fechaTexto = dataAfectacionReintegroDTO.FechaEfectivaMovimiento;
            string[] formatos = { "yyyy-MM-dd", "yyyyMMdd" };

            if (!DateTime.TryParseExact(fechaTexto, formatos, CultureInfo.InvariantCulture, DateTimeStyles.None, out fecha))
            {
                throw new FormatException($"El formato de la fecha no es válido: '{fechaTexto}'");
            }

            NuevosCreditosDTO nuevosCreditosDTO = new NuevosCreditosDTO()
            {
                IdIcetex = dataAfectacionReintegroDTO.IdIcetex,
                MontoMovimientoCapital = dataAfectacionReintegroDTO.MontoMovimientoCapital,
                CodigoTipoMovimientoCapital = dataAfectacionReintegroDTO.CodigoTipoMovimientoCapital,
                CodigoSubproductoObligacion = dataAfectacionReintegroDTO.CodigoSubProducto,
                DescripcionMovimientoMemo = dataAfectacionReintegroDTO.DescripcionMovimientoMemo,
                CodigoNovedad = int.Parse(dataAfectacionReintegroDTO.CodigoNovedad)
            };

            ValidarMontoMovimientoCapital(dataAfectacionReintegroDTO.MontoMovimientoCapital);

            var dataSaldosCartera = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitud(nuevosCreditosDTO.IdIcetex, idSubProducto);
            dataSaldosCartera.IdSaldosCarteraAnt = dataSaldosCartera.Id;
            dataSaldosCartera.IdMovimiento = dataAfectacionReintegroDTO.IdMovimientoDiario;

            // Afectacion
            if ((nuevosCreditosDTO.CodigoTipoMovimientoCapital == 70) && (nuevosCreditosDTO.CodigoNovedad == 10) && (idSubProducto != 0))
            {
                nuevosCreditosDTO.DescripcionMovimientoMemo = ValidarYExtraerNumero(nuevosCreditosDTO.DescripcionMovimientoMemo ?? "").ToString();
                var dataPeticion = await nuevoscreditosRepository.ObtenerDataNuevosCreditos(nuevosCreditosDTO.IdIcetex, Convert.ToInt32(nuevosCreditosDTO.DescripcionMovimientoMemo), nuevosCreditosDTO.CodigoSubproductoObligacion);

                if (dataPeticion == null)
                    return new SaldosCarteraDTO();

                dataPeticion.idSubProducto = idSubProducto;

                if (core == "L")
                    return await ValidacionesDataLargoPlazo(nuevosCreditosDTO, dataPeticion, dataSaldosCartera, fecha);
                else if (core == "C")
                    return await ValidacionesDataCortoPlazo(nuevosCreditosDTO, dataPeticion, dataSaldosCartera, fecha);
            }

            // Reversion
            if ((nuevosCreditosDTO.CodigoTipoMovimientoCapital == 20) && (nuevosCreditosDTO.CodigoNovedad == 10) && (idSubProducto != 0))
            {
                return await RevertirSaldoCartera(nuevosCreditosDTO, idSubProducto, dataSaldosCartera, nuevosCreditosDTO.CodigoSubproductoObligacion);
            }

            return new SaldosCarteraDTO();
        }


        public static int ValidarYExtraerNumero(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return 0;

            bool starts010 = input.StartsWith("010");

            if (!starts010)
                return 0;

            try
            {
                string numberStr = string.Empty;
                if (starts010)
                {
                    var match = Regex.Match(input, @"010.*(?:\.|\s)(\d+)");
                    if (!match.Success)
                        return 0;

                    numberStr = match.Groups[1].Value;
                }

                if (int.TryParse(numberStr, out int result))
                    return result;

                return 0;
            }
            catch
            {
                return 0;
            }
        }


        public async Task<SaldosCarteraDTO> ValidacionesDataCortoPlazo(NuevosCreditosDTO nuevosCreditosDTO, DataQueryNuevosCreditodDTO dataQueryNuevosCreditodDTO, SaldosCarteraDTO dataSaldosCartera, DateTime fecha)
        {
            if (dataSaldosCartera == null)
                return new SaldosCarteraDTO();

            var saldosCarteraCP = (SaldosCarteraDTO)dataSaldosCartera.Clone();


            bool afectaSaldo1 = (dataQueryNuevosCreditodDTO.YearGiro < 2024) && (dataQueryNuevosCreditodDTO.SemestreGiro == 1);
            bool afectaSaldo4 = (dataQueryNuevosCreditodDTO.YearGiro > 2023) && (dataQueryNuevosCreditodDTO.SemestreGiro == 2);

            if ((dataQueryNuevosCreditodDTO.InstitucionGiro == TiposBeneficio.APORTES_IES && (dataQueryNuevosCreditodDTO.VALORAPORTEIES == null || dataQueryNuevosCreditodDTO.VALORAPORTEIES == 0)) || dataQueryNuevosCreditodDTO.InstitucionGiro == "")
            {
                dataQueryNuevosCreditodDTO.InstitucionGiro = TiposBeneficio.NO_APLICA_BENEFICIO;
            }

            switch (dataQueryNuevosCreditodDTO.InstitucionGiro)
            {
                case TiposBeneficio.APORTES_IES:
                    saldosCarteraCP.Saldo3 -= nuevosCreditosDTO.MontoMovimientoCapital;
                    if (saldosCarteraCP.Saldo3 < 0)
                        saldosCarteraCP.Saldo3 = 0;
                    break;

                case TiposBeneficio.CONTRIBUCION_IES:
                    saldosCarteraCP.Saldo2 -= nuevosCreditosDTO.MontoMovimientoCapital;
                    if (saldosCarteraCP.Saldo2 < 0)
                        saldosCarteraCP.Saldo2 = 0;
                    break;

                case TiposBeneficio.NO_APLICA_BENEFICIO:
                    if (dataQueryNuevosCreditodDTO.YearGiro <= 2023)
                    {
                        saldosCarteraCP.Saldo1 -= nuevosCreditosDTO.MontoMovimientoCapital;
                        if (saldosCarteraCP.Saldo1 < 0)
                            saldosCarteraCP.Saldo1 = 0;
                    }
                    else if (dataQueryNuevosCreditodDTO.YearGiro >= 2024)
                    {
                        saldosCarteraCP.Saldo4 -= nuevosCreditosDTO.MontoMovimientoCapital;
                        if (saldosCarteraCP.Saldo4 < 0)
                            saldosCarteraCP.Saldo4 = 0;
                    }
                    break;

                default:
                    break;
            }

            var saldosActualizados = this.MapearSaldosCarteraAfectaciones(saldosCarteraCP);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldosActualizados);

            return saldosCarteraCP;
        }

        public async Task<SaldosCarteraDTO> ValidacionesDataLargoPlazo(NuevosCreditosDTO nuevosCreditosDTO, DataQueryNuevosCreditodDTO dataQueryNuevosCreditodDTO, SaldosCarteraDTO dataSaldosCartera, DateTime fecha)
        {
            if (dataSaldosCartera == null)
                return new SaldosCarteraDTO();

            var saldosCarteraLPs = (SaldosCarteraDTO)dataSaldosCartera.Clone();

            if ((dataQueryNuevosCreditodDTO.InstitucionGiro == TiposBeneficio.APORTES_IES && (dataQueryNuevosCreditodDTO.VALORAPORTEIES == null || dataQueryNuevosCreditodDTO.VALORAPORTEIES == 0)) || dataQueryNuevosCreditodDTO.InstitucionGiro == "")
            {
                dataQueryNuevosCreditodDTO.InstitucionGiro = TiposBeneficio.NO_APLICA_BENEFICIO;
            }

            switch (dataQueryNuevosCreditodDTO.InstitucionGiro)
            {
                case TiposBeneficio.APORTES_IES:
                    saldosCarteraLPs.Saldo3 -= nuevosCreditosDTO.MontoMovimientoCapital;
                    if (saldosCarteraLPs.Saldo3 < 0)
                        saldosCarteraLPs.Saldo3 = 0;
                    break;

                case TiposBeneficio.CONTRIBUCION_IES:
                    saldosCarteraLPs.Saldo2 -= nuevosCreditosDTO.MontoMovimientoCapital;
                    if (saldosCarteraLPs.Saldo2 < 0)
                        saldosCarteraLPs.Saldo2 = 0;
                    break;


                case TiposBeneficio.NO_APLICA_BENEFICIO:
                    if (dataQueryNuevosCreditodDTO.YearGiro <= 2023)
                    {
                        saldosCarteraLPs.Saldo1 -= nuevosCreditosDTO.MontoMovimientoCapital;
                        if (saldosCarteraLPs.Saldo1 < 0)
                            saldosCarteraLPs.Saldo1 = 0;
                    }
                    else if (dataQueryNuevosCreditodDTO.YearGiro >= 2024)
                    {
                        saldosCarteraLPs.Saldo4 -= nuevosCreditosDTO.MontoMovimientoCapital;
                        if (saldosCarteraLPs.Saldo4 < 0)
                            saldosCarteraLPs.Saldo4 = 0;
                    }
                    break;

                default:
                    break;
            }

            var saldosActualizados = this.MapearSaldosCarteraAfectaciones(saldosCarteraLPs);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldosActualizados);

            return saldosCarteraLPs;
        }

        private async Task<SaldosCarteraDTO> RevertirSaldoCartera(NuevosCreditosDTO nuevosCreditosDTO, int idSubProducto, SaldosCarteraDTO dataSaldosCartera, int idsubproducto2)
        {
            if (dataSaldosCartera == null)
                return new SaldosCarteraDTO();

            var saldosRevertidos = (SaldosCarteraDTO)dataSaldosCartera.Clone();

            nuevosCreditosDTO.DescripcionMovimientoMemo = ValidarYExtraerNumero(nuevosCreditosDTO.DescripcionMovimientoMemo ?? "").ToString();
            var dataPeticion = await nuevoscreditosRepository.ObtenerDataNuevosCreditos(
                nuevosCreditosDTO.IdIcetex,
                Convert.ToInt32(nuevosCreditosDTO.DescripcionMovimientoMemo),
                idsubproducto2
            );

            if (dataPeticion == null)
                return saldosRevertidos;

            if ((dataPeticion.InstitucionGiro == TiposBeneficio.APORTES_IES && (dataPeticion.VALORAPORTEIES == null || dataPeticion.VALORAPORTEIES == 0)) || dataPeticion.InstitucionGiro == "")
            {
                dataPeticion.InstitucionGiro = TiposBeneficio.NO_APLICA_BENEFICIO;
            }

            bool afectaSaldo1 = dataPeticion.YearGiro <= 2023;
            bool afectaSaldo4 = dataPeticion.YearGiro >= 2024;

            if (dataPeticion.InstitucionGiro == TiposBeneficio.APORTES_IES &&
                (dataPeticion.VALORAPORTEIES == null || dataPeticion.VALORAPORTEIES == 0))
            {
                dataPeticion.InstitucionGiro = TiposBeneficio.NO_APLICA_BENEFICIO;
            }

            switch (dataPeticion.InstitucionGiro)
            {
                case TiposBeneficio.APORTES_IES:
                    saldosRevertidos.Saldo3 += nuevosCreditosDTO.MontoMovimientoCapital;
                    break;

                case TiposBeneficio.CONTRIBUCION_IES:
                    saldosRevertidos.Saldo2 += nuevosCreditosDTO.MontoMovimientoCapital;
                    break;

                case TiposBeneficio.NO_APLICA_BENEFICIO:
                    if (afectaSaldo1)
                        saldosRevertidos.Saldo1 += nuevosCreditosDTO.MontoMovimientoCapital;
                    else if (afectaSaldo4)
                        saldosRevertidos.Saldo4 += nuevosCreditosDTO.MontoMovimientoCapital;
                    break;

                default:
                    break;
            }
            var saldosActualizados = this.MapearSaldosCarteraAfectaciones(saldosRevertidos);
            await solicitudCarteraActivaRepository.GuardarSaldos(saldosActualizados);

            return saldosRevertidos;
        }

        private static void ValidarMontoMovimientoCapital(double monto)
        {
            if (monto <= 0)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
            {
                PropertyName = nameof(MovimientoAfectacionDTO.MontoMovimientoCapital),
                ErrorMessage = "El campo monto movimiento capital del movimiento es requerido para poder realizar las operaciones y debe ser mayor a cero"
            }
                ]);
            }
        }

    }
}
