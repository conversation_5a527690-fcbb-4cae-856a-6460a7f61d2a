﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models;

[EntityName("VM_REPORTE_BENEFICIO_AC001")]
public class MvReporteBeneficioAc001 : IEntity<string?>
{
    public string? Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public decimal Calculo_Saldo1 { get; set; }
    public decimal Calculo_Saldo2 { get; set; }
    public decimal Calculo_Saldo3 { get; set; }
    public decimal Calculo_Saldo4 { get; set; }
    public decimal CapitalExigible { get; set; }
    public decimal CapitalNoExigible { get; set; }
    public decimal CapitalVencido { get; set; }
    public decimal CapitalVigente { get; set; }
    public string? Ccn { get; set; }
    public string? Ciudad { get; set; }
    public string? CodigoDepartamento { get; set; }
    public int CodigoLinea { get; set; }
    public string? CodigoSubproducto { get; set; }
    public string? CodSniesInst { get; set; }
    public int CuotasPendientesPlanPagos { get; set; }
    public string? Departamento { get; set; }
    public string? Documento { get; set; }
    public int Estrato { get; set; }
    public DateOnly FechaGeneracion { get; set; }
    public DateOnly FechaMovimiento { get; set; }
    public string? Genero { get; set; }
    public int IdSignature { get; set; }
    public int IdSolicitud { get; set; }
    public string? Ies { get; set; }
    public string? Ipc_Beneficio { get; set; }
    public string? IpcContratacion { get; set; }
    public string? Linea { get; set; }
    public string? ModalidadCredito { get; set; }
    public string? NombreCompleto { get; set; }
    public decimal Saldo1 { get; set; }
    public decimal Saldo2 { get; set; }
    public decimal Saldo3 { get; set; }
    public decimal Saldo4 { get; set; }
    public string? Sisben { get; set; }
    public string? Sublinea { get; set; }
    public decimal SumaBeneficios { get; set; }
    public string? TipoBeneficio { get; set; }
    public string? TipoCartera { get; set; }
    public decimal Valor_Tasa_Beneficio { get; set; }
    public decimal Valor_Tasa_Contratacion { get; set; }
}
