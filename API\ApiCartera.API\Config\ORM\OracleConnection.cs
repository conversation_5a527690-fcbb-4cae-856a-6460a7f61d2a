﻿using ApiCartera.Infrastructure.Data.AdoDbContext;
using Oracle.ManagedDataAccess.Client;

namespace ApiCartera.API.Config.ORM;

public static class OracleDbConfiguration
{
    public static IServiceCollection OracleConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<OracleConnection>(provider =>
        {
            string connectionString = configuration.GetConnectionString(nameof(Oracle))!;
            OracleConnection connection = new(connectionString);
            return connection;
        });

        services.AddScoped<OracleDataContext>(provider =>
        {
            OracleConnection oracleConnection = provider.GetRequiredService<OracleConnection>();
            return new OracleDataContext(oracleConnection);
        });

        return services;
    }
}
