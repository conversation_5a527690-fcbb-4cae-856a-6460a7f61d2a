﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class SaldosCarteraDataService(BusService busService, IConfiguration configuration) : 
        GenericCustomRepositoryBusDataService<SaldosCartera>(busService, configuration), ISaldosCarteraRepository
    {
        public async Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorId(int id)
        {
            var url = $"saldoscartera-id?id={id}";

            var respuestaSaldosCartera = await busService.Services.Generico.Get<RespuestaDataServiceDTO<SaldosCarteraDTO>>(url);

            if (respuestaSaldosCartera.Response != TiposRespuestaDataService.EXITOSA)
            {
                throw new ValidationException([
                    new ValidationFailure()
                    {
                        PropertyName = nameof(id),
                        ErrorMessage = respuestaSaldosCartera.Response
                    }]);
            }
            return respuestaSaldosCartera.Data;
        }

        public async Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitud(long idSolicitud, int idSubproducto)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            string? url = $"saldoscartera?idSolicitud={idSolicitud}&idSubProducto={idSubproducto}";
            RespuestaDataServiceDTO<SaldosCarteraDTO>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<SaldosCarteraDTO>>(url);

                    if (respuesta.Response == TiposRespuestaDataService.EXITOSA)
                    {
                        return respuesta.Data;
                    }
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                }
                contador++;
            }
            List<ValidationFailure>? validaciones = new List<ValidationFailure>()
            {
                new ValidationFailure()
                {
                    PropertyName = $"{nameof(ObtenerSaldosCarteraPorIdSolicitud)} {Constants.DATASERVICE}",
                    ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                }
            };
            throw new ValidationException(validaciones);
        }

        public async Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitud(long idSolicitud, int idSubproducto, DateTime fecha)
        {
            var url = $"saldoscartera?idSolicitud={idSolicitud}&idSubProducto={idSubproducto}&fecha={fecha}";

            var respuestaSaldosCartera = await busService.Services.Generico.Get<RespuestaDataServiceDTO<SaldosCarteraDTO>>(url);

            if (respuestaSaldosCartera.Response != TiposRespuestaDataService.EXITOSA)
            {
                throw new ValidationException([
                    new ValidationFailure()
                    {
                        PropertyName = $"{nameof(idSolicitud)} {nameof(idSubproducto)}",
                        ErrorMessage = respuestaSaldosCartera.Response
                    }]);
            }
            return respuestaSaldosCartera.Data;
        }

        public Task<SaldosCarteraDTO> ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(long idSolicitud, string codigoSubproducto)
        {
            var url = $"obtenersaldoscarteraporsolicitudycodigosubproducto?idSolicitud={idSolicitud}&codigoSubproducto={codigoSubproducto}";

            return busService.Services.Generico.Get<SaldosCarteraDTO>(url);
        }
    }
}
