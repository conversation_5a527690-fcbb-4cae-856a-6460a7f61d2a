﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Domain.Features.Reportes.Services;
using ApiCartera.Domain.Models;

namespace ApiCartera.Application.Services.Reportes
{
     
    public class ReporteContribucionesAportesIESService(IRepository<MvReporteCiesAies, string> repository, IReporteContribucionesAportesIESRepository reporteBeneficioAcuerdo001Repository) : IReporteContribucionesAportesIESService
    {
        private readonly IRepository<MvReporteCiesAies, string> _repository = repository;
        public async Task<List<MvReporteCiesAies>> ObtenerReporte(DateTime desde, DateTime hasta)
        {
            List<MvReporteCiesAies>? response = await _repository.ExecuteViewAsync<MvReporteCiesAies>("VM_REPORTE_CIES_AIES");
            List<MvReporteCiesAies>? responseFiltrada = response.Where(registro => registro.FechaGiro >= new DateOnly(desde.Year, desde.Month, desde.Day) && registro.FechaGiro <= new DateOnly(hasta.Year, hasta.Month, hasta.Day)).ToList();
            return responseFiltrada;
        }
    }
}
