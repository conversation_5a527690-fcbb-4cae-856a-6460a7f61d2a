﻿using ApiCartera.API.Constants;
using ApiCartera.API.Wrappers;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Exceptions;
using System.Net;
using System.Text.Json;

namespace ApiCartera.API.Middleware
{
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IHostEnvironment _env;
        private readonly ILogger<ExceptionMiddleware> _logger;

        public ExceptionMiddleware(
            RequestDelegate next, 
            IHostEnvironment env,
            ILogger<ExceptionMiddleware> logger)
        {
            _next = next;
            _env = env;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(message: ex.Message, ex);
                context.Response.ContentType = TypeResponse.Json;
                var statusCode = (int)HttpStatusCode.InternalServerError;
                string result = string.Empty;

                switch (ex)
                {
                    case ValidationException validationException:
                        result = JsonSerializer.Serialize(new ResponseWrapper<object>(StatusCodes.Status400BadRequest, null, ex.Message, errors: validationException.Errors));
                        context.Response.StatusCode = StatusCodes.Status400BadRequest;
                        break;
                    case NotAuthorizeException exception:
                        result = JsonSerializer.Serialize(new ResponseWrapper<object>(StatusCodes.Status400BadRequest, null, exception.Message));
                        context.Response.StatusCode = StatusCodes.Status400BadRequest;
                        break;
                    case BaseException exception:
                        result = JsonSerializer.Serialize(new ResponseWrapper<object>(StatusCodes.Status400BadRequest, null, exception.Message));
                        context.Response.StatusCode = StatusCodes.Status400BadRequest;
                        break;
                    default:
                        context.Response.StatusCode = statusCode;
                        break;
                }
                if(string.IsNullOrEmpty(result))
                {
                    string trace = _env.IsDevelopment() ? ex?.StackTrace ?? string.Empty : ApiMessages.ServerErrorInternal500;
                    result = JsonSerializer.Serialize(new ResponseWrapper<object>(statusCode,null, trace));
                    context.Response.StatusCode = statusCode;
                }


                await context.Response.WriteAsync(result);
            }
        }
    }
}
