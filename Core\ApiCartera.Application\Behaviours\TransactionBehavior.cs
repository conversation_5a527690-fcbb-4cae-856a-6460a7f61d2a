﻿using ApiCartera.Application.Interfaces;
using MediatR;

namespace ApiCartera.Application.Behaviours;

public class TransactionBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
{
    private readonly ITransactionScope _transactionScope;

    public TransactionBehavior(ITransactionScope transactionScope)
    {
        _transactionScope = transactionScope;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (request!.GetType().Name.Contains("Command"))
        {
            try
            {
                _transactionScope.BeginTransaction();

                var response = await next();

                _transactionScope.Commit();

                return response;
            }
            catch (Exception)
            {
                _transactionScope.Rollback();
                throw;
            }
            finally
            {
                _transactionScope.Dispose();
            }
        }
        else
        {
            await _transactionScope.BeginTransactionAsync();

            var response = await next();

            await _transactionScope.CommitAsync();

            _transactionScope.Dispose();

            return response;
        }
    }
}
