﻿using ApiCartera.Application.Features.BeneficioAcuerdo001.Commands;
using ApiCartera.Application.Features.BeneficioAcuerdo001.ViewModels;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Handlers
{

    public class CalculoTasasHandler(
    ICalcularTasasCarteraService _calcularTasasCarteraService,
    IMapper _mapper) : IRequestHandler<CalculoTasasCommand, TasaCarteraVm>
    {
        public async Task<TasaCarteraVm> Handle(CalculoTasasCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _calcularTasasCarteraService.CalcularTasasYProporcionSaldos(request.IdSolicitud, request.IdSubproducto, request.FechaMovimiento);
            return _mapper.Map<TasaCarteraVm>(resultado);
        }
    }
}
