﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class ReporteBeneficioAcuerdo001DataService(BusService busService) : GenericRepositoryBusDataService<Solicitud>(busService), IReporteBeneficioAcuerdo001Repository
    {
        public async Task<List<BeneficioAcuerdo001DTO>> ObtenerReporte(DateTime desde, DateTime hasta)
        {
            var url = $"reportebeneficioacuerdo001?desde={desde.ToString("yyyy-MM-dd")}&hasta={hasta.ToString("yyyy-MM-dd")}";

            var respuesta= await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<BeneficioAcuerdo001DTO>>>(url);

            if (respuesta.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(desde)} y {nameof(hasta)}",
                        ErrorMessage = respuesta.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuesta.Data;
         
        }
    }
}
