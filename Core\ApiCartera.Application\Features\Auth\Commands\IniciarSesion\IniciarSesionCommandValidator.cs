﻿using FluentValidation;

namespace ApiCartera.Application.Features.Auth.Commands.IniciarSesion
{
    public class IniciarSesionCommandValidator : AbstractValidator<IniciarSesionCommand>
    {
        public IniciarSesionCommandValidator()
        {

            RuleFor(property => property.Email)
                .NotEmpty()
                .EmailAddress(FluentValidation.Validators.EmailValidationMode.Net4xRegex);

            RuleFor(property => property.Password)
                .NotEmpty();

            RuleFor(property => property.IP)
                .NotEmpty();
        }
    }
}
