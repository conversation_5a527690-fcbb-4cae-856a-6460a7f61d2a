﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesReversionPorPasoAlCobroServiceTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoPlazoReversionPasoAlCobro), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoPlazoConValorMayorSaldosAnterior_RetornaSaldosReversados(MovimientoDTO movimiento)
        {
            var saldosCarteraAnteriorCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 800000,
                Saldo2 = 0,
                Saldo3 = 200000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 148
            };
            var saldosCarteraPasoAlCobroCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 1000000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149,
                IdSaldosCarteraAnt = 148
            };
            var saldoCarteraActualPlazoAntes = new SaldosCarteraDTO()
            {
                Saldo1 = 2200000,
                Saldo2 = 0,
                Saldo3 = 600000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 156
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            solicitudCarteraActivaRepositoryMock.Setup(x => x.GuardarSaldos(
                                                It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                                            It.IsAny<long>(),
                                            It.IsAny<int>(),
                                            It.IsAny<DateTime>()))
                                        .Returns(Task.FromResult(saldosCarteraPasoAlCobroCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                                            It.IsAny<long>(),
                                            It.IsAny<int>()))
                                        .Returns(Task.FromResult(saldoCarteraActualPlazoAntes));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorId((int)saldosCarteraPasoAlCobroCortoPlazo.IdSaldosCarteraAnt))
                                        .Returns(Task.FromResult(saldosCarteraAnteriorCortoPlazo));

            var afectacionesReversionPorPasoAlCobroService = new AfectacionesReversionPorPasoAlCobroService(
                                                                    saldosCarteraRepositoryMock.Object,
                                                                    solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await afectacionesReversionPorPasoAlCobroService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(2000000, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(800000, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
        }        
        
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoPlazoReversionPasoAlCobro), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoPlazoConValorMenorSaldosAnterior_RetornaSaldosReversados(MovimientoDTO movimiento)
        {
            var saldosCarteraAnteriorCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 800000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 200000,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 148
            };
            var saldosCarteraPasoAlCobroCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 149,
                IdSaldosCarteraAnt = 148
            };
            var saldoCarteraActualPlazoAntes = new SaldosCarteraDTO()
            {
                Saldo1 = 1000000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 600000,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
                IdSaldosSolicitudCartera = 156
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            solicitudCarteraActivaRepositoryMock.Setup(x => x.GuardarSaldos(
                                                It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                                            It.IsAny<long>(),
                                            It.IsAny<int>(),
                                            It.IsAny<DateTime>()))
                                        .Returns(Task.FromResult(saldosCarteraPasoAlCobroCortoPlazo));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                                            It.IsAny<long>(),
                                            It.IsAny<int>()))
                                        .Returns(Task.FromResult(saldoCarteraActualPlazoAntes));
            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorId((int)saldosCarteraPasoAlCobroCortoPlazo.IdSaldosCarteraAnt))
                                        .Returns(Task.FromResult(saldosCarteraAnteriorCortoPlazo));

            var afectacionesReversionPorPasoAlCobroService = new AfectacionesReversionPorPasoAlCobroService(
                                                                    saldosCarteraRepositoryMock.Object,
                                                                    solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await afectacionesReversionPorPasoAlCobroService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(1200000, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(0, resultado.Saldo3);
            Assert.Equal(800000, resultado.Saldo4);
        }
    }
}
