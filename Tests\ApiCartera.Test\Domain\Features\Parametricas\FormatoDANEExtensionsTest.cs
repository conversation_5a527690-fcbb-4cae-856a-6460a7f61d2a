﻿using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Domain.Features.Parametricas.Extensions;
using Sarlaft.Domain.Features.Parametricas.ValueObjects;

namespace Sarlaft.Test.Domain.Features.Parametricas
{
    public class FormatoDANEExtensionsTest
    {
        [Fact]
        public void ValidarFormato()
        {
            //Arrange
            DANEExcel dane = new()
            { 
                CodigoMunicipio = "1",
                CodigoDepartamento = "2",
                CodigoDANE = "02001",
                Departamento = "Santander",
                Municipio = "Bucaramanga",
                Poblacion = 329817
            };
            int digitosCiudad = 3;
            int digitosDepartamento = 2;

            //Act
            Ciudad ciudad = new()
            {
                CodigoDANE = dane.CodigoMunicipio.FormatearNumeroConCeros(digitosCiudad),
                Poblacion = dane.Poblacion
            };

            Departamento departamento= new()
            {
                CodigoDANE = dane.CodigoDepartamento.FormatearNumeroConCeros(digitosDepartamento)
            };

            //Assert
            Assert.Equal(ciudad.CodigoDANE.Count(), digitosCiudad);
            Assert.Equal(departamento.CodigoDANE.Count(), digitosDepartamento);
            Assert.Equal(ciudad.CodigoDANE, $"00{dane.CodigoMunicipio}");
            Assert.Equal(departamento.CodigoDANE, $"0{dane.CodigoDepartamento}");

        }
    }
}
