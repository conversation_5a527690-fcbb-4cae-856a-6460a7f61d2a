﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Movimientos.DTOs;
using ApiCartera.Domain.Features.Movimientos.Services;

namespace ApiCartera.Application.Services.Movimientos
{
    public class MovimientoSolicitudService(IMovimientoRepository movimientoRepository) : IMovimientoSolicitudService
    {
        public async Task<List<ConsultaMovimientoDTO>> ObtenerMovimientosSolicitudPorSubproducto(ParametrosConsultaMovimientoSolicitudDTO consultarMovimientoSolicitud)
        {
            var response = await movimientoRepository.ConsultarMovimientoCredito(consultarMovimientoSolicitud);
            return response;
        }
    }
}
