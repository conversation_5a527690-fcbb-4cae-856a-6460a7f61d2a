﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ApiCartera.Domain.Features.DivisionSaldos.DTOs;

public class SolicitudDTO
{
    [JsonProperty("tasaContratacion")]
    private object TasaContratacionRaw
    {
        set
        {
            if (value is JObject nilObject &&
                nilObject["@nil"]?.ToString() == "true")
            {
                TasaContratacion = 0.0;
            }
            else if (value is double doubleValue)
            {
                TasaContratacion = doubleValue;
            }
        }
    }

    [JsonProperty("marcaAnterior")]
    private object MarcaAnteriorRaw
    {
        set
        {
            if (value is JObject nilObject &&
                nilObject["@nil"]?.ToString() == "true")
            {
                MarcaAnterior = "";
            }
            else if (value is string stringValue)
            {
                MarcaAnterior = stringValue;
            }
        }
    }

        [JsonProperty("marca")]
        private object MarcaRaw
        {
            set
            {
                if (value is JObject nilObject &&
                    nilObject["@nil"]?.ToString() == "true")
                {
                    Marca = "";
                }
                else if (value is string stringValue)
                {
                    Marca = stringValue;
                }
            }
        }

    public int Id { get; set; }
    public long IdSolicitante { get; set; }
    public int IdTipoLinea { get; set; }
    public int IdTipoSublinea { get; set; }
    public string? TipoLinea { get; set; }
    public string? TipoSublinea { get; set; }
    public double Puntos { get; set; }
    public double TasaEfectiva { get; set; }
    public double TasaContratacion { get; set; }
    public double IPC { get; set; }
    public double IPC2 { get; set; }
    public double IPC4 { get; set; }
    public int DiasMora { get; set; }
    public string? Marca { get; set; } = "";
    public string? MarcaAnterior { get; set; }
    [JsonProperty("numTotalCuotas")]
    public int NumTotalCuotas { get; set; }
    public double SumaFactor2 { get; set; }
    public double SumaFactor3 { get; set; }
    public double BeneficioAcumulado { get; set; }
}   
