﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Resources;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class SolicitudCarteraActivaDataService(BusService busService, IConfiguration configuration) 
        : GenericCustomRepositoryBusDataService<SaldosSolicitudCartera>(busService, configuration), 
        ISolicitudCarteraActivaRepository
    {
        private const int ESTADO_GIRADO = 1;

        public async Task<List<SaldosSolicitudCarteraDTO>> ObtenerPorIdSolicitud(long idSolicitud)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            var url = $"saldoscartera-solicitud?idSolicitud={idSolicitud}&girado={ESTADO_GIRADO}";
            RespuestaDataServiceDTO<List<SaldosSolicitudCarteraDTO>>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<SaldosSolicitudCarteraDTO>>>(url);

                    if (respuesta.Response == TiposRespuestaDataService.EXITOSA)
                    {
                        return respuesta.Data;
                    }                        
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                }
                contador++;
            }

            List<ValidationFailure>? validaciones = new List<ValidationFailure>()
            {
                new()
                {
                    PropertyName = $"{nameof(ObtenerPorIdSolicitud)} {Constants.DATASERVICE}",
                    ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                }
            };
            throw new ValidationException(validaciones);            
        }

        public async Task<List<DesembolsoDTO>> ObtenerDesembolsosPorIdSolicitud(long idSolicitud)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            var url = $"desembolsos-solicitud?idSolicitud={idSolicitud}";
            RespuestaDataServiceDTO<List<DesembolsoDTO>>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<DesembolsoDTO>>>(url);

                    if (respuesta.Response == TiposRespuestaDataService.EXITOSA)
                    {
                        return respuesta.Data;
                    }
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                }
                contador++;
            }
            var validaciones = new List<ValidationFailure>()
            {
                new()
                {
                    PropertyName = $"{nameof(ObtenerDesembolsosPorIdSolicitud)} {Constants.DATASERVICE}",
                    ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                }
            };
            throw new ValidationException(validaciones);
        }        
        
        public async Task<DesembolsoDTO> ObtenerDesembolsoPorNoRelacion(long idSolicitud, int noRelacion)
        {
            var url = $"desembolsos-norelacion?idSolicitud={idSolicitud}&idEstado={TiposEstadoDesembolso.EN_FIRME}&modalidadRubro={TiposModalidadRubro.MATRICULA}&noRelacion={noRelacion}";

            var respuestaDesembolsos = await busService.Services.Generico.Get<RespuestaDataServiceDTO<DesembolsoDTO>>(url);

            if (respuestaDesembolsos.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(idSolicitud)} - {nameof(noRelacion)}",
                        ErrorMessage = respuestaDesembolsos.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaDesembolsos.Data;
        }

        public async Task<DesembolsoDTO> ObtenerDesembolsoPorNoRelacionGenerica(long idSolicitud, int noRelacion, string modalidadRubro)
        {
            var url = $"desembolsos-norelacion?idSolicitud={idSolicitud}&idEstado={TiposEstadoDesembolso.EN_FIRME}&modalidadRubro={modalidadRubro}&noRelacion={noRelacion}";

            var respuestaDesembolsos = await busService.Services.Generico.Get<RespuestaDataServiceDTO<DesembolsoDTO>>(url);

            if (respuestaDesembolsos.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(idSolicitud)} - {nameof(noRelacion)}",
                        ErrorMessage = respuestaDesembolsos.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaDesembolsos.Data;
        }



        public async Task GuardarSaldos(SaldosCartera saldosCartera)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            bool validacion = false;
            string? url = "registro-saldoscartera";
            var nuevoSaldosCartera = new
            {
                saldo1 = saldosCartera.Saldo1,
                saldo2 = saldosCartera.Saldo2,
                saldo3 = saldosCartera.Saldo3,
                saldo4 = saldosCartera.Saldo4,
                idSubProducto = saldosCartera.IdSubproducto,
                idSaldosSolicitudCartera = saldosCartera.IdSaldosSolicitudCartera,
                idSaldosCarteraAnt = saldosCartera.IdSaldosCarteraAnt,
                idMovimiento = saldosCartera.IdMovimiento
            };
            RespuestaDataServiceDTO<dynamic>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Post<RespuestaDataServiceDTO<dynamic>, dynamic>(url, nuevoSaldosCartera);
                    contador = maxReintentos + 1;
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                    contador++;
                    if (contador == 4)
                        validacion = true;
                }               
            }
            if (validacion)
            {
                List<ValidationFailure>? validaciones = new()
                {
                    new()
                    {
                        PropertyName = $"{nameof(GuardarSaldos)} {Constants.DATASERVICE}",
                        ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                    }
                };
                throw new ValidationException(validaciones);
            }            
        }

        public async Task<List<int>> ObtenerSegmentacionBeneficioAcuerdo001()
        {
            var url = $"obtenersegmentacionbeneficioacuerdo001";

            var respuestaObtenerSolicitud = await busService.Services.Generico.Get<RespuestaDataServiceDTO<List<int>>>(url);

            if (respuestaObtenerSolicitud.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = $"{nameof(List<int>)}",
                        ErrorMessage = respuestaObtenerSolicitud.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaObtenerSolicitud.Data;
        }

        public async Task<SolicitudDTO> ObtenerSolicitudPorId(long idSolicitud, DateTime fechamov)
        {
            int maxReintentos = Constants.MAX_REINTENTOS;
            int contador = Constants.CONTADOR;
            string? url = $"obtenersolicitudporid?idSolicitud={idSolicitud}&fechaMov={fechamov.ToString("dd-MM-yyyy")}";

            RespuestaDataServiceDTO<SolicitudDTO>? respuesta = null;

            while (contador <= maxReintentos)
            {
                try
                {
                    respuesta = await busService.Services.Generico.Get<RespuestaDataServiceDTO<SolicitudDTO>>(url);

                    if (respuesta.Response == TiposRespuestaDataService.EXITOSA)
                    {
                        return respuesta.Data;
                    }
                }
                catch
                {
                    await Task.Delay(Constants.DELAY_REINTENTOS);
                }
                contador++;
            }
            List<ValidationFailure>? validaciones = new List<ValidationFailure>()
            {
                new()
                {
                    PropertyName = $"{nameof(ObtenerSolicitudPorId)} {Constants.DATASERVICE}",
                    ErrorMessage = respuesta?.Response ?? Resource1.ErrorReintentos
                }
            };
            throw new ValidationException(validaciones);           
        }
    }
}
