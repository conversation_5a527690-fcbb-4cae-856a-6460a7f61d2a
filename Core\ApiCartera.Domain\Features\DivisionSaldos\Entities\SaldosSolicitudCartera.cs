﻿using ApiCartera.Domain.Features.Shared.Entities;

namespace ApiCartera.Domain.Features.DivisionSaldos.Entities
{
    public class SaldosSolicitudCartera : Entity
    {
        public long IdSolicitud { get; set; }
        public long IdSignature { get; set; }
        public double SaldoCapitalVigente { get; set; }
        public double SaldoTotalCapital { get; set; }
        public double SaldoCapitalVencido { get; set; }
        public int IdSubproducto { get; set; }
        public int IdTipoCartera { get; set; }
        public int CuotasPendientesPagar {  get; set; }
        public DateTime? FechaFinalizacionPlanPagos {  get; set; }
    }
}
