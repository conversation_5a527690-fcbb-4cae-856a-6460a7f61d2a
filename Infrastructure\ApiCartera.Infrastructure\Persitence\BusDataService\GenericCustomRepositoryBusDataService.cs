﻿using ICETEX.Librerias.BusServicios;
using MediatR;
using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Attributes;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Shared.Entities;
using ApiCartera.Infrastructure.Persitence.BusDataService.Models;
using System.Reflection;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class GenericCustomRepositoryBusDataService<TEntity>: IGenericRepository<TEntity> where TEntity : Entity, new()
    {
        protected RoutesBusAttribute RoutesBus => typeof(TEntity).GetCustomAttribute<RoutesBusAttribute>() ?? throw new NullException("No se encontro el atributo RoutesBus de " + typeof(TEntity).Name);

        private readonly BusService busService;
        private readonly IConfiguration configuration;

        public GenericCustomRepositoryBusDataService(BusService busService,
        IConfiguration configuration)
        {
            this.busService = busService;
            this.configuration = configuration;

            this.busService.Settings.WithUrl(configuration.GetSection("BusService")["urlBusServiciosCustom"]);
        }

        protected string GetRoute(string route) => "/sf/" + route;

        public virtual async Task<int> CrearAsync(TEntity entity)
        {
            string route = this.GetRoute(this.RoutesBus.Crear);
            int num = await busService.Services.Generico.Post<int, TEntity>(route, entity);
            return num;
        }

        public virtual async Task<TEntity> ActualizarAsync(TEntity entity)
        {
            string route = this.GetRoute(this.RoutesBus.Actualizar);
            await busService.Services.Generico.Post<int, TEntity>(route, entity);
            return entity;
        }

        public virtual async Task<List<TEntity>> ObtenerTodosAsync()
        {
            string route = this.GetRoute(this.RoutesBus.ObtenerTodos);
            var request = await busService.Services.Generico.Get<Registro<List<TEntity>>>(route);
            return request.registros;
        }

        public virtual async Task<TEntity> ObtenerPorIdAsync(int id)
        {
            string route = Path.Combine(this.GetRoute(this.RoutesBus.ObtenerPorId), id.ToString());
            var request = await busService.Services.Generico.Get<Registro<TEntity>>(route);
            return request.registros;
        }

        public virtual async Task EliminarPorIdAsync(int id)
        {
            string route = Path.Combine(this.GetRoute(this.RoutesBus.Eliminar), id.ToString());
            Unit unit = await busService.Services.Generico.Post<Unit, Unit>(route, Unit.Value);
        }
    }
}
