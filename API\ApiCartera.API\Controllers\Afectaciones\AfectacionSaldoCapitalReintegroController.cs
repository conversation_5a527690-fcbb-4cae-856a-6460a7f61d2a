﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Afectaciones
{
    [Route("api/[controller]")]
    [ApiController]
    public class AfectacionSaldoCapitalReintegroController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("AfectacionSaldoCapitalReintegro")]
        [ProducesResponseType<ResponseWrapper<SaldoCarteraVm>>(StatusCodes.Status200OK)]
        public async Task<SaldoCarteraVm> AfectacionSaldoCapitalReintegro(
            [FromBody] AfectacionSaldoCapitalReintegroCommand request) => await _mediator.Send(request);
    }
}
