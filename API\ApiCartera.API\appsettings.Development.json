{"BusService": {"urlBusServicios": "http://gtw-dev.apps.gate.icetex.gov.co", "urlBusServiciosCustom": "http://gtw-dev.apps.gate.icetex.gov.co", "tokenPortal": "SGhxWEZoX3R2R1pVeWV4WDRXV3hGQmZxc3ZRYTo4RVJxNDYxdDlfWXVCcDFoU253OU5lTk04eDhh", "tokenCartera": "su<PERSON>en"}, "ConnectionStrings": {"Oracle": "DATA SOURCE=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SICCI)));USER ID=ICETEX_NUEVO; Password=************;"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "/Logs/ApiCartera-.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "<PERSON><PERSON>", "Environment": "Development"}}}