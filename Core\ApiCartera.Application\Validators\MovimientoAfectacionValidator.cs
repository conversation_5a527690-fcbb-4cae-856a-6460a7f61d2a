﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using FluentValidation;

namespace ApiCartera.Application.Validators
{
    public class MovimientoAfectacionValidator : AbstractValidator<MovimientoAfectacionDTO>
    {
        public MovimientoAfectacionValidator()
        {
            RuleFor(x => x.Monto<PERSON>ovimientoCapital)
                .NotNull()
                .NotEmpty()
                .GreaterThan(0)
                .WithMessage(ValidationMessages.MovimientoValorGiro);
        }

    }
}
