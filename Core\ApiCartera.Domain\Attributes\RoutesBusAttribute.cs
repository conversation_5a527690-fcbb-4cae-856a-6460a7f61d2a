﻿namespace ApiCartera.Domain.Attributes
{
    public class RoutesBusAttribute : Attribute
    {
        public string ObtenerTodos { get; private set; }
        public string ObtenerPorId { get; private set; }
        public string Crear { get; private set; }
        public string Actualizar { get; private set; }
        public string Eliminar { get; private set; }

        public RoutesBusAttribute(string obtenerTodos = "",string obtenerPorId="", string crear = "", string actualizar = "", string eliminar = "")
        {
            ObtenerPorId = obtenerPorId;
            ObtenerTodos = obtenerTodos;
            Crear = crear;
            Actualizar = actualizar;
            Eliminar = eliminar;
        }

    }
}
