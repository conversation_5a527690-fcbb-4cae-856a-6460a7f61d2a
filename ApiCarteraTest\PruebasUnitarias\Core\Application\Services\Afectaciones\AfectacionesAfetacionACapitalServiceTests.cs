﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesAfetacionACapitalServiceTests
    {
        private readonly SaldosCarteraDTO saldoCartera = new()
        {
            Saldo1 = 8500000,
            Saldo2 = 0,
            Saldo3 = 400000,
            Saldo4 = 0,
            IdSolicitud = 6272827,
            IdSubproducto = 6,
            CodigoSubproducto = "1101",
            IdSaldosSolicitudCartera = 3
        };

        private readonly AfectacionesAfetacionACapitalService _afectacionesAfetacionACapitalService;
        private readonly Mock<ISaldosCarteraRepository> _saldosCarteraRepositoryMock;
        private readonly Mock<ISolicitudCarteraActivaRepository> _solicitudCarteraActivaRepositoryMock;

        public AfectacionesAfetacionACapitalServiceTests()
        {
            _saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            _saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(saldoCartera));
            _solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            _afectacionesAfetacionACapitalService = new AfectacionesAfetacionACapitalService(
                _saldosCarteraRepositoryMock.Object,
                _solicitudCarteraActivaRepositoryMock.Object);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosPorAfectacionACapitalCortoPlazo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoDesembolsos_RetornaSaldosDiminuidosPorAfectacionACapital(MovimientoDTO movimiento)
        {
            var resultado = await _afectacionesAfetacionACapitalService.CalcularSaldosCartera(movimiento);
            Assert.NotNull(resultado);
            Assert.Equal(7000000, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(400000, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
            Assert.NotEqual(8500000, resultado.Saldo1);
        }       
        
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientosPorAfectacionACapitalLargoPlazo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoLargoDesembolsos_RetornaSaldosDiminuidosPorAfectacionACapital(MovimientoDTO movimiento)
        {
            var resultado = await _afectacionesAfetacionACapitalService.CalcularSaldosCartera(movimiento);
            Assert.NotNull(resultado);
            Assert.Equal(6500000, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(400000, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
            Assert.NotEqual(8500000, resultado.Saldo1);
        }
    }
}
