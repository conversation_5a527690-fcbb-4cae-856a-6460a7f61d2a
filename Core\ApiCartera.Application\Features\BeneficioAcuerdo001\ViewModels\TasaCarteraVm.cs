﻿namespace ApiCartera.Application.Features.BeneficioAcuerdo001.ViewModels
{
    public record TasaCarteraVm(
            int Id,
            long IdSolicitud,
            double TasaMinimaSaldo1,
            double TasaMinimaSaldo2,
            double TasaMinimaSaldo3,
            double TasaMinimaSaldo4,
            double TasaMaximaSaldo1,
            double TasaMaximaSaldo2,
            double TasaMaximaSaldo3,
            double TasaMaximaSaldo4,
            double ProporcionSaldo1,
            double ProporcionSaldo2,
            double ProporcionSaldo3,
            double ProporcionSaldo4,
            int IdSubproducto,
            string CodigoSubproducto,
            int? IdMovimiento, 
            int? IdSaldosSolicitudCartera);
}
