using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionesSaldosACapitalHandler(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
        IMapper _mapper) : IRequestHandler<AfectacionesSaldosACapitalCommand, SaldoCarteraVm>
    {
        private readonly CalcularSaldosCarteraAfectacionFactory _calcularSaldosCarteraFactory = new(saldosCarteraRepository, solicitudCarteraActivaRepository);
        public async Task<SaldoCarteraVm> Handle(AfectacionesSaldosACapitalCommand request, CancellationToken cancellationToken)
        {
            ICalcularSaldosCarteraAfectacionService calcularSaldosCartera = _calcularSaldosCarteraFactory.ObtenerCalculadoraSaldos(request.Movimiento.CodigoNovedad);
            var resultado = await calcularSaldosCartera.CalcularSaldosCartera(request.Movimiento);
            return _mapper.Map<SaldoCarteraVm>(resultado);
        }
    }
}