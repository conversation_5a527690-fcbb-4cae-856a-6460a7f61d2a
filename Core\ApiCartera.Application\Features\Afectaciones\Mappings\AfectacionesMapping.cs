﻿using ApiCartera.Application.Features.Afectaciones.ViewModels;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using AutoMapper;

namespace ApiCartera.Application.Features.Afectaciones.Mappings
{
    public class AfectacionesMapping : Profile
    {
        public AfectacionesMapping()
        {
            CreateMap<SaldosPorAfectacionDTO, SaldosPorAfectacionVm>();
            CreateMap<SaldosCarteraDTO, SaldoCarteraVm>();
        }
    }
}
