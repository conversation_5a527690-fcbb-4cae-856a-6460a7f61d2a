﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("MBT_CONTROL_DIVISION_SALDOS")]
public class MbtControlDivisionSaldosEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int? IdSaldosSolicitudCartera { get; set; }
    public int? Estado_Division { get; set; }
    public long? IdSolicitud { get; set; }
    public string? Tipo_Fallo_Division { get; set; }
}
