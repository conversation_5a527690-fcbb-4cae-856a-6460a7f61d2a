using Sarlaft.Domain.Features.Files.Constants;
using Sarlaft.Domain.Features.Files.Enums;
using Sarlaft.Domain.Features.Files.Extensions;

namespace Sarlaft.Test.Domain.Features.Files
{
    public class ConversionSizesTest
    {
        [Fact]
        public void ConvertTest()
        {
            double valueInMb = 1;


            var valueInGb = valueInMb.ConvertFromTo(SizesScale.MegaByte, SizesScale.GigaByte);

            Assert.True(valueInMb > valueInGb);
        }

        [Fact]
        public void ConvertFromBytesTest()
        {
            double valueInByte = 1024;


            var valueInKb = valueInByte.ConvertBytesTo(SizesScale.KiloByte);

            Assert.True(valueInByte > valueInKb);
        }
    }
}