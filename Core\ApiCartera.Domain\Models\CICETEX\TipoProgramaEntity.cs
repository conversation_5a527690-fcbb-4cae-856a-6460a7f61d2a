﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("TIPOPROGRAMA")]
public class TipoProgramaEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public string? CodSnies_Prog { get; set; }
    public string? Dur_Codigo { get; set; }
}
