﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.Services;
using ApiCartera.Domain.Models;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Reportes.Handlers
{
    public class BeneficioAcuerdo001QueryHandler(
        IReporteBeneficioAcuerdo001Service reporteBeneficioAcuerdo001Service,
        IMapper mapper
        ) : IRequestHandler<BeneficioAcuerdo001Query, List<BeneficioAcuerdo001Vm>>
    {        
        public async Task<List<BeneficioAcuerdo001Vm>> Handle(BeneficioAcuerdo001Query request, CancellationToken cancellationToken)
        {
            List<MvReporteBeneficioAc001>? resultado = await reporteBeneficioAcuerdo001Service.ObtenerReporte(request.Desde, request.Hasta);
            
            return mapper.Map<List<BeneficioAcuerdo001Vm>>(resultado);
        }
    }
}
