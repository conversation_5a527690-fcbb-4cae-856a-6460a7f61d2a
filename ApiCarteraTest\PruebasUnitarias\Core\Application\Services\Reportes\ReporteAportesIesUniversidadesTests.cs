﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Reportes;
using ApiCartera.Domain.Features.Reportes.DTOs;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Reportes;

public class ReporteAportesIesUniversidadesTests
{
    private readonly Mock<IReporteAportesIESUniversidadesRepository> _reporte;
    private readonly ReporteAportesIESUniversidadesService _obtenerReporte;

    public ReporteAportesIesUniversidadesTests()
    {
        _reporte = new Mock<IReporteAportesIESUniversidadesRepository>();

        _obtenerReporte = new ReporteAportesIESUniversidadesService(
                   _reporte.Object     
            );
    }

    [Fact]
    private async void EjecutarReportePorLoMenosUnaVez()
    {
        DateTime desde = new DateTime(2025,2,01);
        DateTime hasta = new DateTime(2025, 2, 28);
        int page = 1;
        int pageSize = 100;

        await _obtenerReporte.ObtenerReporte(desde, hasta, page);

        _reporte.Verify(x => x.ObtenerReporte(desde, hasta, pageSize,page), Times.AtLeastOnce());
    }

    [Fact]
    private async void RetornaTipoDeDatoListAportesIESUniversidadesDto()
    {
        DateTime desde = new DateTime(2025, 2, 01);
        DateTime hasta = new DateTime(2025, 2, 28);
        int page = 1;
        int pageSize = 100;

        _reporte.Setup(x => x.ObtenerReporte(desde, hasta, pageSize, page)).ReturnsAsync(new List<AportesIESUniversidadesDTO>());

        var resultado = await _obtenerReporte.ObtenerReporte(desde, hasta, page);

        Assert.True(resultado.GetType() == typeof(List<AportesIESUniversidadesDTO>));
    }
}
