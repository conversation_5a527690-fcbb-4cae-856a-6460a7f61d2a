﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("TIPOSUBLINEACREDITO")]
public class TipoSubLineaCreditoEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int IdTipoLinea { get; set; }
    public int IdTipoSublinea  { get; set; }
    public double PorcCarteraPlan { get; set; }
}
