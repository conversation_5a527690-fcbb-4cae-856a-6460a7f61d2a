﻿using AutoMapper;
using MediatR;
using ApiCartera.Application.Contracts.Auth;
using ApiCartera.Application.Contracts.Auth.Models;
using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Usuarios.Entities;
using ApiCartera.Domain.Features.Usuarios.Messages;

namespace ApiCartera.Application.Features.Auth.Commands.IniciarSesion
{
    public class IniciarSesionCommandHandler (
        IAuthService authService, 
        IUsuarioPermitidoRepository usuarioPermitidoRepository,
        IMapper mapper)
        : IRequestHandler<IniciarSesionCommand, InicioSesionResponse>
    {


        public async Task<InicioSesionResponse> Handle(IniciarSesionCommand request, CancellationToken cancellationToken)
        {
            var model = mapper.Map<InicioSesionRequest>(request);
            var response = await authService.IniciarSesion(model);
            var usuariosPermitidos = await usuarioPermitidoRepository.ObtenerTodosAsync();
            if (!usuariosPermitidos.Any(usuario => usuario.Email == request.Email))
                throw new NotAuthorizeException(UsuarioMessages.UsuarioNoPermitido);
            return response;
        }
    }
}
