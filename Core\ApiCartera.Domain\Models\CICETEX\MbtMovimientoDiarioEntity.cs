﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("MBT_MOVIMIENTODIARIO")]
public class MbtMovimientoDiarioEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int IdMovimientoDiario { get; set; }
    public long IdIcetex { get; set; }
    public long IdSignature { get; set; }
    public decimal SdoTotalCapitalVigente { get; set; }
    public DateTime FechaEfectivaMovimiento { get; set; }
    public DateTime FechaPosteo { get; set; }
    public decimal MontoMovimientoDeCapital { get; set; }
    public int CodigoTipoMovtoDeCapital { get; set; }
    public string NombreTipoMovtoDeCapital { get; set; }
    public string CodSubproductoXObligacion { get; set; }
    public decimal SaldoTotalDeCapital { get; set; }
    public decimal SaldoCapitalVencido { get; set; }
    public string DescripcionMovtoMemo { get; set; }
    public string CodNovedad { get; set; }
    public string TipoCartera { get; set; }
    public int CuotasPendientesPorPagar { get; set; }
    public string? DescripcionNovacion { get; set; }
    public string? ReferenciaCus { get; set; }
    public int Year { get; set; }
    public int Semestre { get; set; }
    public string NombreArchivo { get; set; }
    public DateTime FechaEjecucion { get; set; }
    public int ControlMovimiento { get; set; }

}

