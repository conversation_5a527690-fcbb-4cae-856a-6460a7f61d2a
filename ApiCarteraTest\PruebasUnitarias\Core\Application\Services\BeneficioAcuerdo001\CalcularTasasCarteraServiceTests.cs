﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.BeneficioAcuerdo001;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.BeneficioAcuerdo001
{
    public class CalcularTasasCarteraServiceTests
    {
        [Fact]
        public async Task CalcularPropocion_RecibeTasaMinimasYMaximasParaCortoPlazo_RetornaPropociones()
        {
            var tasasCarteraCortoPlazo = new TasasSaldosCarteraDTO()
            {
                IdSolicitud = 123581,
                TasaMaximaSaldo1 = 20.5,
                TasaMaximaSaldo2 = 30.5,
                TasaMaximaSaldo3 = 14.6,
                TasaMaximaSaldo4 = 16.9,
                TasaMinimaSaldo1 = 10.5,
                TasaMinimaSaldo2 = 15.5,
                TasaMinimaSaldo3 = 7.3,
                TasaMinimaSaldo4 = 10.45,
                IdSubproducto = 123,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularProporcion(tasasCarteraCortoPlazo);

            Assert.NotNull(resultado);
            Assert.Equal(0.952, resultado.ProporcionSaldo1);
            Assert.Equal(0.968, resultado.ProporcionSaldo2);
            Assert.Equal(1, resultado.ProporcionSaldo3);
            Assert.Equal(0.617, resultado.ProporcionSaldo4);
        }

        [Fact]
        public async Task CalcularPropocion_RecibeTasaMinimasYMaximasParaLargoPlazo_RetornaPropociones()
        {
            var tasasCarteraLargoPlazo = new TasasSaldosCarteraDTO()
            {
                IdSolicitud = 123581,
                TasaMaximaSaldo1 = 22.5,
                TasaMaximaSaldo2 = 35.5,
                TasaMaximaSaldo3 = 18.6,
                TasaMaximaSaldo4 = 16.9,
                TasaMinimaSaldo1 = 10.5,
                TasaMinimaSaldo2 = 15.5,
                TasaMinimaSaldo3 = 7.3,
                TasaMinimaSaldo4 = 9.45,
                IdSubproducto = 123,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularProporcion(tasasCarteraLargoPlazo);

            Assert.NotNull(resultado);
            Assert.Equal(1.143, resultado.ProporcionSaldo1);
            Assert.Equal(1.290, resultado.ProporcionSaldo2);
            Assert.Equal(1.548, resultado.ProporcionSaldo3);
            Assert.Equal(0.788, resultado.ProporcionSaldo4);
        }

        [Fact]
        public async Task CalcularPropocion_RecibeTasasMinimasYMaximasNegativas_RetornaProporcionesEnCero()
        {
            var tasasCarteraCortoPlazo = new TasasSaldosCarteraDTO()
            {
                IdSolicitud = 123581,
                TasaMaximaSaldo1 = -20.5,
                TasaMaximaSaldo2 = -30.5,
                TasaMaximaSaldo3 = -14.6,
                TasaMaximaSaldo4 = -16.9,
                TasaMinimaSaldo1 = 10.5,
                TasaMinimaSaldo2 = 15.5,
                TasaMinimaSaldo3 = 7.3,
                TasaMinimaSaldo4 = 10.45,
                IdSubproducto = 123,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO,
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularProporcion(tasasCarteraCortoPlazo);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.ProporcionSaldo1);
            Assert.Equal(0, resultado.ProporcionSaldo2);
            Assert.Equal(0, resultado.ProporcionSaldo3);
            Assert.Equal(0, resultado.ProporcionSaldo4);
        }

        [Fact]
        public async Task CalcularPropocion_RecibeTasasMinimasYMaximasIguales_RetornaProporcionesEnCero()
        {
            var tasasCarteraLargoPlazo = new TasasSaldosCarteraDTO()
            {
                IdSolicitud = 123581,
                TasaMaximaSaldo1 = 22.5,
                TasaMaximaSaldo2 = 35.5,
                TasaMaximaSaldo3 = 18.6,
                TasaMaximaSaldo4 = 16.9,
                TasaMinimaSaldo1 = 22.5,
                TasaMinimaSaldo2 = 35.5,
                TasaMinimaSaldo3 = 18.6,
                TasaMinimaSaldo4 = 16.9,
                IdSubproducto = 123,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularProporcion(tasasCarteraLargoPlazo);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.ProporcionSaldo1);
            Assert.Equal(0, resultado.ProporcionSaldo2);
            Assert.Equal(0, resultado.ProporcionSaldo3);
            Assert.Equal(0, resultado.ProporcionSaldo4);
        }

        [Fact]
        public async Task CalcularPropocion_RecibeTasasMaximasYMinimasEnCero_RetornaProporcionesEnCero()
        {
            var tasasCarteraLargoPlazo = new TasasSaldosCarteraDTO()
            {
                IdSolicitud = 123581,
                TasaMaximaSaldo1 = 0,
                TasaMaximaSaldo2 = 0,
                TasaMaximaSaldo3 = 0,
                TasaMaximaSaldo4 = 0,
                TasaMinimaSaldo1 = 0,
                TasaMinimaSaldo2 = 0,
                TasaMinimaSaldo3 = 0,
                TasaMinimaSaldo4 = 0,
                IdSubproducto = 123,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION,
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularProporcion(tasasCarteraLargoPlazo);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.ProporcionSaldo1);
            Assert.Equal(0, resultado.ProporcionSaldo2);
            Assert.Equal(0, resultado.ProporcionSaldo3);
            Assert.Equal(0, resultado.ProporcionSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMaximas_RecibeSolicitudSinMora_RetornaSaldo2Y3ConIPC2()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 0,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 2300000,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMaximas(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.TasaContratacion, resultado.TasaMaximaSaldo1);
            Assert.Equal(solicitud.IPC2, resultado.TasaMaximaSaldo2);
            Assert.Equal(solicitud.IPC2, resultado.TasaMaximaSaldo3);
            Assert.Equal(0, resultado.TasaMaximaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMaximas_RecibeSolicitudEnMora_RetornaSaldo2Y3ConIPC4()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 20,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 2300000,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMaximas(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.TasaContratacion, resultado.TasaMaximaSaldo1);
            Assert.Equal(solicitud.IPC4, resultado.TasaMaximaSaldo2);
            Assert.Equal(solicitud.IPC4, resultado.TasaMaximaSaldo3);
            Assert.Equal(0, resultado.TasaMaximaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMaximas_RecibeSaldosLargoPlazoNoAdheridoAlBeneficioUSolidaria_RetornaTasaMaximaSaldo4IgualTasaContratacion()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 20,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 4250000,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMaximas(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.TasaMaximaSaldo1);
            Assert.Equal(0, resultado.TasaMaximaSaldo2);
            Assert.Equal(0, resultado.TasaMaximaSaldo3);
            Assert.Equal(solicitud.TasaContratacion, resultado.TasaMaximaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMaximas_RecibeSaldosCortoPlazo_RetornaSaldo1Y4EnCero()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 20,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 250000,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMaximas(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.TasaMaximaSaldo1);
            Assert.Equal(solicitud.IPC4, resultado.TasaMaximaSaldo2);
            Assert.Equal(solicitud.IPC4, resultado.TasaMaximaSaldo3);
            Assert.Equal(0, resultado.TasaMaximaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMinimas_RecibeSaldosAdheridoAlBeneficioUSolidaria_RetornaSaldo1Y2ConIPC()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 0,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 2300000,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMinimasCortoPlazo(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.IPC, resultado.TasaMinimaSaldo1);
            Assert.Equal(solicitud.IPC, resultado.TasaMinimaSaldo2);
            Assert.Equal(solicitud.IPC, resultado.TasaMinimaSaldo3);
            Assert.Equal(0, resultado.TasaMinimaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMinimas_RecibeSolicitudEnMora_RetornaSaldo1Y2ConIPC4()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 20,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMinimasLargoPlazo(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.IPC4, resultado.TasaMinimaSaldo1);
            Assert.Equal(solicitud.IPC4, resultado.TasaMinimaSaldo2);
            Assert.Equal(0, resultado.TasaMinimaSaldo3);
            Assert.Equal(0, resultado.TasaMinimaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMinimas_RecibeSolicitudSinMora_RetornaSaldo1Y2ConIPC2()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 0,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                CodigoSubproducto = TiposSubproducto.LARGO_PLAZO_AMORTIZACION
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMinimasCortoPlazo(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.IPC2, resultado.TasaMinimaSaldo1);
            Assert.Equal(solicitud.IPC2, resultado.TasaMinimaSaldo2);
            Assert.Equal(0, resultado.TasaMinimaSaldo3);
            Assert.Equal(0, resultado.TasaMinimaSaldo4);
        }

        [Fact]
        public async Task CalcularTasasMinimas_RecibeSaldosNoAdheridoAlBeneficioUSolidaria_RetornaSaldo4IgualTasaContratacion()
        {
            var solicitud = new SolicitudDTO()
            {
                Id = 12,
                IdSolicitante = 12456,
                IdTipoLinea = 0,
                IdTipoSublinea = 4,
                TipoLinea = "ACCESS",
                TipoSublinea = "ACUERDO001",
                TasaContratacion = 0.42,
                IPC = 0.62,
                IPC2 = 2.68,
                IPC4 = 5.8,
                DiasMora = 0,
            };

            var saldos = new SaldosCarteraDTO()
            {
                Id = 86,
                Saldo1 = 5600000,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 1000000,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            };

            var tasaCarteraRepository = new Mock<ITasaCarteraRepository>();
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            var calcularTasasCarteraService = new CalcularTasasCarteraService(tasaCarteraRepository.Object,
                                                                              saldosCarteraRepositoryMock.Object,
                                                                              solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await calcularTasasCarteraService.CalcularTasasMinimasLargoPlazo(solicitud, saldos);

            Assert.NotNull(resultado);
            Assert.Equal(solicitud.IPC2, resultado.TasaMinimaSaldo1);
            Assert.Equal(solicitud.IPC2, resultado.TasaMinimaSaldo2);
            Assert.Equal(0, resultado.TasaMinimaSaldo3);
            Assert.Equal(solicitud.TasaContratacion, resultado.TasaMinimaSaldo4);
        }
    }
}
