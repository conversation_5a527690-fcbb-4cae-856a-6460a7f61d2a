using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Moq;
using Sarlaft.Application.Behaviours;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Application.Features.Parametricas.Commands.DANE.CargarDANE;
using Sarlaft.Domain.Features.Files.Entities;
using Sarlaft.Domain.Features.Files.Enums;
using Sarlaft.Domain.Features.Files.Messages;
using Sarlaft.Domain.Features.Shared.Messages;
using ValidationException = Sarlaft.Application.Exceptions.ValidationException;

namespace Sarlaft.Test.Application.Behaviours
{
    public class ValidatorBehaviourTest
    {

        [Fact]
        public async Task CrearSolicitudFallida()
        {
            // Arrange
            var mockValidator = new Mock<IValidator<CargarDANECommand>>();
            mockValidator.Setup(validator => validator.ValidateAsync(It.IsAny<ValidationContext<CargarDANECommand>>(), CancellationToken.None))
                .ReturnsAsync(new ValidationResult(new List<ValidationFailure> { new ValidationFailure("file", string.Format(FileValidationMessages.MaximoPesoArchivo, 5, SizesScale.MegaByte.ToString())) }));

            var validators = new List<IValidator<CargarDANECommand>> { mockValidator.Object };

            var validationBehavior = new ValidationBehaviour<CargarDANECommand, Unit>(validators);

            // Simula el siguiente comportamiento del pipeline

            byte[] arregloLleno = Enumerable.Repeat((byte)0x55, 20).ToArray();
            var file = new CustomFile(arregloLleno, "test.pdf", ApplicationTypes.PDF);
            var mockRequest = new CargarDANECommand(file);
            var mockNext = new Mock<RequestHandlerDelegate<Unit>>();
            mockNext.Setup(next => next()).ReturnsAsync(new Unit());

            // Act
            Func<Task<Unit>> act = async () => await validationBehavior.Handle(mockRequest, mockNext.Object, CancellationToken.None);

            // Assert
            var exception = await Assert.ThrowsAsync<ValidationException>(act);
            Assert.True(exception.Errors.Any());
        }

        [Fact]
        public async Task CrearSolicitudCorrecta()
        {
            // Arrange
            var mockValidator = new Mock<IValidator<CargarDANECommand>>();
            mockValidator.Setup(validator => validator.ValidateAsync(It.IsAny<ValidationContext<CargarDANECommand>>(), CancellationToken.None))
                .ReturnsAsync(new ValidationResult(new List<ValidationFailure>()));

            var validators = new List<IValidator<CargarDANECommand>> { mockValidator.Object };

            var validationBehavior = new ValidationBehaviour<CargarDANECommand, Unit>(validators);

            // Simula el siguiente comportamiento del pipeline
            byte[] arregloLleno = Enumerable.Repeat((byte)0x55, 20).ToArray();
            var file = new CustomFile(arregloLleno, "test.pdf", ApplicationTypes.PDF);
            var mockRequest = new CargarDANECommand(file);
            var mockNext = new Mock<RequestHandlerDelegate<Unit>>();
            mockNext.Setup(next => next()).ReturnsAsync(new Unit());

            // Act
            var result = await validationBehavior.Handle(mockRequest, mockNext.Object, CancellationToken.None);
            // Assert
            Assert.IsType<Unit>(result);
        }
    }
}