﻿using Microsoft.OpenApi.Any;
using Moq;
using Sarlaft.Application.Contracts.Auth;
using Sarlaft.Application.Contracts.Auth.Models;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Auth.Commands.IniciarSesion;
using Sarlaft.Domain.Exceptions;
using Sarlaft.Domain.Features.Usuarios.Entities;
using Sarlaft.Test.Application.Config;
using Sarlaft.Test.Domain.Features;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sarlaft.Test.Application.Features.Auth.Commands
{
    public class IniciarSesionTest
    {
        [Fact]
        public void IniciarSesion_Validaciones()
        {
            //Arrange
            var validator = new IniciarSesionCommandValidator();
            var command =  new IniciarSesionCommand("rafa","1234","190.168.0.1");

            //Act
            var validatorResult = validator.Validate(command);

            //Assert
            Assert.False(validatorResult.IsValid);
            Assert.True(validatorResult.Errors.Count == 1);
        }

        [Fact]
        public async Task IniciarSesion_EmailNoPermitido()
        {
            //Arrange
            var authServiceMock = new Mock<IAuthService>();
            var usuarioPermitidoRepositoryMock = new Mock<IUsuarioPermitidoRepository>();
            var mapper = MapperConfig.Build();
            var handler = new IniciarSesionCommandHandler(authServiceMock.Object, usuarioPermitidoRepositoryMock.Object, mapper);
            var command =  new IniciarSesionCommand("<EMAIL>","1234","190.168.0.1");

            authServiceMock.Setup(service=> service.IniciarSesion(It.IsAny<InicioSesionRequest>()))
                .ReturnsAsync(new InicioSesionResponse
                {
                    Email = command.Email,
                    Nombres = "Elkin Andres",
                    Apellidos = "Fajardo Rincon",
                    Token = Guid.NewGuid().ToString()
                });

            usuarioPermitidoRepositoryMock.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<UsuarioPermitido>
                {
                    new()
                    {
                        Email = "<EMAIL>",
                        Id = EntityExtensionTest.GenerateId()
                    }
                });

            //Act and Assert
            await Assert.ThrowsAsync<NotAuthorizeException>(async () => await handler.Handle(command, CancellationToken.None));      
        }
        [Fact]
        public async Task IniciarSesion_Correcto()
        {
            //Arrange
            var authServiceMock = new Mock<IAuthService>();
            var usuarioPermitidoRepositoryMock = new Mock<IUsuarioPermitidoRepository>();
            var mapper = MapperConfig.Build();
            var handler = new IniciarSesionCommandHandler(authServiceMock.Object, usuarioPermitidoRepositoryMock.Object, mapper);
            var command =  new IniciarSesionCommand("<EMAIL>","1234","190.168.0.1");

            authServiceMock.Setup(service=> service.IniciarSesion(It.IsAny<InicioSesionRequest>()))
                .ReturnsAsync(new InicioSesionResponse
                {
                    Email = command.Email,
                    Nombres = "Elkin Andres",
                    Apellidos = "Fajardo Rincon",
                    Token = Guid.NewGuid().ToString()
                });

            usuarioPermitidoRepositoryMock.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(new List<UsuarioPermitido>
                {
                    new()
                    {
                        Email = command.Email,
                        Id = EntityExtensionTest.GenerateId()
                    }
                });

            //Act
            var result = await handler.Handle(command,CancellationToken.None);

            //Assert
            Assert.NotEmpty(result.Token);            
            Assert.Equal(command.Email, result.Email);
        }
    }
}
