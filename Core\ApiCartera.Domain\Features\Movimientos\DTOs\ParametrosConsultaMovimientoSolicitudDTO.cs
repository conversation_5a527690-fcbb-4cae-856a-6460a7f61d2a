﻿namespace ApiCartera.Domain.Features.Movimientos.DTOs
{
    public class ParametrosConsultaMovimientoSolicitudDTO
    {
        public int? IdSolicitud { get; set; }
        public int? IdSignature { get; set; }
        public int? Documento { get; set; }
        public DateTime FechaInicio { get; set; }
        public string FechaInicioFormatted
        {
            get { return FechaInicio.ToString("yyyy-MM-ddTHH:mm:ssZ"); }
        }
        public DateTime FechaFin { get; set; }
        public string FechaFinalFormatted
        {
            get { return FechaFin.ToString("yyyy-MM-ddTHH:mm:ssZ"); }
        }
    }
}
