﻿using ApiCartera.Application.Contracts.Persistence.Http;
using ApiCartera.Application.Contracts.Persistence.Oracle;
using ApiCartera.Application.Contracts.Persistence.Registros;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Registros.DTOs;
using ApiCartera.Domain.Features.Registros.Models;
using ApiCartera.Domain.Features.Registros.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using System.Globalization;

namespace ApiCartera.Application.Services.Registros
{
    public class GestorRegistrosSaldosSolicitudCartera(
        ISFTPRepository sftpRepository,
        IOracleRepository oracleRepository,
        IHttpRequestRepository httpRequestRepository) : IGestorRegistrosArchivosUT
    {
        private readonly ISFTPRepository _sftpRepository = sftpRepository;
        private readonly IOracleRepository _oracleRepository = oracleRepository;
        private readonly IHttpRequestRepository _httpRequestRepository = httpRequestRepository;
        private const int MES_JUNIO = 6;
        private const int PRIMER_SEMESTRE = 1;
        private const int SEGUNDO_SEMESTRE = 2;

        public async Task ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, string oracleConnectionString)
        {
            var solicitudesParaDivision = new List<long>();
            var remoteFilePath = ObtenerNombreArchivoSaldos(sftpSettings.RemotePath);
            var registros = _sftpRepository.LeerArchivo<RegistroSaldosSolicitudCartera>(sftpSettings.Host, sftpSettings.Username, sftpSettings.Password, remoteFilePath);
            var registrosMapeados = MapearRegistrosSaldosSolicitudCartera(registros);
            var resultadoInsercion = await _oracleRepository.InsertarSaldosMasivamente(oracleConnectionString, registrosMapeados);
            int count = 1;
            while (count > 0)
            {
                solicitudesParaDivision = await _oracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(oracleConnectionString);
                await EjecutarDivisionSaldos(solicitudesParaDivision);
                count = solicitudesParaDivision.Count();
            }                   
        }

        public async Task<ResultadoEjecucion<long>> ProcesarRegistrosMovimientos(SftpSettingsDTO sftpSettings, string oracleConnectionString)
        {
            var remoteFilePath = ObtenerNombreArchivoMovimientos(sftpSettings.RemotePath);
            var registros = _sftpRepository.LeerArchivoMovimientoDiario<RegistroMovimientoDiario>(sftpSettings.Host, sftpSettings.Username, sftpSettings.Password, remoteFilePath);
            var registrosMapeados = MapearRegistrosMovimientos(registros, remoteFilePath);
            var resultadoInsercion = await _oracleRepository.InsertarMovimientosMasivamente(oracleConnectionString, registrosMapeados);
            var resultadoAfectaciones = EjecutarCalculoAfectaciones(resultadoInsercion);
            return resultadoAfectaciones;
        }
        private static string ObtenerNombreArchivoSaldos(string remotePath)
        {
            var prefix = "USOLIDA";
            var suffix = "TXT";
            //var fileCreateDate = DateTime.Now.AddDays(-1).ToString("yyyyMMdd");
            var fileCreateDate = "20241018";
            return $"{remotePath}{prefix}{fileCreateDate}{suffix}";
        }

        private static string ObtenerNombreArchivoMovimientos(string remotePath)
        {
            var prefix = "PRE0756";
            var suffix = "TXT";
            var fileCreateDate = DateTime.Now.AddDays(-1).ToString("yyyyMMdd");
            return $"{remotePath}{prefix}{fileCreateDate}{suffix}";
        }

        private static List<SaldosSolicitudCartera> MapearRegistrosSaldosSolicitudCartera(List<RegistroSaldosSolicitudCartera> registros)
        {
            var registrosMapeados = new List<SaldosSolicitudCartera>();

            foreach (var registro in registros) 
            {
                var registroMapeado = new SaldosSolicitudCartera
                {
                    IdSolicitud = long.Parse(registro.IdIcetex),
                    IdSignature = long.Parse(registro.IdSignature),
                    SaldoCapitalVigente = registro.SaldoCapitalVigente,
                    SaldoTotalCapital = registro.SaldoCapitalTotal,
                    SaldoCapitalVencido = registro.SaldoCapitalVencido,
                    IdSubproducto = ObtenerIdSubProducto(registro.Producto),
                    IdTipoCartera = ObtenerIdTipoCartera(registro.TipoCartera),
                    CuotasPendientesPagar = registro.CuotasPendientesPorPagar,
                    FechaFinalizacionPlanPagos = ObtenerFecha(registro.FechaFinalizacionPlanPagos)
                };

                registrosMapeados.Add(registroMapeado);
            }

            return registrosMapeados;
        }

        private static List<MovimientosDiariosDTO> MapearRegistrosMovimientos(List<RegistroMovimientoDiario> registros, string RutaArchivo)
        {
            var registrosMapeados = new List<MovimientosDiariosDTO>();
            var fechaEjecucion = DateTime.Now;

            var RutaArchivoModificada = RutaArchivo.Replace("/", "");

            foreach (var registro in registros)
            {
                var registroMapeado = new MovimientosDiariosDTO
                {
                    IdIcetex = long.Parse(registro.IdIcetex),
                    IdSignature = long.Parse(registro.IdSignature),
                    SdoTotalCapitalVigente  = registro.SaldoCapitalVigente,
                    FechaEfectivaMovimiento = ObtenerFecha(registro.FechaEfectivaMovimiento).Value,
                    FechaPosteo = ObtenerFecha(registro.FechaPosteo).Value,
                    MontoMovimientoDeCapital = registro.MontoMovimientoDeCapital,
                    CodigoTipoMovtoDeCapital = int.Parse(registro.CodigoTipoMovimientoDeCapital),
                    NombreTipoMovtoDeCapital = registro.NombreTipoMovimientoDeCapital,
                    CodSubproductoXObligacion = registro.CodigoSubproductoPorObligacion,
                    SaldoTotalDeCapital = registro.SaldoTotalCapital,
                    SaldoCapitalVencido = registro.SaldoCapitalVencido,
                    DescripcionMovtoMemo = registro.DescripcionMovimiento,
                    CodNovedad = registro.CodigoNovedad,
                    TipoCartera = registro.TipoCartera,
                    CuotasPendientesPorPagar = registro.CuotasPendientesPorPagar,
                    DescripcionNovacion = registro.DescripcionNovacion,
                    ReferenciaCus = registro.ReferenciaCus,
                    Year = ObtenerFecha(registro.FechaEfectivaMovimiento).Value.Year,
                    Semestre = ObtenerFecha(registro.FechaEfectivaMovimiento).Value.Month > MES_JUNIO ? SEGUNDO_SEMESTRE : PRIMER_SEMESTRE,
                    NombreArchivo = RutaArchivoModificada,
                    FechaEjecucion = fechaEjecucion,
                    ControlMovimiento = 0
                };

                registrosMapeados.Add(registroMapeado);
            }

            return registrosMapeados;
        }

        private static int ObtenerIdSubProducto(string codigoSubProducto)
        {
            return codigoSubProducto switch
            {
                TiposSubproducto.CORTO_PLAZO => 1,
                TiposSubproducto.LARGO_PLAZO => 2,
                TiposSubproducto.LARGO_PLAZO_AMORTIZACION => 6,
                TiposSubproducto.ALIANZAS_LP_RP_AMORTIZACION => 9,
                TiposSubproducto.ALIANZAS_LP_RP => 10,
                TiposSubproducto.ALIANZAS_REEMBOLSABLE_LP_RA => 12,
                TiposSubproducto.ORI_CONDONABLE => 34,
                TiposSubproducto.ORI_CONDONABLE_R_TERCEROS => 36,
                TiposSubproducto.ORI_REEMBOLSABLE_RT => 37,
                _ => throw new NotImplementedException(),
            };
        }

        private static int ObtenerIdTipoCartera(string tipoCartera)
        {
            return tipoCartera switch
            {
                TiposCartera.ESTUDIOS => 1,
                TiposCartera.AMORTIZACION => 2,
                "" => 3,
                _ => throw new NotImplementedException(),
            };
        }

        private static int ObtenerIdTipoNovedad(string tipoNovedad)
        {
            return int.Parse(tipoNovedad);
        }

        private static DateTime? ObtenerFecha(string fechaFinalizacionPlanPagos)
        {
            if (fechaFinalizacionPlanPagos == "0")
            {
                return null;
            }

            if (DateTime.TryParseExact(fechaFinalizacionPlanPagos, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime fechaFinalizacionParseada))
            {
                return fechaFinalizacionParseada;
            }

            return null;
        }

        private async Task EjecutarDivisionSaldos(List<long> solicitudesParaDivision)
        {
            Console.WriteLine($"Se inicia Division de saldos");
            const int batchSize = 200;
            int totalRecords = solicitudesParaDivision.Count;
            int processedRecords = 0;
            int count = 1;
            int maxDegreeOfParallelism = 7;

            var batches = new List<List<long>>();

            while (processedRecords < totalRecords)
            {
                var batchRecords = solicitudesParaDivision.Skip(processedRecords).Take(batchSize).ToList();
                batches.Add(batchRecords);
                processedRecords += batchRecords.Count;
            }

            using (var semaphore = new SemaphoreSlim(maxDegreeOfParallelism))
            { 
                var tasks = new List<Task>();

                foreach (var batch in batches)
                {
                    await semaphore.WaitAsync();

                    var currentBatch = batch;
                    var currentCount = count++;

                    var task = Task.Run(async () =>
                    {
                        try
                        {
                            Console.WriteLine($"Iniciando lote #{currentCount} con {currentBatch.Count} registros.");
                            await DivisionSaldos(currentBatch);
                            Console.WriteLine($"Lote #{currentCount} procesado.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error al procesar el lote #{currentCount}: {ex.Message}");
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    });
                    tasks.Add(task);
                }                
                await Task.WhenAll(tasks);
            }
            Console.WriteLine("Todos los lotes han sido procesados.");
        }

        private async Task DivisionSaldos(List<long> idSolicitudes)
        {
            var endpoint = $"DivisionSaldos/ObtenerDivisionSaldosCarteraIniciales";

            var bodyDivision = new
            {
                idSolicitudes
            };

            var respuesta = await _httpRequestRepository.PostAsync<string>(endpoint, bodyDivision);
        }

        private ResultadoEjecucion<long> EjecutarCalculoAfectaciones(ResultadoEjecucion<MovimientosDiariosDTO> resultado)
        {
            var resultadoCalculoAfectaciones = new ResultadoEjecucion<long>()
            {
                Total= resultado.Exitosos.Count()
            };

            foreach (var registro in resultado.Exitosos.Distinct())
            {
                //var endpoint = "";

                //if (registro.CodigoTransaccion == TiposTransaccion.DESEMBOLSO_INICIAL)
                //{
                //    endpoint = $"AfectacionesSaldoACapitalPorAdjudicacion/CalcularSaldosCarteraPorAdjudicacion";
                //}
                //else if (registro.CodigoTransaccion == TiposTransaccion.DESEMBOLSO_ADICIONAL)
                //{
                //    endpoint = $"AfectacionesSaldoACapitalPorDesembolso/CalcularSaldosCarteraPorDesembolso";
                //}
                //else
                //{
                //    endpoint = $"Afectaciones/CalcularSaldosCartera";
                //}

                //var bodyAfectaciones = MapearBodyAfectaciones(registro);
                //var respuesta = _httpRequestRepository.PostAsync<SaldosCarteraDTO>(endpoint, bodyAfectaciones);
                //if (respuesta != null) 
                //{ 
                //    resultadoCalculoAfectaciones.Exitosos.Add(registro.IdSolicitud);
                //}
                //else
                //{
                //    resultadoCalculoAfectaciones.Fallidos.Add(registro.IdSolicitud);
                //}

                // Se ajusta de esta manera para el calculo de afectaciones porque en este momento no se realiza el proceso completo
                resultadoCalculoAfectaciones.Exitosos.Add(registro.IdIcetex);
            }

            resultadoCalculoAfectaciones.Fallidos.AddRange(resultado.Fallidos.Select(r => (long)r.IdIcetex).ToList());

            return resultadoCalculoAfectaciones;
        }

        private static dynamic MapearBodyAfectaciones(MovimientoDTO movimiento)
        {
            var bodyAfectacion = new
            {
                movimiento = new
                {
                    idMovimiento = 0,
                    fechaMovimiento = movimiento.FechaMovimiento,
                    valorGiro = movimiento.ValorGiro,
                    descripcion = movimiento.Descripcion,
                    idTipoNovedad = movimiento.IdTipoNovedad,
                    codigoNovedad = movimiento.IdTipoNovedad.ToString(),
                    year = movimiento.Year,
                    semestre = movimiento.Semestre,
                    idSolicitud = movimiento.IdSolicitud,
                    idSubproducto = movimiento.IdSubproducto,
                    codigoSubproducto = movimiento.CodigoSubproducto,
                    saldoCapitalVigente = movimiento.SaldoCapitalVigente,
                    saldoTotalCapital = movimiento.SaldoTotalCapital,
                    saldoCapitalVencido = movimiento.SaldoCapitalVencido,
                    noRelacion = 0,
                    codigoTransaccion = movimiento.CodigoTransaccion,
                    diasSinMovimiento = 0
                }
            };

            return bodyAfectacion;
        }
    }
}