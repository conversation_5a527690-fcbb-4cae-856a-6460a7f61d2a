﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalRecaudoServiceTests
    {
        private readonly SaldosCarteraDTO _saldoCartera = new()
        {
            Saldo1 = 1500,
            Saldo2 = 2000,
            Saldo3 = 4500,
            Saldo4 = 3000,
            IdSolicitud = 1,
            IdSubproducto = 2,
            CodigoSubproducto = "1",
            IdSaldosSolicitudCartera = 1
        };
        private readonly AfectacionesSaldoACapitalRecaudoService _afectacionesSaldoACapitalRecaudoService;
        private readonly Mock<ISaldosCarteraRepository> _saldosCarteraRepositoryMock;
        private readonly Mock<ISolicitudCarteraActivaRepository> _solicitudCarteraActivaRepositoryMock;

        public AfectacionesSaldoACapitalRecaudoServiceTests()
        {
            _saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            _saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(_saldoCartera));

            _solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            _solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));

            _afectacionesSaldoACapitalRecaudoService = new(_saldosCarteraRepositoryMock.Object, _solicitudCarteraActivaRepositoryMock.Object);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCapitalRecaudoAsobancaria), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_Recibe_RecaudoAsobancaria_RetornaSaldosDisminuidos(MovimientoDTO movimiento)
        {
            var resultado = await _afectacionesSaldoACapitalRecaudoService.CalcularSaldosCartera(movimiento);
            Assert.NotNull(resultado);
            Assert.NotEqual(resultado.Saldo1, movimiento.ValorGiro);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCapitalRecaudoManual), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_Recibe_RecaudoManual_RetornaSaldosDisminuidos(MovimientoDTO movimiento)
        {
            var resultado = await _afectacionesSaldoACapitalRecaudoService.CalcularSaldosCartera(movimiento);
            Assert.NotNull(resultado);
            Assert.NotEqual(resultado.Saldo1, movimiento.ValorGiro);
        }
    }
}
