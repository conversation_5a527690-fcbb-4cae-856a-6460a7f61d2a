﻿using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Models;
using AutoMapper;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Mappings;

public class ContribucionesAportesIESMapping : Profile
{
    public ContribucionesAportesIESMapping()
    {
        CreateMap<MvReporteCiesAies, ContribucionesAportesIESVm>()
            .ForMember(dest => dest.FechaFinEjecucion,
            opt => opt.MapFrom(src => src.FechaFinEjecucion != null ? src.FechaFinEjecucion.ToString() : null))
            .ForMember(dest => dest.FechaGeneracion,
            opt => opt.MapFrom(src => src.FechaGeneracion != null ? src.FechaGeneracion.ToString() : null))
            .ForMember(dest => dest.FechaGiro,
            opt => opt.MapFrom(src => src.FechaGiro != null ? src.FechaGiro.ToString() : null));
    }
}