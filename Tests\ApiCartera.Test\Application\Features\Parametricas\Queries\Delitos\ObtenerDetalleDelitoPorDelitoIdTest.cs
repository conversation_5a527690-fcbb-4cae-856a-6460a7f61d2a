using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Queries.Delitos.ObtenerDetalleDelitoPorDelitoId;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Infrastructure.Persitence.Memory;
using Sarlaft.Test.Application.Config;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Delitos
{
    public class ObtenerDetalleDelitoPorDelitoIdTest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            IDelitoDetalleRepository mockRepository = new DelitoDetalleRepositoryMemory();
            var mockMapper = MapperConfig.Build();
            var delitoId = EntityExtensionTest.GenerateId();
            var detallesDelito = new List<DelitoDetalle>
            {
                new()
                {
                    DelitoId = delitoId,
                    Cantidad = 1,
                    CiudadId =  EntityExtensionTest.GenerateId(),
                    FechaHecho = DateTime.Now
                },
                new()
                {
                    DelitoId = delitoId,
                    Cantidad = 1,
                    CiudadId =  EntityExtensionTest.GenerateId(),
                    FechaHecho = DateTime.Now
                },
                new()
                {
                    DelitoId =  EntityExtensionTest.GenerateId(),
                    Cantidad = 1,
                    CiudadId =  EntityExtensionTest.GenerateId(),
                    FechaHecho = DateTime.Now
                },
            };
            detallesDelito.ForEach(async detalleDelito => await mockRepository.CrearAsync(detalleDelito));

            var handler = new ObtenerDetalleDelitoPorDelitoIdQueryHandler(mockRepository, mockMapper);
            var query = new ObtenerDetalleDelitoPorDelitoIdQuery(delitoId);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(result.Count(), detallesDelito.Where(detalleDelito=> detalleDelito.DelitoId == delitoId).Count());
        }
    }
}