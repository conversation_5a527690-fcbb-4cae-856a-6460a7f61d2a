﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.Afectaciones.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Afectaciones
{
    [Route("api/[controller]")]
    [ApiController]
    public class AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("CalcularSaldosCarteraPorFinalizacionFechaPlanDePagos")]
        [ProducesResponseType<ResponseWrapper<List<SaldosPorAfectacionVm>>>(StatusCodes.Status200OK)]
        public async Task<List<SaldosPorAfectacionVm>> CalcularSaldosCarteraPorDesembolso(
        [FromBody] AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosCommand request) => await _mediator.Send(request);
    }
}
