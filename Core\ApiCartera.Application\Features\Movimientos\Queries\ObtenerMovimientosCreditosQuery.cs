﻿using ApiCartera.Application.Features.Movimientos.ViewModels;
using MediatR;

namespace ApiCartera.Application.Features.Movimientos.Queries
{
    public record ObtenerMovimientosCreditosQuery : IRequest<List<ConsultaMovimientoVm>>
    {
        public int? IdSolicitud { get; set; } = 0;
        public int? IdSignature { get; set; } = 0;
        public int? Documento { get; set; } = 0;
        public DateTime FechaInicio { get; set; }
        public DateTime FechaFin { get; set; }
    }
}
