﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalReversionRecaudoServiceTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoPlazoReversionRecaudo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoPlazo_RetornaSaldo1Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 800000,
                Saldo2 = 200000,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 150
            };
            var saldosCarteraCortoPlazoRecaudo = new SaldosCarteraDTO()
            {
                Saldo1 = 1800000,
                Saldo2 = 600000,
                Saldo3 = 600000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149,
                IdSaldosCarteraAnt = 148
            };
            var saldosCarteraCortoPlazoAntesRecaudo = new SaldosCarteraDTO()
            {
                Saldo1 = 2200000,
                Saldo2 = 600000,
                Saldo3 = 600000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 148
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1,
                    It.IsAny<DateTime>()))
                .Returns(Task.FromResult(saldosCarteraCortoPlazoRecaudo));       
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorId(
                    148))
                .Returns(Task.FromResult(saldosCarteraCortoPlazoAntesRecaudo));
            var afectacionesSaldoACapitalReversionRecaudoService = new AfectacionesSaldoACapitalReversionRecaudoService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionRecaudoService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(1200000, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
        
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoLargoPlazoReversionRecaudo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoLargoPlazo_RetornaSaldo1Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 8000000,
                Saldo2 = 2000000,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 150
            };
            var saldosCarteraLargoPlazoRecaudo = new SaldosCarteraDTO()
            {
                Saldo1 = 18000000,
                Saldo2 = 6000000,
                Saldo3 = 6000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149,
                IdSaldosCarteraAnt = 148
            };
            var saldosCarteraLargoPlazoAntesRecaudo = new SaldosCarteraDTO()
            {
                Saldo1 = 22000000,
                Saldo2 = 6000000,
                Saldo3 = 6000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 148
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2,
                    It.IsAny<DateTime>()))
                .Returns(Task.FromResult(saldosCarteraLargoPlazoRecaudo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorId(
                    148))
                .Returns(Task.FromResult(saldosCarteraLargoPlazoAntesRecaudo));
            var afectacionesSaldoACapitalReversionRecaudoService = new AfectacionesSaldoACapitalReversionRecaudoService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalReversionRecaudoService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(12000000, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
    }
}
