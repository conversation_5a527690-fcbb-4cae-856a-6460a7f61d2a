﻿using ApiCartera.Application.Contracts.Server;
using ApiCartera.Application.Contracts.Storage;
using ApiCartera.Domain.Exceptions;
using ApiCartera.Domain.Features.Files.Entities;
using ApiCartera.Domain.Features.Files.Extensions;
using ApiCartera.Domain.Features.Shared.Messages;

namespace ApiCartera.Infrastructure.Storage
{
    internal class StorageLocalService : IStorageService
    {
        private readonly IWebServerInfo _webServerInfo;
        public StorageLocalService(IWebServerInfo webServerInfo )
        {
            _webServerInfo = webServerInfo;
        }
        public string _basePath => _webServerInfo.GetRootPath() +"/Files";

        public string Save(CustomFile file, string route)
        {
            var fullRoute = Path.Combine(_basePath, route);
            if (!Directory.Exists(fullRoute))
            {
                Directory.CreateDirectory(fullRoute);
            }
            var relativePath = Path.Combine(route, file.FullName);
            var absolutePath = Path.Combine(_basePath, relativePath);
            File.WriteAllBytes(absolutePath, file.File);
            return relativePath;
        }

        public CustomFile Get(string route)
        {
            var absolutePath = Path.Combine(_basePath, route);
            if(File.Exists(absolutePath))
            {
                var fileName = Path.GetFileName(absolutePath);
                var file = File.ReadAllBytes(absolutePath);
                var extension = Path.GetExtension(absolutePath);
                var applicationType = ApplicationFileTypeExtensions.FindByExtension(extension);
                return new CustomFile(file, fileName, applicationType.ApplicationType);
            }
            throw new NotFoundException(string.Format(ExceptionMessages.ArchivoNoEncontrado,route));
        }

        public void Delete(string route)
        {
            var absolutePath = Path.Combine(_basePath, route);
            if(File.Exists(absolutePath))
            {
                File.Delete(absolutePath);
            }
        }

        public void Clear(string path)
        {
            var absolutePath = Path.Combine(_basePath, path);
            if (Directory.Exists(absolutePath))
            {
                string[] archivos = Directory.GetFiles(absolutePath);
                foreach (var archivo in archivos)
                {
                    File.Delete(archivo);
                }
            }
        }
    }
}
