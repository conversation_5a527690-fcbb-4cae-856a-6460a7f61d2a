﻿using ApiCartera.Application.Features.DivisionSaldos.Queries;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.DivisionSaldos.Handlers
{
    public class CalcularSaldosQueryHandler(

        ICalculadoraSaldosService calcularSaldosService,
        IMapper mapper
        ) : IRequestHandler<CalcularSaldosQuery, Unit>
    {
        public async Task<Unit> Handle(CalcularSaldosQuery request, CancellationToken cancellationToken)
        {
            foreach (var idSolicitud in request.IdSolicitudes)
            {
                await calcularSaldosService.ObtenerSaldos(idSolicitud);
            }

            return Unit.Value;
        }
    }
}
