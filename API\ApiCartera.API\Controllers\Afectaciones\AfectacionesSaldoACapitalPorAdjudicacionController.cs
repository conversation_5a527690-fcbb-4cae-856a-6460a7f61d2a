﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Afectaciones
{
    [Route("api/[controller]")]
    [ApiController]
    public class AfectacionesSaldoACapitalPorAdjudicacionController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("CalcularSaldosCarteraPorAdjudicacion")]
        [ProducesResponseType<ResponseWrapper<List<SaldoCarteraVm>>>(StatusCodes.Status200OK)]
        public async Task<List<SaldoCarteraVm>> CalcularSaldosCarteraPorDesembolso(
            [FromBody] AfectacionesSaldoACapitalPorAdjudicacionCommand request) => await _mediator.Send(request);
    }
}
