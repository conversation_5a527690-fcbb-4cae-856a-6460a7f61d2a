﻿using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Application.Features.Afectaciones.Commands
{
    public record AfectacionSaldoCapitalReintegroCommand(MovimientoAfectacionDTO dataSaldoReintegro) : IRequest<SaldoCarteraVm>;
}
