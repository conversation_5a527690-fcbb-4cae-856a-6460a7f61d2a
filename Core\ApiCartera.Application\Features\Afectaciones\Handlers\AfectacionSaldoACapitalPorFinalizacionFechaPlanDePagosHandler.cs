﻿using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.Afectaciones.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosHandler(
        ICalcularSaldosCarteraPorFinalizacionFechaPlanDePagoService _calcularSaldosCarteraPorFinalizacionFechaPlanDePagoService,
        IMapper _mapper) : IRequestHandler<AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosCommand, List<SaldosPorAfectacionVm>>
    {
        public async Task<List<SaldosPorAfectacionVm>> Handle(AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _calcularSaldosCarteraPorFinalizacionFechaPlanDePagoService.CalcularSaldosCarteraPorFinalizacionFechaPlanDePago();
            return _mapper.Map<List<SaldosPorAfectacionVm>>(resultado);
        }
    }
}
