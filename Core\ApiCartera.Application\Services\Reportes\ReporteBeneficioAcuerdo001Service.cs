﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Domain.Features.Reportes.Services;
using ApiCartera.Domain.Models;

namespace ApiCartera.Application.Services.Reportes;

public class ReporteBeneficioAcuerdo001Service(IRepository<MvReporteBeneficioAc001, string> repository, IReporteBeneficioAcuerdo001Repository reporteBeneficioAcuerdo001Repository) : IReporteBeneficioAcuerdo001Service
{
    private readonly IRepository<MvReporteBeneficioAc001, string> _repository = repository;

    public async Task<List<MvReporteBeneficioAc001>> ObtenerReporte(DateTime desde, DateTime hasta)
    {
        List<MvReporteBeneficioAc001>? response = await _repository.ExecuteViewAsync<MvReporteBeneficioAc001>("VM_REPORTE_BENEFICIO_AC001");
        List<MvReporteBeneficioAc001>? responseFiltrada = response.Where(registro => registro.FechaMovimiento >= DateOnly.FromDateTime(desde) && registro.FechaMovimiento <= DateOnly.FromDateTime(hasta)).ToList();
        return responseFiltrada;
    }
}
