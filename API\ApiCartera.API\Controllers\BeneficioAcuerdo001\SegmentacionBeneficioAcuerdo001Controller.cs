﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Interfaces;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Queries;
using ApiCartera.Application.Features.Movimientos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace ApiCartera.API.Controllers.BeneficioAcuerdo001
{
    [Route("api/[controller]")]
    [ApiController] 
    public class SegmentacionBeneficioAcuerdo001Controller(IMediator _mediator, ISegmentacionBeneficioAcuerdo001Factory _queryFactory) : ControllerBase
    {
        [HttpGet("ObtenerSegmentadosBeneficioAcuerdo001")]
        [ProducesResponseType<ResponseWrapper<List<int>>>(StatusCodes.Status200OK)]
        public async Task<List<int>> ObtenerSegmentadosBeneficioAcuerdo001()
        {
            var request = _queryFactory.Create();
            return await _mediator.Send(request);
        }            
    }
}
