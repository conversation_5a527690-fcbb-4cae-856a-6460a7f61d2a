﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ApiCartera.Domain.Features.Files.Messages {
    using System;
    
    
    /// <summary>
    ///   Clase de recurso fuertemente tipado, para buscar cadenas traducidas, etc.
    /// </summary>
    // StronglyTypedResourceBuilder generó automáticamente esta clase
    // a través de una herramienta como ResGen o Visual Studio.
    // Para agregar o quitar un miembro, edite el archivo .ResX y, a continuación, vuelva a ejecutar ResGen
    // con la opción /str o recompile su proyecto de VS.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class FileValidationMessages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FileValidationMessages() {
        }
        
        /// <summary>
        ///   Devuelve la instancia de ResourceManager almacenada en caché utilizada por esta clase.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ApiCartera.Domain.Features.Files.Messages.FileValidationMessages", typeof(FileValidationMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Reemplaza la propiedad CurrentUICulture del subproceso actual para todas las
        ///   búsquedas de recursos mediante esta clase de recurso fuertemente tipado.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a El/La &apos;{0}&apos; de la fila N°{1} esta vacío. .
        /// </summary>
        public static string CeldaVacia {
            get {
                return ResourceManager.GetString("CeldaVacia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a El formato del archivo debe ser {0}..
        /// </summary>
        public static string FormatoArchivo {
            get {
                return ResourceManager.GetString("FormatoArchivo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a El peso máximo del archivo es de {0} {1}..
        /// </summary>
        public static string MaximoPesoArchivo {
            get {
                return ResourceManager.GetString("MaximoPesoArchivo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró la columna &apos;{0}&apos; en el archivo..
        /// </summary>
        public static string NoSeEncontroColumnaExcel {
            get {
                return ResourceManager.GetString("NoSeEncontroColumnaExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Busca una cadena traducida similar a No se encontro formato con el {0}: &apos;{1}&apos;.
        /// </summary>
        public static string NoSeEncontroFormatFileType {
            get {
                return ResourceManager.GetString("NoSeEncontroFormatFileType", resourceCulture);
            }
        }
    }
}
