﻿using ApiCartera.Application.Features.BeneficioAcuerdo001.Interfaces;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Queries;
using ApiCartera.Application.Features.Movimientos.Queries;
using ApiCartera.Application.Features.Movimientos.ViewModels;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.Movimientos.DTOs;
using ApiCartera.Domain.Features.Movimientos.Services;
using AutoMapper;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Handlers
{
    public class SegmentacionBeneficioAcuerdo001Handler(ISegmentacionBeneficioAcuerdo001Service segmentarBeneficioAcuerdo001) : IRequestHandler<SegmentacionBeneficioAcuerdo001Query, List<int>>
    {
        public async Task<List<int>> Handle(SegmentacionBeneficioAcuerdo001Query request, CancellationToken cancellationToken)
        {
            var resultado = await segmentarBeneficioAcuerdo001.ObtenerSegmentacionBeneficioAcuerdo001();

            return resultado;
        }
    }
}
