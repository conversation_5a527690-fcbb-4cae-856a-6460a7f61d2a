﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.Afectaciones
{
    [Route("api/[controller]")]
    [ApiController]
    public class IdentificadorCreditosNuevosController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("IdentificadorNuevosCreditos")]
        [ProducesResponseType<ResponseWrapper<SaldoCarteraVm>>(StatusCodes.Status200OK)]
        public async Task<SaldoCarteraVm> IdentificadorNuevosCreditos(
            [FromBody] IdentificadorCreditosNuevosCommand request) => await _mediator.Send(request);
    }
}
