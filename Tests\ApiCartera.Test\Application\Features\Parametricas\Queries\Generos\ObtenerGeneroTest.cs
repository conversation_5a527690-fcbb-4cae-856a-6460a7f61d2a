using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Queries.Generos.ObtenerTodosGeneros;
using Sarlaft.Domain.Features.Parametricas.Entities;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Generos
{
    public class ObtenerGenerosTest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var services = DependencyContainer.Get();
            var mockRepository = new Mock<IGenericRepository<Genero>>();
            var data = new List<Genero>()
                {
                    new()
                    {
                        Id = 1,
                        Nombre = "M",
                        Valor = 1,
                    },
                    new()
                    {
                        Id = 2,
                        Nombre = "F",
                        Valor = 2,
                    },
                };
            mockRepository.Setup(repo => repo.ObtenerTodosAsync())
                .ReturnsAsync(data);
            services.AddScoped(_ => mockRepository.Object);

            using (var scope = services.BuildServiceProvider().CreateScope())
            {
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var command = new ObtenerTodosGenerosQuery();

                // Ejecuta el comando utilizando MediatR
                var resultado = await mediator.Send(command);

                mockRepository.Verify(repo => repo.ObtenerTodosAsync());
                Assert.Equal(data.Count, resultado.Count);
            }
        }
    }
}