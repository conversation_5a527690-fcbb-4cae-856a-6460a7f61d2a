﻿using ApiCartera.API.Wrappers;
using ApiCartera.Application.Features.BeneficioAcuerdo001.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ApiCartera.API.Controllers.BeneficioAcuerdo001;

[Route("api/[controller]")]
[ApiController]
public class CalculoBeneficioAcuerdo001Controller(IMediator _mediator) : ControllerBase
{
    [HttpPost("CalcularBeneficio")]
    [ProducesResponseType<ResponseWrapper<InteresesLiquidadosVM>>(StatusCodes.Status200OK)]
    public async Task<InteresesLiquidadosVM> CalcularBeneficio([FromBody] CalculoBeneficioAcuerdo001Command request) =>
        await _mediator.Send(request);
}
