﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Extensions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using FluentValidation.Results;
using System.Text.RegularExpressions;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorDesembolsoService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {
        private const int ANIO_APORTES_IES = 2024;
        private const int ANIO_CONTRIBUCION_IES = 2023;
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            ValidarMovimientoAfectacion(movimiento);
            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);

            ValidarSaldosCartera(saldosCartera, movimiento.IdIcetex);
            var nuevosSaldosCartera = (SaldosCarteraDTO)saldosCartera.Clone();

            var controlModalidadRubro = 0;

            var norelacion = ValidarYExtraerNumero(movimiento.DescripcionMovimientoMemo.ToString());
            if (norelacion != 0) {

                var desembolsoM = await solicitudCarteraActivaRepository.ObtenerDesembolsoPorNoRelacionGenerica(movimiento.IdIcetex, norelacion, TiposModalidadRubro.MATRICULA);

                var desembolsoS = await solicitudCarteraActivaRepository.ObtenerDesembolsoPorNoRelacionGenerica(movimiento.IdIcetex, norelacion, TiposModalidadRubro.SOSTENIMIENTO);
              
                if (!string.IsNullOrWhiteSpace(desembolsoM.ModalidadRubro)) { controlModalidadRubro = 1; }
                else if (!string.IsNullOrWhiteSpace(desembolsoS.ModalidadRubro)) { controlModalidadRubro = 2; }

                if (controlModalidadRubro != 0)
                {

                    if (controlModalidadRubro != 2)
                    {

                        ValidarDesembolso(desembolsoM);

                        if (!desembolsoM.Marca.Equals(TiposBeneficio.CUENTA_COBRO_NACION) || !desembolsoM.Marca.Equals(TiposBeneficio.ALERTA))
                        {

                            var validarValorAportesIES = desembolsoM.ValorAportesIES != null && desembolsoM.ValorAportesIES > 0;

                            if (desembolsoM.Marca.Equals(TiposBeneficio.APORTES_IES) && validarValorAportesIES)
                            {
                                nuevosSaldosCartera.Saldo3 = nuevosSaldosCartera.Saldo3 + movimiento.MontoMovimientoCapital;
                            }

                            if (desembolsoM.Marca != TiposBeneficio.APORTES_IES && desembolsoM.YearGiro > ANIO_CONTRIBUCION_IES)
                            {
                                nuevosSaldosCartera.Saldo4 = nuevosSaldosCartera.Saldo4 + movimiento.MontoMovimientoCapital;
                            }

                            if (desembolsoM.YearGiro < ANIO_APORTES_IES)
                            {
                                nuevosSaldosCartera.Saldo1 = nuevosSaldosCartera.Saldo1 + movimiento.MontoMovimientoCapital;
                            }
                            nuevosSaldosCartera.IdSaldosSolicitudCartera = null;
                            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
                            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
                            var saldos = this.MapearSaldosCarteraAfectaciones(nuevosSaldosCartera);
                            await solicitudCarteraActivaRepository.GuardarSaldos(saldos);

                        }

                    }
                    else
                    {

                        nuevosSaldosCartera.Saldo1 = nuevosSaldosCartera.Saldo1 + movimiento.MontoMovimientoCapital;
                        nuevosSaldosCartera.IdSaldosSolicitudCartera = null;
                        nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
                        nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;
                        var saldos = this.MapearSaldosCarteraAfectaciones(nuevosSaldosCartera);
                        await solicitudCarteraActivaRepository.GuardarSaldos(saldos);

                    }

                }
                else {
                    ValidarDesembolso(desembolsoM);
                }
                
            }
            
            return nuevosSaldosCartera;
        }

        private static void ValidarDesembolso(DesembolsoDTO desembolso)
        {
            if (desembolso == null)
            {
                throw new ValidationException(
                [
                    new()
                    {
                        PropertyName = nameof(desembolso.IdSolicitud),
                        ErrorMessage = $"{ValidationMessages.Desembolso} {desembolso.IdSolicitud}"
                    }
                ]);
            }
            var desembolsoValidador = new DesembolsoValidator();
            desembolsoValidador.ValidateAndThrowCustomValidationException(desembolso);
        }

        public static int ValidarYExtraerNumero(string input)
        {

            if (string.IsNullOrWhiteSpace(input))
                return 0;

            bool starts06 = input.StartsWith("06 ");
            bool starts006 = input.StartsWith("006 ");

            if (!starts06 && !starts006)
                return 0;

            try
            {
                string numberStr;
                if (starts06)
                {

                    var match = Regex.Match(input, @"06\s+Res\.?\s*(\d+)");
                    if (!match.Success)
                        return 0;

                    numberStr = match.Groups[1].Value;
                }
                else
                {

                    var match = Regex.Match(input, @"006\s+Res\.?\s*(\d+)");
                    if (!match.Success)
                        return 0;

                    numberStr = match.Groups[1].Value;
                }

                if (int.TryParse(numberStr, out int result))
                    return result;

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }
    }
}
