﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.BeneficioAcuerdo001;

[EntityName("MBT_TASAS_SALDOS_CARTERA")]
public class TasasSaldosCarteraEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public long IdSolicitud { get; set; }
    public double TasaMinimaSaldo1 { get; set; }
    public double TasaMinimaSaldo2 { get; set; }
    public double TasaMinimaSaldo3 { get; set; }
    public double TasaMinimaSaldo4 { get; set; }
    public double TasaMaximaSaldo1 { get; set; }
    public double TasaMaximaSaldo2 { get; set; }
    public double TasaMaximaSaldo3 { get; set; }
    public double TasaMaximaSaldo4 { get; set; }
    public double ProporcionSaldo1 { get; set; }
    public double ProporcionSaldo2 { get; set; }
    public double ProporcionSaldo3 { get; set; }
    public double ProporcionSaldo4 { get; set; }
    public int IdSubproducto { get; set; }
    public string CodigoSubproducto { get; set; }
    public int? IdMovimiento { get; set; }
    public int? IdSaldosSolicitudCartera { get; set; }
}
