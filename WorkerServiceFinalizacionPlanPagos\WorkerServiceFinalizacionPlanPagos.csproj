<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-WorkerServiceFinalizacionPlanPagos-43af3197-386d-477f-a5b1-48ceff511226</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ApiCartera.Application\ApiCartera.Application.csproj" />
    <ProjectReference Include="..\Core\ApiCartera.Domain\ApiCartera.Domain.csproj" />
    <ProjectReference Include="..\Infrastructure\ApiCartera.Infrastructure\ApiCartera.Infrastructure.csproj" />
  </ItemGroup>
</Project>
