﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalCondonacionAlianzaServiceTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoLargoPlazoCondonacionAlianza), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoLargoPlazo_RetornaSaldosEnCero(MovimientoDTO movimiento)
        {
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 18000000,
                Saldo2 = 6000000,
                Saldo3 = 6000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "20001",
                IdSaldosSolicitudCartera = 149
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            var afectacionesSaldoACapitalCondonacionAlianzaService = new AfectacionesSaldoACapitalCondonacionAlianzaService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalCondonacionAlianzaService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(18000000, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
    }
}
