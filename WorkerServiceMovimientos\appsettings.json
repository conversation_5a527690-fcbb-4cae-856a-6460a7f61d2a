{"ApiCarteraUrl": "http://***********:7000/api/", "ConnectionStrings": {"OracleConnection": "DATA SOURCE=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SICCI)));USER ID=ICETEX_NUEVO; Password=************;"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "/Logs/WorkerServiceMovimientos-.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "Worker Service Movimientos", "Environment": "Development"}}, "SftpSettings": {"Host": "*************", "Username": "usercore0", "Password": "C0l0mb142025*", "RemotePath": "/"}}