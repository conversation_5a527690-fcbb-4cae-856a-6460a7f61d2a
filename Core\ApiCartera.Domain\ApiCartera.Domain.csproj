﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Features\Files\Messages\FileValidationMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>FileValidationMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Features\Parametricas\Messages\DANEMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>DANEMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Features\Parametricas\Messages\DelitoMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>DelitoMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Features\Parametricas\Messages\IndiceLAFTMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>IndiceLAFTMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Features\Shared\Messages\ExceptionMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ExceptionMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Features\Usuarios\Messages\UsuarioMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>UsuarioMessages.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Features\Files\Messages\FileValidationMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FileValidationMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Features\Parametricas\Messages\DANEMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DANEMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Features\Parametricas\Messages\DelitoMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DelitoMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Features\Parametricas\Messages\IndiceLAFTMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>IndiceLAFTMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Features\Shared\Messages\ExceptionMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExceptionMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Features\Usuarios\Messages\UsuarioMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UsuarioMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Afectaciones\Enums\" />
    <Folder Include="Features\DivisionSaldos\DTOs\CICETEXDTOs\" />
    <Folder Include="Features\DivisionSaldos\Enums\" />
  </ItemGroup>

</Project>
