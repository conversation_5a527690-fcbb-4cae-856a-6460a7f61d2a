﻿using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation;

namespace ApiCartera.Application.Validators
{
    public class SaldosSolicitudCarteraValidator: AbstractValidator<SaldosSolicitudCarteraDTO>
    {
        public SaldosSolicitudCarteraValidator()
        {
            RuleFor(x => x.PorcentajeCarteraPlan).NotNull().WithMessage(ValidationMessages.SaldosSolicitudPorcentajeCarteraPlan);
            RuleFor(x => x.CodigoSubproducto).NotNull().NotEmpty().WithMessage(ValidationMessages.SaldosSolicitudCodigoSubproducto);
            RuleFor(x => x.Periodicidad).NotNull().NotEmpty().WithMessage(ValidationMessages.SaldosSolicitudPeriodicidad);
            RuleFor(x => x.TipoCartera).NotNull().NotEmpty().WithMessage(ValidationMessages.SaldosSolicitudTipoCartera);
        }
    }
}
