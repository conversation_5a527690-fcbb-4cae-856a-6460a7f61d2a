2024-10-22 13:23:55.823 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T13:23:55.7953081-05:00"
2024-10-22 13:25:30.834 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T13:25:30.8183356-05:00"
2024-10-22 14:04:00.151 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:04:00.1403053-05:00"
2024-10-22 14:04:21.428 -05:00 [ERR] Hosting failed to start
System.Net.Sockets.SocketException (10060): Se produjo un error durante el intento de conexión ya que la parte conectada no respondió adecuadamente tras un periodo de tiempo, o bien se produjo un error en la conexión establecida ya que el host conectado no ha podido responder.
   at Renci.SshNet.Abstractions.SocketAbstraction.ConnectCore(Socket socket, EndPoint remoteEndpoint, TimeSpan connectTimeout, Boolean ownsSocket)
   at Renci.SshNet.Abstractions.SocketAbstraction.Connect(Socket socket, EndPoint remoteEndpoint, TimeSpan connectTimeout)
   at Renci.SshNet.Connection.ConnectorBase.SocketConnect(EndPoint endPoint, TimeSpan timeout)
   at Renci.SshNet.Connection.DirectConnector.Connect(IConnectionInfo connectionInfo)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:04:44.799 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:04:44.7870559-05:00"
2024-10-22 14:05:06.077 -05:00 [ERR] Hosting failed to start
System.Net.Sockets.SocketException (10060): Se produjo un error durante el intento de conexión ya que la parte conectada no respondió adecuadamente tras un periodo de tiempo, o bien se produjo un error en la conexión establecida ya que el host conectado no ha podido responder.
   at Renci.SshNet.Abstractions.SocketAbstraction.ConnectCore(Socket socket, EndPoint remoteEndpoint, TimeSpan connectTimeout, Boolean ownsSocket)
   at Renci.SshNet.Abstractions.SocketAbstraction.Connect(Socket socket, EndPoint remoteEndpoint, TimeSpan connectTimeout)
   at Renci.SshNet.Connection.ConnectorBase.SocketConnect(EndPoint endPoint, TimeSpan timeout)
   at Renci.SshNet.Connection.DirectConnector.Connect(IConnectionInfo connectionInfo)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:10:51.726 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:10:51.7092314-05:00"
2024-10-22 14:13:06.162 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:13:06.1546509-05:00"
2024-10-22 14:43:14.139 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:43:14.1300616-05:00"
2024-10-22 14:43:48.667 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:43:48.6582740-05:00"
2024-10-22 14:44:20.130 -05:00 [ERR] BackgroundService failed
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 14:44:20.159 -05:00 [FTL] The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 14:45:07.971 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:45:07.9645345-05:00"
2024-10-22 14:45:13.535 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:55:59.731 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:55:59.7228074-05:00"
2024-10-22 14:56:05.373 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:56:38.926 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:56:38.9179318-05:00"
2024-10-22 14:56:44.575 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:58:03.772 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:58:03.7634563-05:00"
2024-10-22 14:58:09.320 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 14:59:18.108 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T14:59:18.0988838-05:00"
2024-10-22 15:03:17.928 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:03:17.9183140-05:00"
2024-10-22 15:03:28.471 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 15:06:49.782 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:06:49.7754480-05:00"
2024-10-22 15:06:55.459 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SshAuthenticationException: Permission denied (password).
   at Renci.SshNet.ClientAuthentication.Authenticate(IConnectionInfoInternal connectionInfo, ISession session)
   at Renci.SshNet.ConnectionInfo.Authenticate(ISession session, IServiceFactory serviceFactory)
   at Renci.SshNet.Session.Connect()
   at Renci.SshNet.BaseClient.CreateAndConnectSession()
   at Renci.SshNet.BaseClient.Connect()
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 18
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 34
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2024-10-22 15:07:24.120 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:07:24.1114520-05:00"
2024-10-22 15:12:59.856 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:12:59.8417695-05:00"
2024-10-22 15:14:03.047 -05:00 [ERR] BackgroundService failed
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 15:14:03.077 -05:00 [FTL] The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 15:15:13.728 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:15:13.7196978-05:00"
2024-10-22 15:16:44.468 -05:00 [ERR] BackgroundService failed
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 15:16:44.493 -05:00 [FTL] The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.
Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00911: carácter no válido
https://docs.oracle.com/error-help/db/ora-00911/
   at OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.VerifyExecution(OracleConnectionImpl connectionImpl, Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   at OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReaderAsync(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64 internalInitialJSONFS, OracleConnection connection, ER_RefAndOutParamArgCtx er_refOutCtx, Boolean isDescribeOnly, Boolean isFromEF, Boolean bAsync)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderInternalAsync(Boolean requery, Boolean fillRequest, CommandBehavior behavior, Boolean bAsync, CancellationToken cancellationToken)
   at Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReaderAsyncHelper(Boolean requery, Boolean fillRequest, CommandBehavior behavior, CancellationToken cancellationToken, String callingMethodName)
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 305
   at ApiCartera.Infrastructure.Persitence.Oracle.OracleRepository.ObtenerSolicitudesParaDivisionDeSaldos(String connectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Persitence\Oracle\OracleRepository.cs:line 321
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosSaldosSolicitudCartera(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 37
   at WorkerServiceSaldos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceSaldos\Worker.cs:line 26
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2024-10-22 15:17:15.601 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:17:15.5906008-05:00"
2024-10-22 15:19:28.262 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:19:28.2498368-05:00"
2024-10-22 15:22:25.624 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:22:25.6136623-05:00"
2024-10-22 15:22:39.115 -05:00 [INF] Worker inicia ejecución a las: "2024-10-22T15:22:39.0966981-05:00"
