﻿using ApiCartera.Application.Features.Reportes.Queries;
using ApiCartera.Application.Features.Reportes.ViewModels;
using ApiCartera.Domain.Features.Reportes.Services;
using ApiCartera.Domain.Models;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Reportes.Handlers;

public class ContribucionesAportesIESHandler(
IReporteContribucionesAportesIESService reporteContribucionesAportesIESService,
IMapper mapper
) : IRequestHandler<ContribucionesAportesIESQuery, List<ContribucionesAportesIESVm>>
{
    public async Task<List<ContribucionesAportesIESVm>> Handle(ContribucionesAportesIESQuery request, CancellationToken cancellationToken)
    {
        List<MvReporteCiesAies>? resultado = await reporteContribucionesAportesIESService.ObtenerReporte(request.Desde, request.Hasta);
        return mapper.Map<List<ContribucionesAportesIESVm>>(resultado);
    }
}
