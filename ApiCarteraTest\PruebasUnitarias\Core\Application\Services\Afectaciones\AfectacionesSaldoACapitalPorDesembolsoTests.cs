﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorDesembolsoTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoPorAfectacionACapitalPorDesembolso), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoMarcaAporteIES(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 30,
                Saldo2 = 0,
                Saldo3 = 10000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 20,
                Saldo2 = 0,
                Saldo3 = 14400000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "AIES - APORTES IES",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "MATRICULA",
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));

            var afectacionesSaldoACapitalPorDesembolsoService = new AfectacionesSaldoACapitalPorDesembolsoService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalPorDesembolsoService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = respuestaSaldosCartera.Saldo3 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo1, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
     
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoPorAfectacionACapitalPorDesembolso), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoSinMarcaAporteIES(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 30,
                Saldo2 = 0,
                Saldo3 = 10000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 20,
                Saldo2 = 0,
                Saldo3 = 14400000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "NA - NO APLICA BENEFICIO",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "MATRICULA",
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));

            var afectacionesSaldoACapitalPorDesembolsoService = new AfectacionesSaldoACapitalPorDesembolsoService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalPorDesembolsoService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = respuestaSaldosCartera.Saldo4 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado.Saldo4);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo1, resultado.Saldo1);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoPorAfectacionACapitalPorDesembolso), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoVigenciaAnteriorA2024(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 30000,
                Saldo2 = 0,
                Saldo3 = 10000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 14400000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "NA - NO APLICA BENEFICIO",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "MATRICULA",
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));

            var afectacionesSaldoACapitalPorDesembolsoService = new AfectacionesSaldoACapitalPorDesembolsoService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalPorDesembolsoService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = respuestaSaldosCartera.Saldo1 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoPorAfectacionACapitalPorDesembolso), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoYLargoPlazoDesembolsoRubroDiferenteAMatriculaYSostenimiento(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 30000,
                Saldo2 = 0,
                Saldo3 = 10000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 200000,
                Saldo2 = 0,
                Saldo3 = 14400000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };
            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2023,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "NA - NO APLICA BENEFICIO",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "SUBSOSTENIMIENTO",
                Periodicidad = 6
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                    It.IsAny<long>(),
                    It.IsAny<int>()))
                .Returns(Task.FromResult(desembolso));

            var afectacionesSaldoACapitalPorDesembolsoService = new AfectacionesSaldoACapitalPorDesembolsoService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalPorDesembolsoService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = respuestaSaldosCartera.Saldo1 + movimiento.ValorGiro;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
    }
}
