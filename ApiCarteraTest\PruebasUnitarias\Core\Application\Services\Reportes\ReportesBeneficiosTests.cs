﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Reportes;
using ApiCartera.Domain.Features.Reportes.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;
using Moq;


namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Reportes;

public class ReportesBeneficiosTests
{/*
    [Fact]
    public async Task ObtenerReporteBeneficioAcuerdo001_RecibeFechas_RetornaDatos()
    {
        var beneficiosAcuerdo001 = new List<BeneficioAcuerdo001DTO> {
            new BeneficioAcuerdo001DTO()
            {
                IdSolicitud = 6272872,
                IdSignature = 627287211,
                CodigoSubproducto= TiposSubproducto.CORTO_PLAZO,
                CCN="",
                Documento="1122333444",
                NombreCompleto="JOSE MARIA CARRIZOSA",
                Sisben="",
                Estrato=0,
                Genero="",
                Ciudad="",
                Departamento="",
                CodigoDepartamento="",
                CodigoLinea=0,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                IES="",
                TipoCartera="",
                ModalidadCredito="",
                CapitalExigible=0,
                CapitalNoExigible=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                ValorTasaBeneficio=0,
                IPCBeneficio="",
                Saldo1=0,
                Saldo2=0,
                Saldo3=0,
                Saldo4=0,
                FechaGeneracion=DateTime.Now,
                CapitalVigente=0,
                CapitalVencido=0,
                CuotasPendientesPlanPagos=0,
                CalculoBeneficioAcuerdo001Saldo1=0,
                CalculoBeneficioAcuerdo001Saldo2=0,
                CalculoBeneficioAcuerdo001Saldo3=0,
                CalculoBeneficioAcuerdo001Saldo4=0
            },
            new BeneficioAcuerdo001DTO()
            {
                IdSolicitud = 6272873,
                IdSignature = 627287311,
                CodigoSubproducto= TiposSubproducto.CORTO_PLAZO,
                CCN="",
                Documento="",
                NombreCompleto="",
                Sisben="",
                Estrato=0,
                Genero="",
                Ciudad="",
                Departamento="",
                CodigoDepartamento="",
                CodigoLinea=0,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                IES="",
                TipoCartera="",
                ModalidadCredito="",
                CapitalExigible=0,
                CapitalNoExigible=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                ValorTasaBeneficio=0,
                IPCBeneficio="",
                Saldo1=0,
                Saldo2=0,
                Saldo3=0,
                Saldo4=0,
                FechaGeneracion=DateTime.Now,
                CapitalVigente=0,
                CapitalVencido=0,
                CuotasPendientesPlanPagos=0,
                CalculoBeneficioAcuerdo001Saldo1=0,
                CalculoBeneficioAcuerdo001Saldo2=0,
                CalculoBeneficioAcuerdo001Saldo3=0,
                CalculoBeneficioAcuerdo001Saldo4=0
            },
            new BeneficioAcuerdo001DTO()
            {
                IdSolicitud = 6272874,
                IdSignature = 627287411,
                CodigoSubproducto= TiposSubproducto.CORTO_PLAZO,
                CCN="",
                Documento="",
                NombreCompleto="",
                Sisben="",
                Estrato=0,
                Genero="",
                Ciudad="",
                Departamento="",
                CodigoDepartamento="",
                CodigoLinea=0,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                IES="",
                TipoCartera="",
                ModalidadCredito="",
                CapitalExigible=0,
                CapitalNoExigible=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                ValorTasaBeneficio=0,
                IPCBeneficio="",
                Saldo1=0,
                Saldo2=0,
                Saldo3=0,
                Saldo4=0,
                FechaGeneracion=DateTime.Now,
                CapitalVigente=0,
                CapitalVencido=0,
                CuotasPendientesPlanPagos=0,
                CalculoBeneficioAcuerdo001Saldo1=0,
                CalculoBeneficioAcuerdo001Saldo2=0,
                CalculoBeneficioAcuerdo001Saldo3=0,
                CalculoBeneficioAcuerdo001Saldo4=0
            },
            new BeneficioAcuerdo001DTO()
            {
                IdSolicitud = 6272875,
                IdSignature = 627287511,
                CodigoSubproducto= TiposSubproducto.CORTO_PLAZO,
                CCN="",
                Documento="",
                NombreCompleto="",
                Sisben="",
                Estrato=0,
                Genero="",
                Ciudad="",
                Departamento="",
                CodigoDepartamento="",
                CodigoLinea=0,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                IES="",
                TipoCartera="",
                ModalidadCredito="",
                CapitalExigible=0,
                CapitalNoExigible=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                ValorTasaBeneficio=0,
                IPCBeneficio="",
                Saldo1=0,
                Saldo2=0,
                Saldo3=0,
                Saldo4=0,
                FechaGeneracion=DateTime.Now,
                CapitalVigente=0,
                CapitalVencido=0,
                CuotasPendientesPlanPagos=0,
                CalculoBeneficioAcuerdo001Saldo1=0,
                CalculoBeneficioAcuerdo001Saldo2=0,
                CalculoBeneficioAcuerdo001Saldo3=0,
                CalculoBeneficioAcuerdo001Saldo4=0
            },
            new BeneficioAcuerdo001DTO()
            {
                IdSolicitud = 6272876,
                IdSignature = 627287611,
                CodigoSubproducto= TiposSubproducto.CORTO_PLAZO,
                CCN="",
                Documento="",
                NombreCompleto="",
                Sisben="",
                Estrato=0,
                Genero="",
                Ciudad="",
                Departamento="",
                CodigoDepartamento="",
                CodigoLinea=0,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                IES="",
                TipoCartera="",
                ModalidadCredito="",
                CapitalExigible=0,
                CapitalNoExigible=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                ValorTasaBeneficio=0,
                IPCBeneficio="",
                Saldo1=0,
                Saldo2=0,
                Saldo3=0,
                Saldo4=0,
                FechaGeneracion=DateTime.Now,
                CapitalVigente=0,
                CapitalVencido=0,
                CuotasPendientesPlanPagos=0,
                CalculoBeneficioAcuerdo001Saldo1=0,
                CalculoBeneficioAcuerdo001Saldo2=0,
                CalculoBeneficioAcuerdo001Saldo3=0,
                CalculoBeneficioAcuerdo001Saldo4=0
            }
        };

        var reporteBeneficioAcuerdo001Repository = new Mock<IReporteBeneficioAcuerdo001Repository>();
        reporteBeneficioAcuerdo001Repository.Setup(x => x.ObtenerReporte(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                                .Returns(Task.FromResult(beneficiosAcuerdo001));

        var reporteBeneficioAcuerdo001Service = new ReporteBeneficioAcuerdo001Service(reporteBeneficioAcuerdo001Repository.Object);

        var resultado = await reporteBeneficioAcuerdo001Service.ObtenerReporte(DateTime.Now, DateTime.Now);

        Assert.NotNull(resultado);
        Assert.Equal(5, resultado.Count);
    }

    [Fact]
    public async Task ObtenerReporteContribucionesAportesIES_RecibeFechas_RetornaDatos()
    {
        var contribucionesAportesIES = new List<ContribucionesAportesIESDTO> {
            new ContribucionesAportesIESDTO()
            {
                IdSolicitud = 6272876,
                IdSignature = 627287611,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                TipoCartera="",
                ModalidadCredito="",
                PeriodoGiro="",
                Anexo="",
                PorcentajeAsumeIES=0,
                PorcentajeAsumeIcetex=0,
                FechaGiro=DateTime.Now,
                FechaGeneracion=DateTime.Now,
                DiasCalculo=0,
                CapitalExigible=0,
                CapitalNoExigible=0,
                Saldo1=0,
                AjusteSaldo1=0,
                Saldo2=0,
                AjusteSaldo2=0,
                Saldo3=0,
                AjusteSaldo3=0,
                Saldo4=0,
                AjusteSaldo4=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                TasaMinimaSaldo1LargoPlazo=0,
                TasaMinimaSaldo2LargoPlazo=0,
                TasaMinimaSaldo3LargoPlazo=0,
                TasaMinimaSaldo4LargoPlazo=0,
                TasaMaximaSaldo1LargoPlazo=0,
                TasaMaximaSaldo2LargoPlazo=0,
                TasaMaximaSaldo3LargoPlazo=0,
                TasaMaximaSaldo4LargoPlazo=0,
                Factor1=0,
                Factor2=0,
                Factor3=0,
                Factor4=0,
                ResultadoFactor2Factor3=0,
                ValorAporteAplicado=0,
                ValorAporteIES=0,
                ValorAjuste=0,
                ValidadorAjuste="S",
                SaldoAporte=0,
                FechaFinEjecucion=DateTime.Now
            },
            new ContribucionesAportesIESDTO()
            {
                IdSolicitud = 6272877,
                IdSignature = 627287711,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                TipoCartera="",
                ModalidadCredito="",
                PeriodoGiro="",
                Anexo="",
                PorcentajeAsumeIES=0,
                PorcentajeAsumeIcetex=0,
                FechaGiro=DateTime.Now,
                FechaGeneracion=DateTime.Now,
                DiasCalculo=0,
                CapitalExigible=0,
                CapitalNoExigible=0,
                Saldo1=0,
                AjusteSaldo1=0,
                Saldo2=0,
                AjusteSaldo2=0,
                Saldo3=0,
                AjusteSaldo3=0,
                Saldo4=0,
                AjusteSaldo4=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                TasaMinimaSaldo1LargoPlazo=0,
                TasaMinimaSaldo2LargoPlazo=0,
                TasaMinimaSaldo3LargoPlazo=0,
                TasaMinimaSaldo4LargoPlazo=0,
                TasaMaximaSaldo1LargoPlazo=0,
                TasaMaximaSaldo2LargoPlazo=0,
                TasaMaximaSaldo3LargoPlazo=0,
                TasaMaximaSaldo4LargoPlazo=0,
                Factor1=0,
                Factor2=0,
                Factor3=0,
                Factor4=0,
                ResultadoFactor2Factor3=0,
                ValorAporteAplicado=0,
                ValorAporteIES=0,
                ValorAjuste=0,
                ValidadorAjuste="N",
                SaldoAporte=0,
                FechaFinEjecucion=DateTime.Now
            },
            new ContribucionesAportesIESDTO()
            {
                IdSolicitud = 6272878,
                IdSignature = 627287811,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                TipoCartera="",
                ModalidadCredito="",
                PeriodoGiro="",
                Anexo="",
                PorcentajeAsumeIES=0,
                PorcentajeAsumeIcetex=0,
                FechaGiro=DateTime.Now,
                FechaGeneracion=DateTime.Now,
                DiasCalculo=0,
                CapitalExigible=0,
                CapitalNoExigible=0,
                Saldo1=0,
                AjusteSaldo1=0,
                Saldo2=0,
                AjusteSaldo2=0,
                Saldo3=0,
                AjusteSaldo3=0,
                Saldo4=0,
                AjusteSaldo4=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                TasaMinimaSaldo1LargoPlazo=0,
                TasaMinimaSaldo2LargoPlazo=0,
                TasaMinimaSaldo3LargoPlazo=0,
                TasaMinimaSaldo4LargoPlazo=0,
                TasaMaximaSaldo1LargoPlazo=0,
                TasaMaximaSaldo2LargoPlazo=0,
                TasaMaximaSaldo3LargoPlazo=0,
                TasaMaximaSaldo4LargoPlazo=0,
                Factor1=0,
                Factor2=0,
                Factor3=0,
                Factor4=0,
                ResultadoFactor2Factor3=0,
                ValorAporteAplicado=0,
                ValorAporteIES=0,
                ValorAjuste=0,
                ValidadorAjuste="S",
                SaldoAporte=0,
                FechaFinEjecucion=DateTime.Now
            },
            new ContribucionesAportesIESDTO()
            {
                IdSolicitud = 6272879,
                IdSignature = 627287911,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                TipoCartera="",
                ModalidadCredito="",
                PeriodoGiro="",
                Anexo="",
                PorcentajeAsumeIES=0,
                PorcentajeAsumeIcetex=0,
                FechaGiro=DateTime.Now,
                FechaGeneracion=DateTime.Now,
                DiasCalculo=0,
                CapitalExigible=0,
                CapitalNoExigible=0,
                Saldo1=0,
                AjusteSaldo1=0,
                Saldo2=0,
                AjusteSaldo2=0,
                Saldo3=0,
                AjusteSaldo3=0,
                Saldo4=0,
                AjusteSaldo4=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                TasaMinimaSaldo1LargoPlazo=0,
                TasaMinimaSaldo2LargoPlazo=0,
                TasaMinimaSaldo3LargoPlazo=0,
                TasaMinimaSaldo4LargoPlazo=0,
                TasaMaximaSaldo1LargoPlazo=0,
                TasaMaximaSaldo2LargoPlazo=0,
                TasaMaximaSaldo3LargoPlazo=0,
                TasaMaximaSaldo4LargoPlazo=0,
                Factor1=0,
                Factor2=0,
                Factor3=0,
                Factor4=0,
                ResultadoFactor2Factor3=0,
                ValorAporteAplicado=0,
                ValorAporteIES=0,
                ValorAjuste=0,
                ValidadorAjuste="S",
                SaldoAporte=0,
                FechaFinEjecucion=DateTime.Now
            },
            new ContribucionesAportesIESDTO()
            {
                IdSolicitud = 6272880,
                IdSignature = 627288011,
                Linea="",
                Sublinea="",
                CodigoSNIES="564",
                TipoCartera="",
                ModalidadCredito="",
                PeriodoGiro="",
                Anexo="",
                PorcentajeAsumeIES=0,
                PorcentajeAsumeIcetex=0,
                FechaGiro=DateTime.Now,
                FechaGeneracion=DateTime.Now,
                DiasCalculo=0,
                CapitalExigible=0,
                CapitalNoExigible=0,
                Saldo1=0,
                AjusteSaldo1=0,
                Saldo2=0,
                AjusteSaldo2=0,
                Saldo3=0,
                AjusteSaldo3=0,
                Saldo4=0,
                AjusteSaldo4=0,
                ValorTasaContratacion=0,
                IPCContratacion=0,
                TasaMinimaSaldo1LargoPlazo=0,
                TasaMinimaSaldo2LargoPlazo=0,
                TasaMinimaSaldo3LargoPlazo=0,
                TasaMinimaSaldo4LargoPlazo=0,
                TasaMaximaSaldo1LargoPlazo=0,
                TasaMaximaSaldo2LargoPlazo=0,
                TasaMaximaSaldo3LargoPlazo=0,
                TasaMaximaSaldo4LargoPlazo=0,
                Factor1=0,
                Factor2=0,
                Factor3=0,
                Factor4=0,
                ResultadoFactor2Factor3=0,
                ValorAporteAplicado=0,
                ValorAporteIES=0,
                ValorAjuste=0,
                ValidadorAjuste="N",
                SaldoAporte=0,
                FechaFinEjecucion=DateTime.Now
            }
        };

        var reporteContribucionesAportesIESRepository = new Mock<IReporteContribucionesAportesIESRepository>();
        reporteContribucionesAportesIESRepository.Setup(x => x.ObtenerReporte(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                                .Returns(Task.FromResult(contribucionesAportesIES));

        var reporteContribucionesAportesIESService = new ReporteContribucionesAportesIESService(reporteContribucionesAportesIESRepository.Object);

        var resultado = await reporteContribucionesAportesIESService.ObtenerReporte(DateTime.Now, DateTime.Now);

        Assert.NotNull(resultado);
        Assert.Equal(5, resultado.Count);
    }

    [Fact]
    public async Task ObtenerReporteAportesIESUniversidades_RecibeFechas_RetornaDatos()
    {
        var aportesIESUniversidades = new List<AportesIESUniversidadesDTO> {
            new AportesIESUniversidadesDTO()
            {
                CodigoSNIES=564,
                RelacionGiro=654654,
                NombreCompleto="",
                Documento=123132,
                IdSolicitud=0,
                TipoLineaCredito="",
                TipoSubLineaCredito="",
                TipoCartera="",
                ModalidadCredito="",
                Sisben="",
                Estrato=0,
                ValorSubsidioIES=0,
                ValorIESLargoPlazo=0,
                ValorIESCortoPlazo=0,
                ValorIcetexSubsidio=0,
                ValorIcetexCredito=0,
                ValorAlianzaSubsidio=0,
                ValorAlianzaCredito=0,
                ValorPrima=0,
                ValorTotal=0,
                TotalGirar=0,
                YearGiro=0,
                SemestreGiro=0,
                FechaGiro=DateTime.Now,
                EstadoResolucion="",
                ValorLegRenovado=0,
                PorcentajeIPC=0,
                Puntos=0,
                PorcentajePuntosEquivalentes=0,
                ValorExigible=0,
                ValorNoExigible=0,
                Periodicidad=6,
                TipoAnexo="",
                TipoBeneficio="",
                PorcentajeAporteIES=0,
                ValorAporteIES=0,
                PorcentajeAporteIcetex=0,
                ValorAporteIcetex=0,
                PorcentajeFondoSostenibilidad=0,
                SubfondoIES=0,
                ValorPorcPiloRezagado=0,
                ConteoGirosConAporte=0,
                SaldoCapital=0,
                DiasInteres=0,
                Factor2=0,
                Factor3=0,
                EsComplementario="",
                FechaGiroAnteriorAporte=DateTime.Now,
                FechaGiroConAporte=DateTime.Now,
                NitIES=454
            },
            new AportesIESUniversidadesDTO()
            {
                CodigoSNIES=564,
                RelacionGiro=654654,
                NombreCompleto="",
                Documento=456455,
                IdSolicitud=0,
                TipoLineaCredito="",
                TipoSubLineaCredito="",
                TipoCartera="",
                ModalidadCredito="",
                Sisben="",
                Estrato=0,
                ValorSubsidioIES=0,
                ValorIESLargoPlazo=0,
                ValorIESCortoPlazo=0,
                ValorIcetexSubsidio=0,
                ValorIcetexCredito=0,
                ValorAlianzaSubsidio=0,
                ValorAlianzaCredito=0,
                ValorPrima=0,
                ValorTotal=0,
                TotalGirar=0,
                YearGiro=0,
                SemestreGiro=0,
                FechaGiro=DateTime.Now,
                EstadoResolucion="",
                ValorLegRenovado=0,
                PorcentajeIPC=0,
                Puntos=0,
                PorcentajePuntosEquivalentes=0,
                ValorExigible=0,
                ValorNoExigible=0,
                Periodicidad=6,
                TipoAnexo="",
                TipoBeneficio="",
                PorcentajeAporteIES=0,
                ValorAporteIES=0,
                PorcentajeAporteIcetex=0,
                ValorAporteIcetex=0,
                PorcentajeFondoSostenibilidad=0,
                SubfondoIES=0,
                ValorPorcPiloRezagado=0,
                ConteoGirosConAporte=0,
                SaldoCapital=0,
                DiasInteres=0,
                Factor2=0,
                Factor3=0,
                EsComplementario="",
                FechaGiroAnteriorAporte=DateTime.Now,
                FechaGiroConAporte=DateTime.Now,
                NitIES=546
            },
            new AportesIESUniversidadesDTO()
            {
                CodigoSNIES=564,
                RelacionGiro=654654,
                NombreCompleto="",
                Documento=545454,
                IdSolicitud=0,
                TipoLineaCredito="",
                TipoSubLineaCredito="",
                TipoCartera="",
                ModalidadCredito="",
                Sisben="",
                Estrato=0,
                ValorSubsidioIES=0,
                ValorIESLargoPlazo=0,
                ValorIESCortoPlazo=0,
                ValorIcetexSubsidio=0,
                ValorIcetexCredito=0,
                ValorAlianzaSubsidio=0,
                ValorAlianzaCredito=0,
                ValorPrima=0,
                ValorTotal=0,
                TotalGirar=0,
                YearGiro=0,
                SemestreGiro=0,
                FechaGiro=DateTime.Now,
                EstadoResolucion="",
                ValorLegRenovado=0,
                PorcentajeIPC=0,
                Puntos=0,
                PorcentajePuntosEquivalentes=0,
                ValorExigible=0,
                ValorNoExigible=0,
                Periodicidad=4,
                TipoAnexo="",
                TipoBeneficio="",
                PorcentajeAporteIES=0,
                ValorAporteIES=0,
                PorcentajeAporteIcetex=0,
                ValorAporteIcetex=0,
                PorcentajeFondoSostenibilidad=0,
                SubfondoIES=0,
                ValorPorcPiloRezagado=0,
                ConteoGirosConAporte=0,
                SaldoCapital=0,
                DiasInteres=0,
                Factor2=0,
                Factor3=0,
                EsComplementario="",
                FechaGiroAnteriorAporte=DateTime.Now,
                FechaGiroConAporte=DateTime.Now,
                NitIES=546
            },
            new AportesIESUniversidadesDTO()
            {
                CodigoSNIES=564,
                RelacionGiro=654654,
                NombreCompleto="",
                Documento=54564564,
                IdSolicitud=0,
                TipoLineaCredito="",
                TipoSubLineaCredito="",
                TipoCartera="",
                ModalidadCredito="",
                Sisben="",
                Estrato=0,
                ValorSubsidioIES=0,
                ValorIESLargoPlazo=0,
                ValorIESCortoPlazo=0,
                ValorIcetexSubsidio=0,
                ValorIcetexCredito=0,
                ValorAlianzaSubsidio=0,
                ValorAlianzaCredito=0,
                ValorPrima=0,
                ValorTotal=0,
                TotalGirar=0,
                YearGiro=0,
                SemestreGiro=0,
                FechaGiro=DateTime.Now,
                EstadoResolucion="",
                ValorLegRenovado=0,
                PorcentajeIPC=0,
                Puntos=0,
                PorcentajePuntosEquivalentes=0,
                ValorExigible=0,
                ValorNoExigible=0,
                Periodicidad=6,
                TipoAnexo="",
                TipoBeneficio="",
                PorcentajeAporteIES=0,
                ValorAporteIES=0,
                PorcentajeAporteIcetex=0,
                ValorAporteIcetex=0,
                PorcentajeFondoSostenibilidad=0,
                SubfondoIES=0,
                ValorPorcPiloRezagado=0,
                ConteoGirosConAporte=0,
                SaldoCapital=0,
                DiasInteres=0,
                Factor2=0,
                Factor3=0,
                EsComplementario="",
                FechaGiroAnteriorAporte=DateTime.Now,
                FechaGiroConAporte=DateTime.Now,
                NitIES=456
            },
            new AportesIESUniversidadesDTO()
            {
                CodigoSNIES=564,
                RelacionGiro=654654,
                NombreCompleto="",
                Documento=5644889,
                IdSolicitud=0,
                TipoLineaCredito="",
                TipoSubLineaCredito="",
                TipoCartera="",
                ModalidadCredito="",
                Sisben="",
                Estrato=0,
                ValorSubsidioIES=0,
                ValorIESLargoPlazo=0,
                ValorIESCortoPlazo=0,
                ValorIcetexSubsidio=0,
                ValorIcetexCredito=0,
                ValorAlianzaSubsidio=0,
                ValorAlianzaCredito=0,
                ValorPrima=0,
                ValorTotal=0,
                TotalGirar=0,
                YearGiro=0,
                SemestreGiro=0,
                FechaGiro=DateTime.Now,
                EstadoResolucion="",
                ValorLegRenovado=0,
                PorcentajeIPC=0,
                Puntos=0,
                PorcentajePuntosEquivalentes=0,
                ValorExigible=0,
                ValorNoExigible=0,
                Periodicidad=6,
                TipoAnexo="",
                TipoBeneficio="",
                PorcentajeAporteIES=0,
                ValorAporteIES=0,
                PorcentajeAporteIcetex=0,
                ValorAporteIcetex=0,
                PorcentajeFondoSostenibilidad=0,
                SubfondoIES=0,
                ValorPorcPiloRezagado=0,
                ConteoGirosConAporte=0,
                SaldoCapital=0,
                DiasInteres=0,
                Factor2=0,
                Factor3=0,
                EsComplementario="",
                FechaGiroAnteriorAporte=DateTime.Now,
                FechaGiroConAporte=DateTime.Now,
                NitIES=456
            }
        };

        var reporteAportesIESUniversidadesRepository = new Mock<IReporteAportesIESUniversidadesRepository>();
        reporteAportesIESUniversidadesRepository.Setup(x => x.ObtenerReporte(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                                .Returns(Task.FromResult(aportesIESUniversidades));

        var reporteAportesIESUniversidadesService = new ReporteAportesIESUniversidadesService(reporteAportesIESUniversidadesRepository.Object);

        var resultado = await reporteAportesIESUniversidadesService.ObtenerReporte(DateTime.Now, DateTime.Now);

        Assert.NotNull(resultado);
        Assert.Equal(5, resultado.Count);
    }

    [Fact]
    public async Task ObtenerValidacionDatosSolicitud_RecibeFechas_RetornaDatos()
    {
        var aportesIESUniversidades = new List<ValidacionDatosSolicitudDTO> {
            new ValidacionDatosSolicitudDTO()
            {
                IdSolicitud = 6272872,
                SumatoriaSaldos=0,
                ValorCapitalCore=0,
                Diferencia=0
            },
            new ValidacionDatosSolicitudDTO()
            {
                IdSolicitud = 6272873,
                SumatoriaSaldos=0,
                ValorCapitalCore=0,
                Diferencia=0
            },
            new ValidacionDatosSolicitudDTO()
            {
                IdSolicitud = 6272874,
                SumatoriaSaldos=0,
                ValorCapitalCore=0,
                Diferencia=0
            },
            new ValidacionDatosSolicitudDTO()
            {
                IdSolicitud = 6272875,
                SumatoriaSaldos=0,
                ValorCapitalCore=0,
                Diferencia=0
            },
            new ValidacionDatosSolicitudDTO()
            {
                IdSolicitud = 6272876,
                SumatoriaSaldos=0,
                ValorCapitalCore=0,
                Diferencia=0
            }
        };

        var reporteValidacionDatosSolicitudRepository = new Mock<IReporteValidacionDatosSolicitudRepository>();
        reporteValidacionDatosSolicitudRepository.Setup(x => x.ObtenerReporte(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                                .Returns(Task.FromResult(aportesIESUniversidades));

        var reporteValidacionDatosSolicitudService = new ReporteValidacionDatosSolicitudService(reporteValidacionDatosSolicitudRepository.Object);

        var resultado = await reporteValidacionDatosSolicitudService.ObtenerReporte(DateTime.Now, DateTime.Now);

        Assert.NotNull(resultado);
        Assert.Equal(5, resultado.Count);
    }
    */
}
