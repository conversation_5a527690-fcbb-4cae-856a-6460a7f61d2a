﻿using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("SOLICITUD_BENEFICIO")]
public class SolicitudBeneficioEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public long IdSolicitud { get; set; }
    public int Periodicidad { get; set; }
    public int Years { get; set; }
    public int Semestre { get; set; }
    public double ValorIes { get; set; }
    public DateTime Fecha { get; set; }
    public int IdBeneficio { get; set; }
    public int Puntos { get; set; }
}
