﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Reportes.DTOs;

namespace ApiCartera.Application.Services.Reportes
{
    public class ReporteValidacionDatosSolicitudService(IReporteValidacionDatosSolicitudRepository reporteValidacionDatosSolicitudRepository) : IReporteValidacionDatosSolicitudService
    {
        public async Task<List<ValidacionDatosSolicitudDTO>> ObtenerReporte(DateTime desde, DateTime hasta)
        {
            var response = await reporteValidacionDatosSolicitudRepository.ObtenerReporte(desde, hasta);
            return response;
        }
    }
}
