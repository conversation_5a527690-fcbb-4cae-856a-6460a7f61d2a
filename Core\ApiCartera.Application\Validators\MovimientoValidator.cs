﻿using ApiCartera.Domain.Features.Afectaciones.DTOs;
using FluentValidation;

namespace ApiCartera.Application.Validators
{
    public class MovimientoValidator : AbstractValidator<MovimientoDTO>
    {
        public MovimientoValidator()
        {
            RuleFor(x => x.<PERSON>)
                .NotNull()
                .NotEmpty()
                .GreaterThan(0)
                .WithMessage(ValidationMessages.MovimientoValorGiro);
        }
    }
}
