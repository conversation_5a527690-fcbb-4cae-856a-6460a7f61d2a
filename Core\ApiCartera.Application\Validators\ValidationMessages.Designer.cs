﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ApiCartera.Application.Validators {
    using System;


    /// <summary>
    ///   Clase de recurso fuertemente tipado, para buscar cadenas traducidas, etc.
    /// </summary>
    // StronglyTypedResourceBuilder generó automáticamente esta clase
    // a través de una herramienta como ResGen o Visual Studio.
    // Para agregar o quitar un miembro, edite el archivo .ResX y, a continuación, vuelva a ejecutar ResGen
    // con la opción /str o recompile su proyecto de VS.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ValidationMessages {

        private static global::System.Resources.ResourceManager resourceMan;

        private static global::System.Globalization.CultureInfo resourceCulture;

        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ValidationMessages() {
        }

        /// <summary>
        ///   Devuelve la instancia de ResourceManager almacenada en caché utilizada por esta clase.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ApiCartera.Application.Validators.ValidationMessages", typeof(ValidationMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }

        /// <summary>
        ///   Reemplaza la propiedad CurrentUICulture del subproceso actual para todas las
        ///   búsquedas de recursos mediante esta clase de recurso fuertemente tipado.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a Las cuotas pendientes no pueden ser superiores al número total de cuotas del crédito..
        /// </summary>
        internal static string CuotasPendientesMayorANumTotalCuotas {
            get {
                return ResourceManager.GetString("CuotasPendientesMayorANumTotalCuotas", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a Las cuotas pendientes no pueden ser menores a cero..
        /// </summary>
        internal static string CuotasPendientesMenorQueCero {
            get {
                return ResourceManager.GetString("CuotasPendientesMenorQueCero", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró ningún registro de desembolsos relacionado con el id solicitud.
        /// </summary>
        internal static string Desembolso {
            get {
                return ResourceManager.GetString("Desembolso", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} del desembolso es requerido para poder realizar las operaciones y debe ser mayor a cero..
        /// </summary>
        internal static string DesembolsoPeriodicidad {
            get {
                return ResourceManager.GetString("DesembolsoPeriodicidad", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} del desembolso es requerido para poder realizar las operaciones y debe ser mayor a cero..
        /// </summary>
        internal static string DesembolsoTotalGirar {
            get {
                return ResourceManager.GetString("DesembolsoTotalGirar", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} del movimiento es requerido para poder realizar las operaciones y debe ser mayor a cero..
        /// </summary>
        internal static string MontoMovimientoDeCapital {
            get {
                return ResourceManager.GetString("MontoMovimientoDeCapital", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró ningún registro de movimientos relacionado con el id solicitud.
        /// </summary>
        internal static string Movimiento {
            get {
                return ResourceManager.GetString("Movimiento", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} del movimiento es requerido para poder realizar las operaciones..
        /// </summary>
        internal static string MovimientosNoRelacion {
            get {
                return ResourceManager.GetString("MovimientosNoRelacion", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} del movimiento es requerido para poder realizar las operaciones y debe ser mayor a cero..
        /// </summary>
        internal static string MovimientoValorGiro {
            get {
                return ResourceManager.GetString("MovimientoValorGiro", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró ningún registro de saldos cartera relacionado con el id solicitud.
        /// </summary>
        internal static string SaldosCartera {
            get {
                return ResourceManager.GetString("SaldosCartera", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró ningún registro de saldos cartera relacionado al id solicitud {PropertyValue}..
        /// </summary>
        internal static string SaldosCarteraIdSolicitud {
            get {
                return ResourceManager.GetString("SaldosCarteraIdSolicitud", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} de los saldos solicitud es requerido para poder realizar las operaciones..
        /// </summary>
        internal static string SaldosSolicitudCodigoSubproducto {
            get {
                return ResourceManager.GetString("SaldosSolicitudCodigoSubproducto", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a No se encontró ningún registro de saldos solicitud relacionado con el id solicitud.
        /// </summary>
        internal static string SaldosSolicitudIdSolicitud {
            get {
                return ResourceManager.GetString("SaldosSolicitudIdSolicitud", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} de los saldos solicitud es requerido para poder realizar las operaciones..
        /// </summary>
        internal static string SaldosSolicitudPeriodicidad {
            get {
                return ResourceManager.GetString("SaldosSolicitudPeriodicidad", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} de los saldos solicitud es requerido para poder realizar las operaciones..
        /// </summary>
        internal static string SaldosSolicitudPorcentajeCarteraPlan {
            get {
                return ResourceManager.GetString("SaldosSolicitudPorcentajeCarteraPlan", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El campo {PropertyName} de los saldos solicitud es requerido para poder realizar las operaciones..
        /// </summary>
        internal static string SaldosSolicitudTipoCartera {
            get {
                return ResourceManager.GetString("SaldosSolicitudTipoCartera", resourceCulture);
            }
        }

        /// <summary>
        ///   Busca una cadena traducida similar a El formato de fecha debe ser válido.
        /// </summary>
        internal static string FormatoFechaEfectivaMovimiento {
            get {
                return ResourceManager.GetString("FormatoFechaEfectivaMovimiento", resourceCulture);
            }
        }
    }
}
