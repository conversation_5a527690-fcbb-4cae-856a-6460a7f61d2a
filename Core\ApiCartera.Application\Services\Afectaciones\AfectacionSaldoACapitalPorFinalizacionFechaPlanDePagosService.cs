﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.Shared.Constants;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionSaldoACapitalPorFinalizacionFechaPlanDePagosService(
        IDesembolsoRepository desembolsoRepository,
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraPorFinalizacionFechaPlanDePagoService
    {
        public async Task<List<SaldosPorAfectacionDTO>> CalcularSaldosCarteraPorFinalizacionFechaPlanDePago()
        {
            var saldosPorAfectacionList = new List<SaldosPorAfectacionDTO>();

            var desembolsos = await desembolsoRepository.ObtenerTodosLosDesembolsosConFechaPlanPagosFinalizada();
            ValidarDesembolsos(desembolsos);

            foreach (var desembolso in desembolsos) {
                var saldosPorAfectacion = new SaldosPorAfectacionDTO();
                saldosPorAfectacion.Saldos = new List<SaldosCarteraDTO>();

                if (desembolso.Marca.Equals(TiposBeneficio.NO_APLICA_BENEFICIO))
                {
                    var saldosCarteraCortoPlazo = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(desembolso.IdSolicitud, TiposSubproducto.CORTO_PLAZO);

                    var nuevosSaldosCortoPlazo = saldosCarteraCortoPlazo != null ? await CalcularNuevosSaldos((SaldosCarteraDTO)saldosCarteraCortoPlazo.Clone(), desembolso.TotalGirar, desembolso.PorcentajeCarteraPlan) : null;
                    var saldosCarteraLargoPlazoAmortizacion = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(desembolso.IdSolicitud, TiposSubproducto.LARGO_PLAZO_AMORTIZACION);
                    var porcentajeNoExigible = 1 - desembolso.PorcentajeCarteraPlan;
                    var nuevosSaldosLargoPlazo = saldosCarteraLargoPlazoAmortizacion != null ? await CalcularNuevosSaldos((SaldosCarteraDTO)saldosCarteraLargoPlazoAmortizacion.Clone(), desembolso.TotalGirar, porcentajeNoExigible) : null;

                    if (saldosCarteraLargoPlazoAmortizacion == null)
                    {
                        var saldosCarteraLargoPlazoEstudio = await saldosCarteraRepository.ObtenerSaldosCarteraPorIdSolicitudYCodigoSubproducto(desembolso.IdSolicitud, TiposSubproducto.LARGO_PLAZO);
                        porcentajeNoExigible = 1 - desembolso.PorcentajeCarteraPlan;
                        nuevosSaldosLargoPlazo = saldosCarteraLargoPlazoEstudio != null ? await CalcularNuevosSaldos((SaldosCarteraDTO)saldosCarteraLargoPlazoEstudio.Clone(), desembolso.TotalGirar, porcentajeNoExigible) : null;
                    }

                    if (nuevosSaldosCortoPlazo != null)
                    {
                        saldosPorAfectacion.Saldos.Add(nuevosSaldosCortoPlazo);
                    }

                    if (nuevosSaldosLargoPlazo != null)
                    {
                        saldosPorAfectacion.Saldos.Add(nuevosSaldosLargoPlazo);
                    }
                }

                saldosPorAfectacionList.Add(saldosPorAfectacion);
            }

            return saldosPorAfectacionList;
        }

        private static void ValidarDesembolsos(List<DesembolsoDTO> desembolsos)
        {
            var desembolsoValidador = new DesembolsoValidator();
            foreach (var desembolso in desembolsos)
            {
                desembolsoValidador.ValidateAndThrowCustomValidationException(desembolso);
            }
        }

        public async Task<SaldosCarteraDTO> CalcularNuevosSaldos(SaldosCarteraDTO saldos, double totalGirar, double porcentajeCarteraPlan)
        {
            var desembolsoPorSubproducto = totalGirar * porcentajeCarteraPlan;
            saldos.Saldo1 += desembolsoPorSubproducto;
            saldos.Saldo4 -= desembolsoPorSubproducto;
            saldos.Saldo4 = saldos.Saldo4 < 0 ? 0 : saldos.Saldo4;
            var nuevosSaldosCartera = this.MapearSaldosCartera(saldos);

            await solicitudCarteraActivaRepository.GuardarSaldos(nuevosSaldosCartera);

            return saldos;
        }
    }
}
