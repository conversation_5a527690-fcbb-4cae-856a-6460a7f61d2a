﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using ApiCartera.API.Requests.Parametricas;
using ApiCartera.API.Wrappers;
using ApiCartera.Application.Contracts.Auth.Models;
using ApiCartera.Application.Features.Auth.Commands.IniciarSesion;
namespace ApiCartera.API.Controllers.Security
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController(IMediator _mediator) : ControllerBase
    {
        [HttpPost("IniciarSesion")]
        [ProducesResponseType<ResponseWrapper<InicioSesionResponse>>(StatusCodes.Status200OK)]
        public async Task<InicioSesionResponse> IniciarSesion([FromBody] IniciarSesionRequest request) =>
            await _mediator.Send(new IniciarSesionCommand(request.Email,
                request.Password,
                Request.HttpContext.Connection.RemoteIpAddress.ToString()));
    }
}
