﻿using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Sarlaft.Application.Behaviours;
using Sarlaft.Application.Config;
using Sarlaft.Application.Contracts.Auth;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Infrastructure.Config;
using Sarlaft.Infrastructure.Persitence.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sarlaft.Test
{
    public static class DependencyContainer
    {
        public static IServiceCollection Get()
        {
            IServiceCollection services = new ServiceCollection();
            services.AddApplicationServices();
            return services;
        }
        public static IServiceProvider Build()
        {
            IServiceCollection services = new ServiceCollection();
            services.AddApplicationServices();
            services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepositoryMemory<>));
            var serviceProvider = services.BuildServiceProvider();
            return serviceProvider;
        }
    }
}
