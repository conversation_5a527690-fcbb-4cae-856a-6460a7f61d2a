using ApiCartera.Application.Contracts.Persistence.Http;
using ApiCartera.Infrastructure.Http;
using Serilog;
using WorkerServiceFinalizacionPlanPagos;

var builder = Host.CreateApplicationBuilder(args);

Log.Logger = new LoggerConfiguration()
    .ReadFrom
    .Configuration(builder.Configuration)
    .CreateLogger();

builder.Logging.ClearProviders();
builder.Logging.AddSerilog();

builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
builder.Services.AddSingleton<IHttpRequestRepository, HttpRequestRepository>();

builder.Services.AddHostedService<Worker>();
builder.Services.AddProblemDetails();

var host = builder.Build();
host.Run();
