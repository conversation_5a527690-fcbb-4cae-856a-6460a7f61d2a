using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Features.Parametricas.Queries.Ciudades.ObtenerCiudades;
using Sarlaft.Application.Features.Parametricas.ViewModels;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Test.Application.Config;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.Ciudades
{
    public class ObtenerCiudadesTest
    {
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var mockRepository = new Mock<IGenericRepository<Ciudad>>();

            var mapper = MapperConfig.Build();

            var handler = new ObtenerCiudadesQueryHandler(mockRepository.Object, mapper);
            var query = new ObtenerCiudadesQuery();
            var ciudadesInsertar = new List<Ciudad>
            {
                new Ciudad
                {
                    Nombre = "Bucaramanga",
                    CodigoDANE = "031",
                    CodigoDANECompleto = "91031",
                    Poblacion = 23298
                },
                new Ciudad
                {
                    Nombre = "Piedecuesta",
                    CodigoDANE = "032",
                    CodigoDANECompleto = "91032",
                    Poblacion = 1235
                },
            };


            var ciudadesVm = mapper.Map<List<CiudadVm>>(ciudadesInsertar);

            // Configura el comportamiento esperado para el repositorio y el mapeador
            mockRepository.Setup(repo => repo.ObtenerTodosAsync()).ReturnsAsync(ciudadesInsertar);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(result.Count(), ciudadesVm.Count());
            Assert.All(result, ciudad => ciudadesVm.Any(ciudadVm => ciudadVm.Nombre == ciudad.Nombre));
            Assert.All(result, ciudad => ciudadesInsertar.Any(ciudadVm => ciudadVm.Nombre == ciudad.Nombre));
        }
    }
}