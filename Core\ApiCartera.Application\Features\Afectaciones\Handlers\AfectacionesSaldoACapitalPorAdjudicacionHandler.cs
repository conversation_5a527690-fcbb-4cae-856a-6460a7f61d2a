﻿using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionesSaldoACapitalPorAdjudicacionHandler(
        IAfectacionesSaldoACapitalPorAdjudicacionService _calcularSaldosCarteraService,
        IMapper _mapper) : IRequestHandler<AfectacionesSaldoACapitalPorAdjudicacionCommand, List<SaldoCarteraVm>>
    {
        public async Task<List<SaldoCarteraVm>> Handle(AfectacionesSaldoACapitalPorAdjudicacionCommand request, CancellationToken cancellationToken)
        {
            var resultado = await _calcularSaldosCarteraService.CalcularSaldosCartera(request.Movimiento);
            return _mapper.Map<List<SaldoCarteraVm>>(resultado);
        }
    }
}
