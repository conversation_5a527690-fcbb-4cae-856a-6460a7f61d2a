﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalCondonacionGraduacionSaberProServiceTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoPlazoCondonacionGraduacionSaberPro), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoCortoPlazo_RetornaSaldo1EnCeroYSaldo2Disminuido(MovimientoDTO movimiento)
        {
            var saldosCarteraCortoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 1800000,
                Saldo2 = 600000,
                Saldo3 = 600000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                IdSaldosSolicitudCartera = 149
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    1))
                .Returns(Task.FromResult(saldosCarteraCortoPlazo));
            var afectacionesSaldoACapitalCondonacionGraduacionSaberProService = new AfectacionesSaldoACapitalCondonacionGraduacionSaberProService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalCondonacionGraduacionSaberProService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(0, resultado.Saldo1);
            Assert.Equal(400000, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoLargoPlazoCondonacionGraduacionSaberPro), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoLargoPlazo_RetornaSaldo1Disminuido(MovimientoDTO movimiento)
        {
            var saldosCarteraLargoPlazo = new SaldosCarteraDTO()
            {
                Saldo1 = 18000000,
                Saldo2 = 6000000,
                Saldo3 = 6000000,
                Saldo4 = 0,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                IdSaldosSolicitudCartera = 149
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            solicitudCarteraActivaRepositoryMock
                .Setup(x => x.GuardarSaldos(
                    It.IsAny<SaldosCartera>()));
            saldosCarteraRepositoryMock
                .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                    It.IsAny<long>(),
                    2))
                .Returns(Task.FromResult(saldosCarteraLargoPlazo));
            var afectacionesSaldoACapitalCondonacionGraduacionSaberProService = new AfectacionesSaldoACapitalCondonacionGraduacionSaberProService(
                saldosCarteraRepositoryMock.Object,
                solicitudCarteraActivaRepositoryMock.Object);

            var respuestaSaldosCartera = await saldosCarteraRepositoryMock.Object.ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdSolicitud, movimiento.IdSubproducto);
            var resultado = await afectacionesSaldoACapitalCondonacionGraduacionSaberProService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(14000000, resultado.Saldo1);
            Assert.Equal(respuestaSaldosCartera.Saldo2, resultado.Saldo2);
            Assert.Equal(respuestaSaldosCartera.Saldo3, resultado.Saldo3);
            Assert.Equal(respuestaSaldosCartera.Saldo4, resultado.Saldo4);
        }
    }
}
