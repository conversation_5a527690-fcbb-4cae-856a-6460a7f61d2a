﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Application.Resources;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.BeneficioAcuerdo001.Services;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Models.CICETEX;
using ApiCartera.Domain.Models.DivisionSaldos;
using ApiCartera.Domain.ValueObjects;
using FluentValidation;

namespace ApiCartera.Application.Services.DivisionSaldos;

public class CalculadoraSaldos(
    IRepository<GuardarSaldosCarteraEntity, string> saldosRepository,
    IRepository<MbtControlDivisionSaldosEntity, string> controlRepository,
    IDetallesCreditoServices detallesCredito,
    ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository,
    ICalcularTasasCarteraService _calcularTasasCarteraService, IActualizarControlDivisionSaldos _actualizarDivision) 
    : AfectacionesService, ICalculadoraSaldosService
{
    private const int PORCENTAJE_TOTAL_CARTERA_PLAN = 1;
    private const int ANIO_CONTRIBUCION_IES = 2023;
    private const int NUMERO_MESES_ADICIONALES_PARA_VALIDACION = 1;
    private readonly IRepository<GuardarSaldosCarteraEntity, string> _saldosRepository = saldosRepository;
    private readonly IRepository<MbtControlDivisionSaldosEntity, string> _controlRepository = controlRepository;
    private readonly IDetallesCreditoServices _detallesCredito = detallesCredito;

    public async Task ObtenerSaldos(long idSolicitud)
    {
        var saldosSolicitudesCarteraActiva = await _detallesCredito.ObtenerSaldosPorIdSolicitud(idSolicitud);
        
        if (saldosSolicitudesCarteraActiva.Count > 0)
        {
            List<DesembolsoDTO>? desembolsos = await _detallesCredito.ObtenerDesembolsosPorIdSolicitud(idSolicitud);

            var puntos = desembolsos
                .OrderByDescending(desembolso => desembolso.Fecha)
                .Select(desembolso => desembolso.Puntos)
                .FirstOrDefault();

            var cantidadDesembolsosCuentaCobroNacion = desembolsos
                .Count(desembolso => desembolso.Marca!.Equals(TiposBeneficio.CUENTA_COBRO_NACION));

            var cantidadDesembolsosConAlertasEnFirme = desembolsos
                .Count(desembolso => desembolso.Marca!.Equals(TiposBeneficio.ALERTA) && desembolso.Estado!.Equals(Constants.ESTADO_DESEMBOLSO_EN_FIRME));

            var saldosConPeriodicidadNull = desembolsos.Where(desembolso => desembolso.Periodicidad == null && 
                                                                     (desembolso.Marca!.Equals(TiposBeneficio.NO_APLICA_BENEFICIO) ||  
                                                                     ((string.IsNullOrEmpty(desembolso.Marca) && desembolso.YearGiro >= 2024)))).Count() > 0;
            
            if (saldosConPeriodicidadNull)
            {
                var nuevoSaldosCartera = new List<SaldosCarteraDTO>();

                foreach (var saldo in saldosSolicitudesCarteraActiva)
                {
                    var estado = 1;
                    var tipoFallo = Constants.PERIODICIDAD_NULL;
                    await _actualizarDivision.ActualizarControlDivisionSaldos(estado, tipoFallo, saldo.Id);
                }
            }
            else if (!desembolsos.Any() )
            {        
                await CalcularSaldosCortoPlazoCuandoNoHayDesembolsosAsync(saldosSolicitudesCarteraActiva);

                await CalcularSaldosLargoPlazoCuandoNoHayDesembolsos(saldosSolicitudesCarteraActiva);

                await SubProductosSinDesembolsosQueNoHacenDivision(saldosSolicitudesCarteraActiva);

                await SubProductosSinDesembolsosQueHacenDivision(saldosSolicitudesCarteraActiva);                    
            }                
            else if (cantidadDesembolsosCuentaCobroNacion != 0 && puntos == 0)
            {
                int estado = 1;
                foreach(var saldo in saldosSolicitudesCarteraActiva)
                {
                    await _actualizarDivision.ActualizarControlDivisionSaldos(estado, TiposBeneficio.CUENTA_COBRO_NACION, saldo.Id);
                }                    
            }
            else if (cantidadDesembolsosConAlertasEnFirme != 0)
            {
                int estado = 1;
                foreach(var saldo in saldosSolicitudesCarteraActiva)
                {
                    await _actualizarDivision.ActualizarControlDivisionSaldos(estado, Constants.ESTADO_ALERTA_EN_FIRME, saldo.Id);
                } 
            }
            else
            {
                desembolsos = desembolsos.Where(s => s.TotalGirar > 0).ToList();

                ConvertirFechaSiNull(desembolsos);

                double sumaDesembolsosContribucionIES, sumaDesembolsosAportesIES, sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos, sumaDesembolsosSinBeneficiosPlanPagosFinalizado;

                SumarDesembolsos(desembolsos, out sumaDesembolsosContribucionIES, out sumaDesembolsosAportesIES, out sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos, out sumaDesembolsosSinBeneficiosPlanPagosFinalizado);

                foreach (var saldosSolicitudCarteraActiva in saldosSolicitudesCarteraActiva)
                {
                    if (saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.CORTO_PLAZO))
                    {
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosContribucionIES = sumaDesembolsosContribucionIES * saldosSolicitudCarteraActiva.PorcentajeCarteraPlan;
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosAportesIES = sumaDesembolsosAportesIES * saldosSolicitudCarteraActiva.PorcentajeCarteraPlan;
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosSinFinalizarPlanPagos = sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos * saldosSolicitudCarteraActiva.PorcentajeCarteraPlan;
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado = sumaDesembolsosSinBeneficiosPlanPagosFinalizado * saldosSolicitudCarteraActiva.PorcentajeCarteraPlan;
                        await CalcularSaldosPorSubproducto(saldosSolicitudCarteraActiva);
                    }
                    else if (saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.LARGO_PLAZO) ||
                           saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.LARGO_PLAZO_AMORTIZACION) ||
                           saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP_AMORTIZACION) ||
                           saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP))
                    {
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosContribucionIES = sumaDesembolsosContribucionIES * (PORCENTAJE_TOTAL_CARTERA_PLAN - saldosSolicitudCarteraActiva.PorcentajeCarteraPlan);
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosAportesIES = sumaDesembolsosAportesIES * (PORCENTAJE_TOTAL_CARTERA_PLAN - saldosSolicitudCarteraActiva.PorcentajeCarteraPlan);
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosSinFinalizarPlanPagos = sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos * (PORCENTAJE_TOTAL_CARTERA_PLAN - saldosSolicitudCarteraActiva.PorcentajeCarteraPlan);
                        saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado = sumaDesembolsosSinBeneficiosPlanPagosFinalizado * (PORCENTAJE_TOTAL_CARTERA_PLAN - saldosSolicitudCarteraActiva.PorcentajeCarteraPlan);
                        await CalcularSaldosPorSubproducto(saldosSolicitudCarteraActiva);
                    }
                    else if (saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.ORI_REEMBOLSABLE_RT) && saldosSolicitudCarteraActiva.TipoCartera.Equals(TiposCartera.AMORTIZACION))
                    {
                        var saldos = new List<SaldosCarteraDTO>();
                        var saldo = new SaldosCarteraDTO()
                        {
                            Saldo1 = saldosSolicitudCarteraActiva.SaldoCapitalVigente,
                            Saldo2 = 0,
                            Saldo3 = 0,
                            Saldo4 = 0,
                            IdSubproducto = saldosSolicitudCarteraActiva.IdSubproducto,
                            IdSaldosSolicitudCartera = saldosSolicitudCarteraActiva.Id,
                        };
                        saldos.Add(saldo);
                        await GuardarSaldos(saldos, $"{Constants.ORI_REEMBOLSABLE_RT} {TiposCartera.AMORTIZACION}", saldosSolicitudCarteraActiva.IdSolicitud, saldosSolicitudCarteraActiva.IdSubproducto);                            
                    }
                    else
                    {
                        var saldos = new List<SaldosCarteraDTO>();
                        var saldo = new SaldosCarteraDTO()
                        {
                            Saldo1 = 0,
                            Saldo2 = 0,
                            Saldo3 = 0,
                            Saldo4 = 0,
                            IdSubproducto = saldosSolicitudCarteraActiva.IdSubproducto,
                            IdSaldosSolicitudCartera = saldosSolicitudCarteraActiva.Id,
                        };
                        saldos.Add(saldo);
                        GenerarTipoFalloYId(saldosSolicitudCarteraActiva, out string tipoFallo, out int id);
                        await GuardarSaldos(saldos, tipoFallo, saldosSolicitudCarteraActiva.IdSolicitud, saldosSolicitudCarteraActiva.IdSubproducto);                            
                    }                                                  
                }                    
            }
        }
    }

    private void GenerarTipoFalloYId(SaldosSolicitudCarteraDTO saldosSolicitudCarteraActiva, out string tipoFallo, out int idFallo)
    {
        tipoFallo = saldosSolicitudCarteraActiva.CodigoSubproducto switch
        {
            TiposSubproducto.ALIANZAS_REEMBOLSABLE_LP_RA => Constants.ALIANZAS_REEMBOLSABLE_LP_RA,
            TiposSubproducto.ORI_CONDONABLE => Constants.ORI_CONDONABLE,
            TiposSubproducto.ORI_CONDONABLE_R_TERCEROS => Constants.ORI_CONDONABLE_R_TERCEROS,
            TiposSubproducto.ORI_REEMBOLSABLE_RT => Constants.ORI_REEMBOLSABLE_RT,
            _ => Constants.TIPO_BENEFICIO_DESCONOCIDO
        };

        idFallo = saldosSolicitudCarteraActiva.Id;
    }

    public static void SumarDesembolsos(List<DesembolsoDTO> desembolsos, out double sumaDesembolsosContribucionIES, out double sumaDesembolsosAportesIES, out double sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos, out double sumaDesembolsosSinBeneficiosPlanPagosFinalizado)
    {
        sumaDesembolsosContribucionIES = desembolsos
            .Where(desembolso => desembolso.Marca != null && desembolso.Marca.Equals(TiposBeneficio.CONTRIBUCION_IES) 
                && desembolso.Estado!.Equals(Constants.ESTADO_DESEMBOLSO_EN_FIRME))
            .Sum(desembolso => desembolso.TotalGirar);
        sumaDesembolsosAportesIES = desembolsos
            .Where(desembolso => desembolso.Marca != null  && desembolso.Marca.Equals(TiposBeneficio.APORTES_IES) 
                && desembolso.Estado!.Equals(Constants.ESTADO_DESEMBOLSO_EN_FIRME))
            .Sum(desembolso => desembolso.TotalGirar);
        sumaDesembolsosSinBeneficiosSinFinalizarPlanPagos = desembolsos
            .Where(desembolso => (string.IsNullOrEmpty(desembolso.Marca)
                                    || desembolso.Marca.Equals(TiposBeneficio.NO_APLICA_BENEFICIO))
                                    && desembolso.YearGiro > ANIO_CONTRIBUCION_IES
                                    && !FinalizoPlanPagos(desembolso.Fecha, desembolso.Periodicidad!.Value)
                                    && desembolso.Estado!.Equals(Constants.ESTADO_DESEMBOLSO_EN_FIRME))
            .Sum(desembolso => desembolso.TotalGirar);
        sumaDesembolsosSinBeneficiosPlanPagosFinalizado = desembolsos
            .Where(desembolso => (string.IsNullOrEmpty(desembolso.Marca)
                                    || desembolso.Marca.Equals(TiposBeneficio.NO_APLICA_BENEFICIO))
                                    && desembolso.YearGiro >= ANIO_CONTRIBUCION_IES
                                    && FinalizoPlanPagos(desembolso.Fecha, desembolso.Periodicidad!.Value)
                                    && desembolso.Estado!.Equals(Constants.ESTADO_DESEMBOLSO_EN_FIRME))
            .Sum(desembolso => desembolso.TotalGirar);
    }

    public virtual async Task CalcularSaldosLargoPlazoCuandoNoHayDesembolsos(List<SaldosSolicitudCarteraDTO> saldosSolicitudesCarteraActiva)
    {
        var saldosSolicitudCarteraActivaLargoPlazo = saldosSolicitudesCarteraActiva.Where(saldo => saldo.CodigoSubproducto.Equals(TiposSubproducto.LARGO_PLAZO) ||
                                                                                                    saldo.CodigoSubproducto.Equals(TiposSubproducto.LARGO_PLAZO_AMORTIZACION) ||
                                                                                                    saldo.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP_AMORTIZACION) ||
                                                                                                    saldo.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_LP_RP));

        if (saldosSolicitudCarteraActivaLargoPlazo.Any())
        {
            var nuevoSaldosCartera = new List<SaldosCarteraDTO>();
            foreach (var saldoEnLargoPlazo in saldosSolicitudCarteraActivaLargoPlazo)
            {
                nuevoSaldosCartera.Add(new SaldosCarteraDTO
                {
                    Saldo1 = saldoEnLargoPlazo.SaldoCapitalVigente,
                    Saldo2 = 0,
                    Saldo3 = 0,
                    Saldo4 = 0,
                    IdSolicitud = saldoEnLargoPlazo.IdSolicitud,
                    IdSubproducto = saldoEnLargoPlazo.IdSubproducto,
                    IdSaldosSolicitudCartera = saldoEnLargoPlazo.Id,
                    CodigoSubproducto = saldoEnLargoPlazo.CodigoSubproducto
                });
                var tipoFallo = Constants.ESTADO_EXITOSO;
                await GuardarSaldos(nuevoSaldosCartera, tipoFallo, saldoEnLargoPlazo.IdSolicitud, saldoEnLargoPlazo.IdSubproducto);                    
            }                
        }
    }

    public async Task SubProductosSinDesembolsosQueNoHacenDivision(List<SaldosSolicitudCarteraDTO> saldosSolicitudesCarteraActiva)
    {   
        var saldosSolicitudCarteraActivaOri = saldosSolicitudesCarteraActiva.Where(saldo => saldo.CodigoSubproducto.Equals(TiposSubproducto.ALIANZAS_REEMBOLSABLE_LP_RA) ||
                                                                                           saldo.CodigoSubproducto.Equals(TiposSubproducto.ORI_CONDONABLE) ||
                                                                                           saldo.CodigoSubproducto.Equals(TiposSubproducto.ORI_CONDONABLE_R_TERCEROS) ||
                                                                                           (saldo.CodigoSubproducto.Equals(TiposSubproducto.ORI_REEMBOLSABLE_RT) && saldo.TipoCartera.Equals(TiposCartera.ESTUDIOS)));

        if (saldosSolicitudCarteraActivaOri.Any())
        {
            var saldos = new List<SaldosCarteraDTO>();
            foreach (var saldoSolicitud in saldosSolicitudCarteraActivaOri)
            {
                saldos.Clear();
                var saldo = new SaldosCarteraDTO()
                {
                    Saldo1 = 0,
                    Saldo2 = 0,
                    Saldo3 = 0,
                    Saldo4 = 0,
                    IdSubproducto = saldoSolicitud.IdSubproducto,
                    IdSaldosSolicitudCartera = saldoSolicitud.Id,
                };
                saldos.Add(saldo);
                GenerarTipoFalloYId(saldoSolicitud, out string tipoFallo, out int id);
                await GuardarSaldos(saldos, tipoFallo, saldoSolicitud.IdSolicitud, saldoSolicitud.IdSubproducto);                    
            }
        }
    }

    private async Task SubProductosSinDesembolsosQueHacenDivision(List<SaldosSolicitudCarteraDTO> saldosSolicitudesCarteraActiva)
    {
        var saldosSolicitudCarteraActivaOri = saldosSolicitudesCarteraActiva.Where(saldo => saldo.CodigoSubproducto.Equals(TiposSubproducto.ORI_REEMBOLSABLE_RT) && saldo.TipoCartera.Equals(TiposCartera.AMORTIZACION));

        if (saldosSolicitudCarteraActivaOri.Any())
        {
            var saldos = new List<SaldosCarteraDTO>();

            foreach (var saldoSolicitud in saldosSolicitudCarteraActivaOri)
            {
                var saldo = new SaldosCarteraDTO()
                {
                    Saldo1 = saldoSolicitud.SaldoCapitalVigente,
                    Saldo2 = 0,
                    Saldo3 = 0,
                    Saldo4 = 0,
                    IdSubproducto = saldoSolicitud.IdSubproducto,
                    IdSaldosSolicitudCartera = saldoSolicitud.Id,
                };
                saldos.Add(saldo);   
                GenerarTipoFalloYId(saldoSolicitud, out string tipoFallo, out int id);
                await GuardarSaldos(saldos, tipoFallo, saldoSolicitud.IdSolicitud, saldoSolicitud.IdSubproducto);                    
            }
        }
    }

    public async Task CalcularSaldosCortoPlazoCuandoNoHayDesembolsosAsync(List<SaldosSolicitudCarteraDTO> saldosSolicitudesCarteraActiva)
    {
        var nuevoSaldosCartera = new List<SaldosCarteraDTO>();
        var saldosSolicitudCarteraActivaCortoPlazo = saldosSolicitudesCarteraActiva
                                .FirstOrDefault(saldosSolicitudCarteraActiva => saldosSolicitudCarteraActiva.CodigoSubproducto.Equals(TiposSubproducto.CORTO_PLAZO));

        if (saldosSolicitudCarteraActivaCortoPlazo != null)
        {
            nuevoSaldosCartera.Add(new SaldosCarteraDTO
            {
                Saldo1 = saldosSolicitudCarteraActivaCortoPlazo.SaldoCapitalVigente,
                Saldo2 = 0,
                Saldo3 = 0,
                Saldo4 = 0,
                IdSolicitud = saldosSolicitudCarteraActivaCortoPlazo.IdSolicitud,
                IdSubproducto = saldosSolicitudCarteraActivaCortoPlazo.IdSubproducto,
                IdSaldosSolicitudCartera = saldosSolicitudCarteraActivaCortoPlazo.Id,
                CodigoSubproducto = saldosSolicitudCarteraActivaCortoPlazo.CodigoSubproducto
            });
            var tipoFallo = Constants.ESTADO_EXITOSO;
            await GuardarSaldos(nuevoSaldosCartera, tipoFallo, saldosSolicitudCarteraActivaCortoPlazo.IdSolicitud, saldosSolicitudCarteraActivaCortoPlazo.IdSubproducto);                
        }
    }

    public static void ConvertirFechaSiNull(List<DesembolsoDTO> desembolsos)
    {
        desembolsos.Where(obj => obj.Fecha == null).ToList().ForEach(obj =>
        {
            if (obj.SemestreGiro == 1)
            {
                obj.Fecha = new DateTime(obj.YearGiro, 1, 1);
            }
            else
            {
                obj.Fecha = new DateTime(obj.YearGiro, 7, 1);
            }
        });
    }

    public static void ValidarDesembolsosSolicitud(List<DesembolsoDTO> desembolsos)
    {
        var desembolsosSolicitudValidator = new DesembolsoValidator();

        foreach (var desembolso in desembolsos)
        {
            desembolsosSolicitudValidator.ValidateAndThrowCustomValidationException(desembolso);
        }
    }

    private static bool FinalizoPlanPagos(DateTime? fechaGiro, int periodicidad)
    {
        var fechaGiroMasPeriocidad = fechaGiro!.Value.AddMonths(periodicidad + NUMERO_MESES_ADICIONALES_PARA_VALIDACION);
        var fecha = DateTime.Now >= fechaGiroMasPeriocidad;
        return fecha;
    }

    private async Task CalcularSaldosPorSubproducto(SaldosSolicitudCarteraDTO saldosSolicitudCarteraActiva)
    {
        var saldosCartera = new List<SaldosCarteraDTO>();
        saldosCartera.Add(CalcularSaldosCartera(saldosSolicitudCarteraActiva));
        var tipoFallo = Constants.ESTADO_EXITOSO;
        await GuardarSaldos(saldosCartera, tipoFallo, saldosSolicitudCarteraActiva.IdSolicitud, saldosSolicitudCarteraActiva.IdSubproducto);             
    }

    public static SaldosCarteraDTO CalcularSaldosCartera(SaldosSolicitudCarteraDTO saldosSolicitudCarteraActiva)
    {
        var saldosCartera = new SaldosCarteraDTO();

        if (saldosSolicitudCarteraActiva.SaldoCapitalVigente == 0)
        {
            saldosCartera = CalcularSaldosACero(saldosCartera);
        }
        else
        {
            if (saldosSolicitudCarteraActiva.TipoCartera.Equals(TiposCartera.AMORTIZACION))
            {
                saldosCartera = CalcularSaldosEtapaAmortizacion(saldosCartera, saldosSolicitudCarteraActiva);
            }
            else
            {
                var saldoCapitalVigenteRestante = saldosSolicitudCarteraActiva.SaldoCapitalVigente;

                (saldosCartera.Saldo4, saldoCapitalVigenteRestante) = CalcularSaldo4(saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosSinFinalizarPlanPagos,
                                                    saldoCapitalVigenteRestante,
                                                    saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado,
                                                    saldosSolicitudCarteraActiva.SumatoriaDesembolsosAportesIES,
                                                    saldosSolicitudCarteraActiva.SumatoriaDesembolsosContribucionIES);
                (saldosCartera.Saldo3, saldoCapitalVigenteRestante) = CalcularSaldo(saldosSolicitudCarteraActiva.SumatoriaDesembolsosAportesIES, saldoCapitalVigenteRestante);
                (saldosCartera.Saldo2, saldoCapitalVigenteRestante) = CalcularSaldo(saldosSolicitudCarteraActiva.SumatoriaDesembolsosContribucionIES, saldoCapitalVigenteRestante);
                var sumatoriaSaldos = saldosCartera.Saldo4 + saldosCartera.Saldo3 + saldosCartera.Saldo2;
                saldosCartera.Saldo1 = saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado > 0 ?
                                       CalcularSaldo1CuandoExisteDesembolsosSinBeneficiosPlanPagosFinalizado(
                                           saldoCapitalVigenteRestante,
                                           saldosSolicitudCarteraActiva.SaldoCapitalVigente,
                                           saldosSolicitudCarteraActiva.SumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado,
                                           saldosSolicitudCarteraActiva.SumatoriaDesembolsosAportesIES,
                                           sumatoriaSaldos) : 
                                       CalcularSaldo1(saldoCapitalVigenteRestante);
            }
        }

        saldosCartera.IdSolicitud = saldosSolicitudCarteraActiva.IdSolicitud;
        saldosCartera.IdSubproducto = saldosSolicitudCarteraActiva.IdSubproducto;
        saldosCartera.CodigoSubproducto = saldosSolicitudCarteraActiva.CodigoSubproducto;
        saldosCartera.IdSaldosSolicitudCartera = saldosSolicitudCarteraActiva.Id;

        return saldosCartera;
    }

    private static SaldosCarteraDTO CalcularSaldosACero(SaldosCarteraDTO saldosCartera)
    {
        saldosCartera.Saldo1 = 0;
        saldosCartera.Saldo2 = 0;
        saldosCartera.Saldo3 = 0;
        saldosCartera.Saldo4 = 0;

        return saldosCartera;
    }

    private static SaldosCarteraDTO CalcularSaldosEtapaAmortizacion(SaldosCarteraDTO saldosCartera, SaldosSolicitudCarteraDTO saldosSolicitudCartera)
    {
        saldosCartera.Saldo1 = saldosSolicitudCartera.SaldoCapitalVigente;
        saldosCartera.Saldo2 = 0;
        saldosCartera.Saldo3 = 0;
        saldosCartera.Saldo4 = 0;

        return saldosCartera;
    }

    private static (double, double) CalcularSaldo4(double sumatoriaDesembolsos, double saldoCapitalVigenteRestante, double sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado, double sumatoriaDesembolsosAportesIES, double sumatoriaDesembolsosContribucionIES)
    {
        var nuevoSaldoCapitalVigente = 0.0;
        if (sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado > 0)
        {
            nuevoSaldoCapitalVigente = saldoCapitalVigenteRestante - sumatoriaDesembolsos;

            if (sumatoriaDesembolsos > 0 && nuevoSaldoCapitalVigente > 0)
            {
                if (nuevoSaldoCapitalVigente <= 0)
                {
                    return (0, saldoCapitalVigenteRestante);
                }

                if (sumatoriaDesembolsos > 0)
                {
                    return (sumatoriaDesembolsos, nuevoSaldoCapitalVigente);
                }

                return (0, saldoCapitalVigenteRestante);
            }

            if ((sumatoriaDesembolsosAportesIES > 0 || sumatoriaDesembolsosContribucionIES > 0) && sumatoriaDesembolsos < saldoCapitalVigenteRestante)
            {
                if (sumatoriaDesembolsos > 0)
                {
                    var capitalVigeneteSinSumatoriaDesembolso = saldoCapitalVigenteRestante - sumatoriaDesembolsos;

                    return (sumatoriaDesembolsos, capitalVigeneteSinSumatoriaDesembolso);
                }

                return (0, saldoCapitalVigenteRestante);
            }

            if (sumatoriaDesembolsos <= saldoCapitalVigenteRestante)
            {
                return (sumatoriaDesembolsos, nuevoSaldoCapitalVigente);
            }

            if (nuevoSaldoCapitalVigente > 0)
            {
                return (0, nuevoSaldoCapitalVigente);
            }

            return (saldoCapitalVigenteRestante, nuevoSaldoCapitalVigente);
        }

        if (sumatoriaDesembolsos == 0 || saldoCapitalVigenteRestante <= 0)
        {
            return (0, saldoCapitalVigenteRestante);
        }

        nuevoSaldoCapitalVigente = saldoCapitalVigenteRestante - sumatoriaDesembolsos;

        if (nuevoSaldoCapitalVigente <= 0)
        {
            return (saldoCapitalVigenteRestante, nuevoSaldoCapitalVigente);
        }

        return (sumatoriaDesembolsos, nuevoSaldoCapitalVigente);
    }

    private static (double, double) CalcularSaldo(double sumatoriaDesembolsos, double saldoCapitalVigenteRestante)
    {
        if (sumatoriaDesembolsos == 0 || saldoCapitalVigenteRestante <= 0)
        {

            return (0, saldoCapitalVigenteRestante);
        }

        var nuevoSaldoCapitalVigente = saldoCapitalVigenteRestante - sumatoriaDesembolsos;

        if (nuevoSaldoCapitalVigente <= 0)
        {
            return (saldoCapitalVigenteRestante, nuevoSaldoCapitalVigente);
        }

        return (sumatoriaDesembolsos, nuevoSaldoCapitalVigente);
    }

    private static double CalcularSaldo1CuandoExisteDesembolsosSinBeneficiosPlanPagosFinalizado(double saldoCapitalVigenteRestante, double saldoCapitalVigente, double sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado, double SumatoriaDesembolsosAportesIES, double sumatoriaSaldos)
    {
        var saldoCapitalNuevo = saldoCapitalVigente - sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado;

        var restantenteSaldoCapitalVigente = saldoCapitalVigente - sumatoriaSaldos;

        if (saldoCapitalNuevo < 0 && saldoCapitalVigente < sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado && saldoCapitalVigenteRestante < 0
            && sumatoriaSaldos < saldoCapitalVigente)
        {
            return restantenteSaldoCapitalVigente;
        }

        if (sumatoriaSaldos == saldoCapitalVigente)
        {
            return 0;
        }

        if (saldoCapitalNuevo <= 0 && sumatoriaSaldos == saldoCapitalVigente)
        {
            return 0;
        }
        if (SumatoriaDesembolsosAportesIES > 0 && saldoCapitalVigenteRestante < 0)
        {
            return 0;
        }

        if (SumatoriaDesembolsosAportesIES > 0 && saldoCapitalVigenteRestante > 0)
        {
            return saldoCapitalVigenteRestante;
        }
        if (saldoCapitalVigenteRestante < 0 && sumatoriaSaldos == 0 )
        {
           return sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado;
        }
        
        if (sumatoriaSaldos < saldoCapitalVigente)
        {
            return restantenteSaldoCapitalVigente;
        }

        return sumatoriaDesembolsosSinBeneficiosPlanPagosFinalizado + saldoCapitalVigenteRestante;
    }

    private static double CalcularSaldo1(double saldoCapitalVigenteRestante)
    {
        if (saldoCapitalVigenteRestante <= 0)
        {
            return 0;
        }

        return saldoCapitalVigenteRestante;
    }

    public async Task ActualizarControlDivision(string tipoFallo, long idSolicitud, GuardarSaldosCarteraEntity saldosCartera)
    {
        int estado = 1;
        int? id = saldosCartera.IdSaldosSolicitudCartera;
        await _controlRepository.UpdateAsync(new MbtControlDivisionSaldosEntity
        {
            IdSaldosSolicitudCartera = id,
            IdSolicitud = idSolicitud,
            Estado_Division = estado,
            Tipo_Fallo_Division = tipoFallo
        }, "IDSALDOSSOLICITUDCARTERA", id.ToString()!);
    }

    public GuardarSaldosCarteraEntity MapearSaldosCartera(SaldosCarteraDTO saldosCartera)
    {
        return new GuardarSaldosCarteraEntity
        {
            Saldo1 = saldosCartera.Saldo1,
            Saldo2 = saldosCartera.Saldo2,
            Saldo3 = saldosCartera.Saldo3,
            Saldo4 = saldosCartera.Saldo4,
            IdSubproducto = saldosCartera.IdSubproducto,
            IdMovimiento = saldosCartera.IdMovimiento,
            IdSaldosSolicitudCartera = saldosCartera.IdSaldosSolicitudCartera
        };
    }

    public async Task GuardarSaldos(List<SaldosCarteraDTO> saldosCarteras, string tipoFallo, long idSolicitud, int idSubproducto) 
    {
        foreach (SaldosCarteraDTO saldosCarteraDTO in saldosCarteras)
        {
            GuardarSaldosCarteraEntity? saldosCartera = MapearSaldosCartera(saldosCarteraDTO);
            await _saldosRepository.CreateAsync(saldosCartera, DatabaseType.Oracle);
            await _calcularTasasCarteraService.CalcularTasasYProporcionSaldos(idSolicitud, idSubproducto, Constants.FECHA_INICIAL_PROCESO_WORKER);
            await ActualizarControlDivision(tipoFallo, idSolicitud, saldosCartera);
        }
    }    
}