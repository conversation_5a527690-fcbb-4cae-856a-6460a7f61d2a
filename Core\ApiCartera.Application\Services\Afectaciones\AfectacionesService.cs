﻿using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesService : IAfectacionesService
    {
        public SaldosCarteraDTO DisminuirSaldosProgresivamente(SaldosCarteraDTO saldosCartera, double valorAfectacion)
        {
            double[] saldos = [saldosCartera.Saldo1, saldosCartera.Saldo2, saldosCartera.Saldo3, saldosCartera.Saldo4];

            for (int i = 0; i < saldos.Length; i++)
            {
                if (valorAfectacion == 0) break;

                if (valorAfectacion >= saldos[i])
                {
                    valorAfectacion -= saldos[i];
                    saldos[i] = 0;
                }
                else
                {
                    saldos[i] -= valorAfectacion;
                    valorAfectacion = 0;
                }
            }

            saldosCartera.Saldo1 = saldos[0];
            saldosCartera.Saldo2 = saldos[1];
            saldosCartera.Saldo3 = saldos[2];
            saldosCartera.Saldo4 = saldos[3];

            return saldosCartera;
        }

        public SaldosCarteraDTO AumentarSaldo1(SaldosCarteraDTO saldosCartera, double valorAfectacion)
        {
            if (valorAfectacion != 0) {
                saldosCartera.Saldo1 = saldosCartera.Saldo1 + valorAfectacion;
                saldosCartera.Saldo2 = saldosCartera.Saldo2;
                saldosCartera.Saldo3 = saldosCartera.Saldo3;
                saldosCartera.Saldo4 = saldosCartera.Saldo4;
            }
            
            return saldosCartera;
        }

        public SaldosCarteraDTO ConfigurarSaldosACero(SaldosCarteraDTO saldosCartera)
        {
            saldosCartera.Saldo1 = 0;
            saldosCartera.Saldo2 = 0;
            saldosCartera.Saldo3 = 0;
            saldosCartera.Saldo4 = 0;

            return saldosCartera;
        }

        public SaldosCartera MapearSaldosCartera(SaldosCarteraDTO saldosCartera)
        {
            return new SaldosCartera
            {
                Saldo1 = saldosCartera.Saldo1,
                Saldo2 = saldosCartera.Saldo2,
                Saldo3 = saldosCartera.Saldo3,
                Saldo4 = saldosCartera.Saldo4,
                IdSubproducto = saldosCartera.IdSubproducto,
                IdMovimiento = saldosCartera.IdMovimiento,
                IdSaldosSolicitudCartera = saldosCartera.IdSaldosSolicitudCartera
            };
        }

        public SaldosCartera MapearSaldosCarteraNuevosCreditos(SaldosCarteraDTO saldosCartera)
        {
            return new SaldosCartera
            {
                Saldo1 = saldosCartera.Saldo1,
                Saldo2 = saldosCartera.Saldo2,
                Saldo3 = saldosCartera.Saldo3,
                Saldo4 = saldosCartera.Saldo4,
                IdSubproducto = saldosCartera.IdSubproducto,
                IdMovimiento = saldosCartera.IdMovimiento
            };
        }

        public SaldosCartera MapearSaldosCarteraAfectaciones(SaldosCarteraDTO saldosCartera)
        {
            return new SaldosCartera
            {
                Saldo1 = saldosCartera.Saldo1,
                Saldo2 = saldosCartera.Saldo2,
                Saldo3 = saldosCartera.Saldo3,
                Saldo4 = saldosCartera.Saldo4,
                IdSubproducto = saldosCartera.IdSubproducto,
                IdMovimiento = saldosCartera.IdMovimiento,
                IdSaldosSolicitudCartera = saldosCartera.IdSaldosSolicitudCartera,
                IdSaldosCarteraAnt = saldosCartera.IdSaldosCarteraAnt
            };
        }

        public static void ValidarSaldosCartera(SaldosCarteraDTO saldosCartera, long idSolicitud)
        {
            if (saldosCartera == null)
            {
                throw new ValidationException(
                [
                    new() 
                    {
                        PropertyName = nameof(idSolicitud),
                        ErrorMessage = $"{ValidationMessages.SaldosCartera} {idSolicitud}"
                    }
                ]);
            }
            var saldosCarteraValidador = new SaldosCarteraValidator();
            saldosCarteraValidador.ValidateAndThrowCustomValidationException(saldosCartera);
        }

        public static void ValidarMovimiento(MovimientoDTO movimiento) 
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new()
                    {
                        PropertyName = nameof(movimiento.IdSolicitud),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdSolicitud}"
                    }
                ]);
            }
            var movimientoValidator = new MovimientoValidator();
            movimientoValidator.ValidateAndThrowCustomValidationException(movimiento);
        }
    }
}
