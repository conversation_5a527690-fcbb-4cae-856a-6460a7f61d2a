﻿using ApiCartera.Application.Contracts.Persistence.Oracle;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Registros.DTOs;
using Oracle.ManagedDataAccess.Client;
using System.Data;

namespace ApiCartera.Infrastructure.Persitence.Oracle
{
    public class OracleRepository : IOracleRepository
    {
        public async Task<ResultadoEjecucion<long>> InsertarSaldosMasivamente(string connectionString, List<SaldosSolicitudCartera> registros)
        {
            using var connection = new OracleConnection(connectionString);
            const int batchSize = 5000;
            int totalRecords = registros.Count;
            int processedRecords = 0;
            var resultado = new ResultadoEjecucion<long>()
            {
                Total = totalRecords
            };

            try
            {
                await connection.OpenAsync();
                int totalRegistrosExistentes = 0;
                int totalCount = 0;

                while (processedRecords < totalRecords)
                {
                    var batchRecords = registros.Skip(processedRecords).Take(batchSize).ToList();
                    processedRecords += batchRecords.Count;

                    using var transaction = await connection.BeginTransactionAsync();
                    int batchCount = batchRecords.Count;

                    var idSolicitudArray = new long[batchCount];
                    var idSignatureArray = new long[batchCount];
                    var saldoCapitalVigenteArray = new double[batchCount];
                    var saldoTotalCapitalArray = new double[batchCount];
                    var saldoCapitalVencidoArray = new double[batchCount];
                    var idSubproductoArray = new int[batchCount];
                    var idTipoCarteraArray = new int[batchCount];
                    var cuotasPendientesPagarArray = new int[batchCount];
                    var fechaFinalizacionPlanPagosArray = new DateTime?[batchCount];

                    for (int i = 0; i < batchCount; i++)
                    {
                        var registro = batchRecords[i];
                        idSolicitudArray[i] = registro.IdSolicitud;
                        idSignatureArray[i] = registro.IdSignature;
                        saldoCapitalVigenteArray[i] = registro.SaldoCapitalVigente;
                        saldoTotalCapitalArray[i] = registro.SaldoTotalCapital;
                        saldoCapitalVencidoArray[i] = registro.SaldoCapitalVencido;
                        idSubproductoArray[i] = registro.IdSubproducto;
                        idTipoCarteraArray[i] = registro.IdTipoCartera;
                        cuotasPendientesPagarArray[i] = registro.CuotasPendientesPagar;
                        fechaFinalizacionPlanPagosArray[i] = registro.FechaFinalizacionPlanPagos;
                    }

                    using var command = connection.CreateCommand();
                    command.Transaction = (OracleTransaction)transaction;

                    command.CommandText = @"
                        INSERT INTO MBT_SALDOS_SOLICITUD_CARTERA
                        (IDSOLICITUD, IDSIGNATURE, SALDOCAPITALVIGENTE, SALDOTOTALCAPITAL, SALDOCAPITALVENCIDO, IDSUBPRODUCTO, IDTIPOCARTERA, CUOTASPENDIENTESPAGAR, FECHAFINALIZACIONPLANPAGOS, FECHAREGISTRO)                       SELECT :IdSolicitud, :IdSignature, :SaldoCapitalVigente, :SaldoTotalCapital, :SaldoCapitalVencido, :IdSubproducto, :IdTipoCartera, :CuotasPendientesPagar, :FechaFinalizacionPlanPagos, SYSDATE
                        FROM DUAL
                        WHERE NOT EXISTS (
                            SELECT 1
                            FROM MBT_SALDOS_SOLICITUD_CARTERA
                            WHERE IDSOLICITUD = :IdSolicitud 
                                AND IDSIGNATURE = :IdSignature
                                AND SALDOCAPITALVIGENTE = :SaldoCapitalVigente
                                AND SALDOTOTALCAPITAL = :SaldoTotalCapital
                                AND SALDOCAPITALVENCIDO = :SaldoCapitalVencido
                                AND IDSUBPRODUCTO = :IdSubproducto
                                AND IDTIPOCARTERA = :IdTipoCartera
                                AND CUOTASPENDIENTESPAGAR = :CuotasPendientesPagar
                                AND ((FECHAFINALIZACIONPLANPAGOS = :FechaFinalizacionPlanPagos) OR (FECHAFINALIZACIONPLANPAGOS IS NULL AND :FechaFinalizacionPlanPagos IS NULL))
                    )";

                    command.CommandType = CommandType.Text;
                    command.BindByName = true;
                    command.ArrayBindCount = batchCount;

                    command.Parameters.Add("IdSolicitud", OracleDbType.Int64, idSolicitudArray, ParameterDirection.Input);
                    command.Parameters.Add("IdSignature", OracleDbType.Int64, idSignatureArray, ParameterDirection.Input);
                    command.Parameters.Add("SaldoCapitalVigente", OracleDbType.Decimal, saldoCapitalVigenteArray, ParameterDirection.Input);
                    command.Parameters.Add("SaldoTotalCapital", OracleDbType.Decimal, saldoTotalCapitalArray, ParameterDirection.Input);
                    command.Parameters.Add("SaldoCapitalVencido", OracleDbType.Decimal, saldoCapitalVencidoArray, ParameterDirection.Input);
                    command.Parameters.Add("IdSubproducto", OracleDbType.Int32, idSubproductoArray, ParameterDirection.Input);
                    command.Parameters.Add("IdTipoCartera", OracleDbType.Int32, idTipoCarteraArray, ParameterDirection.Input);
                    command.Parameters.Add("CuotasPendientesPagar", OracleDbType.Int32, cuotasPendientesPagarArray, ParameterDirection.Input);
                    command.Parameters.Add("FechaFinalizacionPlanPagos", OracleDbType.Date, fechaFinalizacionPlanPagosArray, ParameterDirection.Input);

                    try
                    {
                        int rowsAffected = await command.ExecuteNonQueryAsync();

                        totalCount += rowsAffected;
                        await transaction.CommitAsync();

                        Console.WriteLine($"Registros procesados en el lote = {rowsAffected} registros");
                    }
                    catch (OracleException ex)
                    {
                        transaction.Rollback();
                        Console.WriteLine($"Error al procesar el lote: {ex.Message}");
                    }
                }
                Console.WriteLine($"Registros totales insertados = {totalCount} registros");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error: {ex.Message}");
            }
            finally
            {
                await connection.CloseAsync();
            }

            return resultado;
        }        

        public async Task<ResultadoEjecucion<MovimientosDiariosDTO>> InsertarMovimientosMasivamente(string connectionString, List<MovimientosDiariosDTO> registros)
        {
            using var connection = new OracleConnection(connectionString);
            const int batchSize = 5000;
            int totalRecords = registros.Count;
            int processedRecords = 0;
            var resultado = new ResultadoEjecucion<MovimientosDiariosDTO>()
            {
                Total = totalRecords
            };

            try
            {
                await connection.OpenAsync();

                using var transaction = await connection.BeginTransactionAsync();
                while (processedRecords < totalRecords)
                {
                    var bathRecords = registros.Skip(processedRecords).Take(batchSize).ToList();
                    processedRecords += bathRecords.Count;

                    using var command = new OracleCommand();
                    command.Connection = connection;
                    command.Transaction = (OracleTransaction)transaction;
                    int count = 0;

                    foreach (var registro in bathRecords)
                    {
                        try
                        {
                            command.CommandText = @"
                            INSERT INTO MBT_MOVIMIENTODIARIO 
                                (IDMOVIMIENTODIARIO, IDICETEX, IDSIGNATURE, SDOTOTALCAPITALVIGENTE, FECHAEFECTIVAMOVIMIENTO, FECHAPOSTEO, 
                                 MONTOMOVIMIENTODECAPITAL, CODIGOTIPOMOVTODECAPITAL, NOMBRETIPOMOVTODECAPITAL, 
                                 CODSUBPRODUCTOXOBLIGACION, SALDOTOTALDECAPITAL, SALDOCAPITALVENCIDO, 
                                 DESCRIPCIONMOVTOMEMO, CODNOVEDAD, TIPOCARTERA, CUOTASPENDIENTESPORPAGAR, 
                                 DESCRIPCIONNOVACION, REFERENCIACUS, YEAR, SEMESTRE, NOMBREARCHIVO, 
                                 FECHAEJECUCION, CONTROL_MOVIMIENTO)
                            SELECT 
                                SEQ_MBT_MOVIMIENTODIARIO.NEXTVAL, :IdSolicitud, :IdSignature, :SaldoCapitalVigente, :FechaMovimiento, :FechaPosteo, 
                                :MontoMovimientoDeCapital, :CodigoTipoMovimientoDeCapital, :NombreTipoMovimientoDeCapital, 
                                :CodigoSubproductoPorObligacion, :SaldoTotalCapital, :SaldoCapitalVencido, 
                                :Descripcion, :CodigoNovedad, :TipoCartera, :CuotasPendientesPorPagar, 
                                :DescripcionNovacion, :ReferenciaCus, :Year, :Semestre, :NombreArchivo, 
                                :FechaEjecucion, :ControlMovimiento
                            FROM DUAL
                            WHERE 
                                NOT EXISTS (
                                    SELECT 1
                                    FROM MBT_MOVIMIENTODIARIO M
                                    WHERE M.IDICETEX = :IdSolicitud
                                    AND M.IDSIGNATURE = :IdSignature                                    
                                    AND ABS(M.SDOTOTALCAPITALVIGENTE - :SaldoCapitalVigente) < 0.001
                                    AND M.FECHAEFECTIVAMOVIMIENTO = :FechaMovimiento
                                    AND M.FECHAPOSTEO = :FechaPosteo
                                    AND ABS(M.MONTOMOVIMIENTODECAPITAL - :MontoMovimientoDeCapital) < 0.001
                                    AND M.CODIGOTIPOMOVTODECAPITAL = :CodigoTipoMovimientoDeCapital
                                    AND M.NOMBRETIPOMOVTODECAPITAL = :NombreTipoMovimientoDeCapital
                                    AND M.CODSUBPRODUCTOXOBLIGACION = :CodigoSubproductoPorObligacion
                                    AND ABS(M.SALDOTOTALDECAPITAL - :SaldoTotalCapital) < 0.001
                                    AND ABS(M.SALDOCAPITALVENCIDO - :SaldoCapitalVencido) < 0.001
                                    AND M.DESCRIPCIONMOVTOMEMO = :Descripcion
                                    AND M.CODNOVEDAD = :CodigoNovedad 
                                    AND M.TIPOCARTERA = :TipoCartera
                                    AND M.CUOTASPENDIENTESPORPAGAR = :CuotasPendientesPorPagar
                                    AND NVL(M.DESCRIPCIONNOVACION, ' ') = NVL(:DescripcionNovacion, ' ')
                                    AND NVL(M.REFERENCIACUS, ' ') = NVL(:ReferenciaCus, ' ')
                                    AND M.YEAR = :Year
                                    AND M.SEMESTRE = :Semestre
                                    AND M.NOMBREARCHIVO = :NombreArchivo)
                             ";

                            command.CommandType = CommandType.Text;
                            command.Parameters.Clear();
                            AgregarParametrosMovimientos(command, registro);
                            int rowsAffected = await command.ExecuteNonQueryAsync();

                            if (rowsAffected == 1)
                            {
                                count++;
                                resultado.Exitosos.Add(registro);
                            }                                
                            else
                                Console.WriteLine($"IDICETEX: {registro.IdIcetex}- CODSUBPRODUCTOXOBLIGACION: {registro.CodSubproductoXObligacion}- CODNOVEDAD: {registro.CodNovedad} - FECHA: {registro.FechaEjecucion} ya existe");                                                        
                        }
                        catch
                        {
                            resultado.Fallidos.Add(registro);
                        }
                    }
                    Console.WriteLine($"registros procesados = {count} registros ({processedRecords})");
                }

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Ocurrió un error: {ex.Message}");
            }
            finally
            {
                connection?.CloseAsync();
            }

            return resultado;
        }
        
        private static void AgregarParametrosMovimientos(OracleCommand command, MovimientosDiariosDTO registro)
        {
            command.Parameters.Add(new OracleParameter("IdSolicitud", OracleDbType.Int64)).Value = registro.IdIcetex;
            command.Parameters.Add(new OracleParameter("IdSignature", OracleDbType.Int64)).Value = registro.IdSignature;
            command.Parameters.Add(new OracleParameter("SaldoCapitalVigente", OracleDbType.Decimal)).Value = registro.SdoTotalCapitalVigente;
            command.Parameters.Add(new OracleParameter("FechaMovimiento", OracleDbType.Date)).Value = registro.FechaEfectivaMovimiento;
            command.Parameters.Add(new OracleParameter("FechaPosteo", OracleDbType.Date)).Value = registro.FechaPosteo;
            command.Parameters.Add(new OracleParameter("MontoMovimientoDeCapital", OracleDbType.Decimal)).Value = registro.MontoMovimientoDeCapital;
            command.Parameters.Add(new OracleParameter("CodigoTipoMovimientoDeCapital", OracleDbType.Int32)).Value = registro.CodigoTipoMovtoDeCapital;
            command.Parameters.Add(new OracleParameter("NombreTipoMovimientoDeCapital", OracleDbType.Varchar2)).Value = registro.NombreTipoMovtoDeCapital;
            command.Parameters.Add(new OracleParameter("CodigoSubproductoPorObligacion", OracleDbType.Varchar2)).Value = registro.CodSubproductoXObligacion;
            command.Parameters.Add(new OracleParameter("SaldoTotalCapital", OracleDbType.Decimal)).Value = registro.SaldoTotalDeCapital;
            command.Parameters.Add(new OracleParameter("SaldoCapitalVencido", OracleDbType.Decimal)).Value = registro.SaldoCapitalVencido;
            command.Parameters.Add(new OracleParameter("Descripcion", OracleDbType.Varchar2)).Value = registro.DescripcionMovtoMemo;
            command.Parameters.Add(new OracleParameter("CodigoNovedad", OracleDbType.Varchar2)).Value = registro.CodNovedad;
            command.Parameters.Add(new OracleParameter("TipoCartera", OracleDbType.Varchar2)).Value = registro.TipoCartera;
            command.Parameters.Add(new OracleParameter("CuotasPendientesPorPagar", OracleDbType.Int32)).Value = registro.CuotasPendientesPorPagar;
            command.Parameters.Add(new OracleParameter("DescripcionNovacion", OracleDbType.Varchar2)).Value = registro.DescripcionNovacion;
            command.Parameters.Add(new OracleParameter("ReferenciaCus", OracleDbType.Varchar2)).Value = registro.ReferenciaCus;
            command.Parameters.Add(new OracleParameter("Year", OracleDbType.Int32)).Value = registro.Year;
            command.Parameters.Add(new OracleParameter("Semestre", OracleDbType.Int32)).Value = registro.Semestre;
            command.Parameters.Add(new OracleParameter("NombreArchivo", OracleDbType.Varchar2)).Value = registro.NombreArchivo;
            command.Parameters.Add(new OracleParameter("FechaEjecucion", OracleDbType.Date)).Value = registro.FechaEjecucion;
            command.Parameters.Add(new OracleParameter("ControlMovimiento", OracleDbType.Int32)).Value = registro.ControlMovimiento;

        }

        public async Task<List<long>> ObtenerSolicitudesParaDivisionDeSaldos(string connectionString)
        {
            var solicitudes = new List<long>();

            using var connection = new OracleConnection(connectionString);

            try
            {
                await connection.OpenAsync();

                var commandText = @"
                    SELECT  
                        DISTINCT IDSOLICITUD
                    FROM MBT_CONTROL_DIVISION_SALDOS
                    WHERE ESTADO_DIVISION = 0 
                    ";

                using var command = new OracleCommand(commandText, connection)
                {
                    CommandType = CommandType.Text,
                };

                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var idSolicitud = reader.GetInt64(0);
                    solicitudes.Add(idSolicitud);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error al obtener las solicitudes de mbt_control_division_saldos: {ex.Message}");
                throw;
            }

            return solicitudes;
        }               
    }
}
