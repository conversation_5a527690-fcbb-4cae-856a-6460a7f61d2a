﻿using ApiCartera.Domain.Features.Afectaciones.Constants;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.Shared.Constants;

namespace ApiCarteraTests.PruebasUnitarias.Config
{
    public class TestData
    {
        public static IEnumerable<object[]> ObtenerMovimientosReintegros()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Parse("2023-02-02"),
                ValorGiro = 350000,
                Descripcion = "Reintegro a capital",
                CodigoNovedad = "10",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                NoRelacion = "1"
            }};

            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Parse("2023-06-03"),
                ValorGiro = 1150000,
                Descripcion = "Reintegro a capital",
                CodigoNovedad = "10",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002",
                NoRelacion = "1"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoPorAfectacionACapitalPorAdjudicacion()
        {
            yield return new object[] {
                new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 1500000,
                Descripcion = "Giros",
                CodigoNovedad = "",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                NoRelacion = "658412",
                CodigoTransaccion = TiposTransaccion.DESEMBOLSO_INICIAL
            } };
        }

        public static IEnumerable<object[]> ObtenerMovimientoPorAfectacionACapitalPorDesembolso()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 1500000,
                Descripcion = "Giros",
                CodigoNovedad = "06",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10001",
                NoRelacion = "658412",
                CodigoTransaccion = TiposTransaccion.DESEMBOLSO_INICIAL
            }};
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 3300000,
                Descripcion = "Giros",
                CodigoNovedad = "06",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10002",
                NoRelacion = "658413",
                CodigoTransaccion = TiposTransaccion.DESEMBOLSO_INICIAL
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientosPorPasoAlCobro()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 1500000,
                Descripcion = "Paso al cobro",
                CodigoNovedad = "030",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10001"
            }};

            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 2000000,
                Descripcion = "Paso al cobro",
                CodigoNovedad = "029",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10002"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientosPorAfectacionACapitalCortoPlazo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 1500000,
                Descripcion = "Afectación a capital",
                CodigoNovedad = "12",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10001"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientosPorAfectacionACapitalLargoPlazo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 2000000,
                Descripcion = "Afectación a capital",
                CodigoNovedad = "12",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272827,
                IdSubproducto = 1,
                CodigoSubproducto = "10002"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoCapitalRecaudoAsobancaria()
        {
            yield return new object[] {
                new MovimientoDTO
                {
                    FechaMovimiento = DateTime.Now,
                    ValorGiro = 1,
                    Descripcion = "",
                    CodigoNovedad = "1",
                    Year = 2023,
                    Semestre = 1,
                    IdSolicitud = 1,
                    IdSubproducto = 1,
                    CodigoSubproducto = "10001"
                }
            };
        }

        public static IEnumerable<object[]> ObtenerMovimientoCapitalRecaudoManual()
        {
            yield return new object[] {
                new MovimientoDTO
                {
                    FechaMovimiento = DateTime.Now,
                    ValorGiro = 1,
                    Descripcion = "",
                    CodigoNovedad = "3",
                    Year = 2023,
                    Semestre = 1,
                    IdSolicitud = 1,
                    IdSubproducto = 1,
                    CodigoSubproducto = "10001"
                }
            };
        }

        public static IEnumerable<object[]> ObtenerMovimientoCortoPlazoReversionRecaudo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 400000,
                Descripcion = "Reversion de Recaudo Asobancaria",
                CodigoNovedad = "2",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoLargoPlazoReversionRecaudo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 4000000,
                Descripcion = "Reversion de Recaudo Manual",
                CodigoNovedad = "4",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientosCondonacionMuerteInvalidez()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 544892.52,
                Descripcion = "Condonación por Muerte o Invalidéz",
                CodigoNovedad = "24",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001"
            }};

            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 36718781.67,
                Descripcion = "Condonación por Muerte o Invalidéz",
                CodigoNovedad = "24",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoCortoPlazoCondonacionGraduacionSaberPro()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 2000000,
                Descripcion = "Condonación por Graduación",
                CodigoNovedad = "17",
                Year = 2023,
                Semestre = 1,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = "10001"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoLargoPlazoCondonacionGraduacionSaberPro()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 4000000,
                Descripcion = "Condonación por Graduación",
                CodigoNovedad = "17",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002"
            }};
        }

        public static IEnumerable<object[]> ObtenerMovimientoLargoPlazoCondonacionAlianza()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 500000,
                Descripcion = "Condonación por Alianza",
                CodigoNovedad = "19",
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = "10002"
            }};
        }
        
        public static IEnumerable<object[]> ObtenerMovimientoCortoYLargoPlazoNovacionViejo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 500000,
                Descripcion = "Novación Viejo",
                CodigoNovedad = TiposNovedadAfectacion.NOVACION_VIEJO,
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            }};
        }   
        
        public static IEnumerable<object[]> ObtenerMovimientoCortoPlazoNovacionNuevo()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = DateTime.Now,
                ValorGiro = 25000000,
                Descripcion = "Novación Nuevo",
                CodigoNovedad = TiposNovedadAfectacion.NOVACION_NUEVO,
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 2,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            }};                 
        }
        
        public static IEnumerable<object[]> ObtenerMovimientoCortoPlazoReversionPasoAlCobro()
        {
            yield return new object[] { new MovimientoDTO
            {
                FechaMovimiento = new DateTime(2024,05,17),
                ValorGiro = 8240000,
                Descripcion = "Reversion paso al cobro",
                CodigoNovedad = TiposNovedadAfectacion.REVERSION_PASO_AL_COBRO,
                Year = 2023,
                Semestre = 2,
                IdSolicitud = 6272872,
                IdSubproducto = 1,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            }};                 
        }
    }
}
