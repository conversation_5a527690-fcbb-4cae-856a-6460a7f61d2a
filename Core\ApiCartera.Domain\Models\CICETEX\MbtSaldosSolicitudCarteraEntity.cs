﻿
using ApiCartera.Domain.Attributes;

namespace ApiCartera.Domain.Models.CICETEX;

[EntityName("MBT_SALDOS_SOLICITUD_CARTERA")]
public class MbtSaldosSolicitudCarteraEntity : IEntity<string>
{
    public string Id
    {
        get => "";
        set { }
    }
    public DateTime CreatedDate { get; set; } = DateTime.MinValue;
    public DateTime? UpdateDate { get; set; } = null;
    public int IdSaldosSolicitudCartera { get; set; }
    public int IdTipoCartera { get; set; }
    public long IdSolicitud { get; set; }
    public long IdSignature { get; set; }
    public int IdSubproducto { get; set; }
    public double SaldoCapitalVigente { get; set; }
    public double SaldoTotalCapital { get; set; }
    public double SaldoCapitalVencido { get; set; }
    public int CuotasPendientesPagar { get; set; }
}
