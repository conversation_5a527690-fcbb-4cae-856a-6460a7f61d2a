﻿using Sarlaft.Domain.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sarlaft.Test.Domain.Exceptions
{
    public class ExceptionsTest
    {
        [Fact]
        public void BaseException()
        {
            var ex = new BaseException();

            Assert.Equal(ex.Message, "Excepción controlada");
        }

        [Fact]
        public void NotFoundException()
        {
            var ex = new NotFoundException();

            Assert.Equal(ex.Message, "No se encontraron registros");
            Assert.IsAssignableFrom(typeof(BaseException), ex);
        }

    }
}
