﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="FluentValidation" Version="11.8.1" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.8.1" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Oracle.ManagedDataAccess" Version="23.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ApiCartera.Domain\ApiCartera.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Contracts\Persistence\Afectaciones\" />
    <Folder Include="Features\Afectaciones\Queries\" />
    <Folder Include="Features\DivisionSaldos\Commands\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Resource1.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resource1.resx</DependentUpon>
    </Compile>
    <Compile Update="Validators\ValidationMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ValidationMessages.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Resource1.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Validators\ValidationMessages.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>ValidationMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
