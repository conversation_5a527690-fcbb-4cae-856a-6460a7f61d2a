﻿using ApiCartera.API.Constants;
using ApiCartera.API.Wrappers;
using ApiCartera.Application.Contracts.Storage;
using System.IO;
using System.Net;
using System.Text.Json;

namespace BancoProyectos.API.Middleware
{
    public class CustomResponseMiddleware
    {
        private readonly RequestDelegate _next;

        public CustomResponseMiddleware(RequestDelegate next)
        {
            _next = next;
        }
        public async Task InvokeAsync(HttpContext context)
        {

            using (var buffer = new MemoryStream())
            {
                var stream = context.Response.Body;

                context.Response.Body = buffer;
                await _next(context);
                
                if (context.Response.StatusCode == (int)HttpStatusCode.OK)
                {
                    buffer.Seek(0, SeekOrigin.Begin);
                    using (var bufferReader = new StreamReader(buffer))
                    {
                        string body = await bufferReader.ReadToEndAsync();
                        if (context.Response.ContentType is null ||
                            context.Response.ContentType.Contains(ApplicationTypes.JSON) ||
                            context.Response.ContentType.Contains(ApplicationTypes.Text))
                        {
                            ResponseWrapper<object> newBody;
                            object bodyObj;
                            try
                            {
                                bodyObj = JsonSerializer.Deserialize<object>(body) ?? string.Empty;
                            }
                            catch
                            {
                                bodyObj = body;
                            }
                            newBody = new(context.Response.StatusCode, bodyObj, ApiMessages.Ok200, true);

                            var jsonString = JsonSerializer.Serialize(newBody);

                            context.Response.Clear();
                            await ReWrite(context, jsonString, stream);
                        }
                        else
                        {
                            context.Response.Body.Seek(0, SeekOrigin.Begin);
                            await context.Response.Body.CopyToAsync(stream);
                        }
                        
                    }
                }
                else if((new int[] {StatusCodes.Status401Unauthorized,StatusCodes.Status403Forbidden}).Any(x=> x == context.Response.StatusCode))
                {
                    var newBody = new ResponseWrapper<object>(context.Response.StatusCode, ApiMessages.NotAuthorize400);
                    var jsonString = JsonSerializer.Serialize(newBody);
                    await ReWrite(context, jsonString, stream);
                }
                else
                {
                    context.Response.Body.Seek(0, SeekOrigin.Begin);
                    await context.Response.Body.CopyToAsync(stream);
                }

            }
        }

        private async Task ReWrite(HttpContext context,string body,Stream stream)
        {
            context.Response.ContentType = TypeResponse.Json;
            await context.Response.WriteAsync(body);
            context.Response.Body.Seek(0, SeekOrigin.Begin);
            await context.Response.Body.CopyToAsync(stream);
            context.Response.Body = stream;
        }
    }
}
