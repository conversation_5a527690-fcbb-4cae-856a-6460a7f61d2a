2024-11-02 22:34:49.508 -05:00 [INF] Worker inicia ejecución a las: "2024-11-02T22:34:49.4598246-05:00"
2024-11-02 22:34:50.844 -05:00 [ERR] Hosting failed to start
Renci.SshNet.Common.SftpPathNotFoundException: /PRE075620241101TXT does not exist
   at Renci.SshNet.Common.AsyncResult.EndInvoke()
   at Renci.SshNet.Common.AsyncResult`1.EndInvoke()
   at Renci.SshNet.Sftp.SftpSession.EndOpen(SftpOpenAsyncResult asyncResult)
   at Renci.SshNet.ServiceFactory.CreateSftpFileReader(String fileName, ISftpSession sftpSession, UInt32 bufferSize)
   at Renci.SshNet.SftpClient.InternalDownloadFile(String path, Stream output, SftpDownloadAsyncResult asyncResult, Action`1 downloadCallback)
   at Renci.SshNet.SftpClient.DownloadFile(String path, Stream output, Action`1 downloadCallback)
   at ApiCartera.Infrastructure.Registros.SFTPRepository.LeerArchivo[T](String host, String username, String password, String remoteFilePath) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Infrastructure\ApiCartera.Infrastructure\Registros\SFTPRepository.cs:line 21
   at ApiCartera.Application.Services.Registros.GestorRegistrosSaldosSolicitudCartera.ProcesarRegistrosMovimientos(SftpSettingsDTO sftpSettings, String oracleConnectionString) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\Core\ApiCartera.Application\Services\Registros\GestorRegistrosSaldosSolicitudCartera.cs:line 47
   at WorkerServiceMovimientos.Worker.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\source\repos\Icetex.NET.ModuloCartera.ApíModuloCartera\WorkerServiceMovimientos\Worker.cs:line 25
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
