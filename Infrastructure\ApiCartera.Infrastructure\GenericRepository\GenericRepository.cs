﻿using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Domain.Attributes;
using ApiCartera.Domain.Models;
using ApiCartera.Domain.ValueObjects;
using ApiCartera.Infrastructure.Data.AdoDbContext;
using ApiCartera.Infrastructure.Data.EFDbContext;
using ApiCartera.Infrastructure.Shared;
using Microsoft.EntityFrameworkCore;
using Oracle.ManagedDataAccess.Client;
using System.Data;
using System.Globalization;
using System.Linq.Expressions;
using System.Reflection;

namespace ApiCartera.Infrastructure.GenericRepository;

public class GenericRepository<T, TKey> : IRepository<T, TKey>
    where T : class, IEntity<TKey>, new()
    where TKey : notnull
{
    private readonly AppDbContext _efContext;
    private readonly DbSet<T> _dbSet;
    private readonly OracleDataContext _oracleContext;
    private readonly string _tableName = CreateInstace<T>();

    public static string CreateInstace<T>() where T : new()
    {
        T instace = new();

        Type type = instace.GetType();

        var attribute = type.GetCustomAttributes<EntityName>();

        return attribute.Select(value => value.Name).First();
    }
    public GenericRepository(AppDbContext efContext, OracleDataContext oracleContext)
    {
        _efContext = efContext;
        _oracleContext = oracleContext;
        _dbSet = _efContext.Set<T>();
    }
    public async Task CreateAsync(T entity)
    {
        entity.CreatedDate = DateTime.Now;
        entity.UpdateDate = null;
        await _dbSet.AddAsync(entity);
        await _efContext.SaveChangesAsync();
    }
    public async Task CreateAsync(T entity, DatabaseType flag)
    {        
        var properties = typeof(T).GetProperties()
            .Where(p => p.Name != "Id" && p.Name != "CreatedDate" && p.Name != "UpdateDate").ToList();

        string collumns = string.Join(", ", properties.Select(property => property.Name));
        string parameters = string.Join(", ", properties.Select(p => $":{p.Name}"));
        string insertQuery = $"INSERT INTO {_tableName} ({collumns}) VALUES ({parameters})";

        OracleCommand command = _oracleContext.CreateCommand(insertQuery);

        foreach (var prop in properties)
        {
            var value = prop.GetValue(entity);
            Console.WriteLine($"{prop.Name} = {value ?? "NULL"} (Tipo: {prop.PropertyType.Name}, Valor DB: {ConvertForDatabase(value)})");
            command.Parameters.Add($":{prop.Name}", value);
        }

        try
        {
            await command.ExecuteNonQueryAsync();
        }
        catch(Exception ex)
        {
            Console.WriteLine($"EXEPCION{ex.Message} INTERNA:{ex.InnerException}");
            throw;
        }
        finally
        {
            command.Dispose();
        }
    }

    private string ConvertForDatabase(object value)
    {
        if (value == null) return "NULL";
        if (value is DateTime) return ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss");
        if (value is string) return $"'{value}'";
        return value.ToString();
    }
    public async Task DeleteAsync(TKey entity)
    {
        var result = await FindByIdAsync(entity) ?? throw new Exception($"{nameof(T)} Not Found");
        _dbSet.Remove(result);
        await _efContext.SaveChangesAsync();
    }
    public async Task DeleteAsync(string property, TKey id)
    {
        string deleteQuery = $"DELETE FROM {_tableName} WHERE {property} = '{id}'";

        using OracleCommand command = _oracleContext.CreateCommand(deleteQuery);

        await command.ExecuteNonQueryAsync();
    }
    public async Task<Dictionary<string, object>> ExcecuteProcedureOutputParams(string package,
        Dictionary<string, object>? @params = null, Dictionary<string, OracleDbType>? outputParams = null)
    {
        Dictionary<string, object> outputResults = [];

        using OracleCommand command = _oracleContext.CreateCommand(package);

        command.CommandType = CommandType.StoredProcedure;

        if (@params != null)
            foreach (var param in @params)
                command.Parameters.Add(param.Key, param.Value);

        if (outputParams != null)
            foreach (var param in outputParams)
            {
                OracleParameter outputParam = new()
                {
                    ParameterName = param.Key,
                    OracleDbType = param.Value,
                    Direction = ParameterDirection.Output,
                    Size = 255
                };
                command.Parameters.Add(outputParam);
            }

        await command.ExecuteReaderAsync();

        if (outputParams != null)
            foreach (var param in outputParams.Keys)
                outputResults[param] = command.Parameters[param].Value;

        return outputResults;
    }
    public async Task<List<T>> ExecuteStoredProcedureWithCursorAsync<T>(
    string procedureName) where T : new()
    {
        List<T> resultSet = [];
        using OracleCommand command = _oracleContext.CreateCommand(procedureName);
        command.CommandType = CommandType.StoredProcedure;

        OracleParameter cursorParam = new()
        {
            ParameterName = "p_cursor",
            OracleDbType = OracleDbType.RefCursor,
            Direction = ParameterDirection.Output
        };
        command.Parameters.Add(cursorParam);

        using OracleDataReader reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            T entity = new();

            foreach (var prop in typeof(T).GetProperties())
            {
                object? value = reader[prop.Name] == DBNull.Value ? null : reader[prop.Name];
                prop.SetValue(entity, value);
            }
            resultSet.Add(entity);
        }
        return resultSet;
    }
    public async Task<T> ExecuteFunctionAsync<T>(string functionName, Dictionary<string, object> parameters)
    {
        using OracleCommand command = _oracleContext.CreateCommand(functionName);
        command.CommandType = CommandType.Text;
        string paramList = string.Join(", ", parameters.Keys.Select(k => ":" + k));
        command.CommandText = $"SELECT {functionName}({paramList}) FROM DUAL";

        foreach (var param in parameters)
            command.Parameters.Add(new OracleParameter(param.Key, param.Value));


        object? result = await command.ExecuteScalarAsync();
        return result == DBNull.Value ? default : (T)Convert.ChangeType(result, typeof(T));
    }
    public async Task<List<T>> ExecuteViewAsync<T>(string viewName) where T : new()
    {
        List<T> resultSet = new List<T>();

        using OracleCommand command = _oracleContext.CreateCommand($"SELECT * FROM {viewName}");
        command.CommandType = CommandType.Text;

        using OracleDataReader reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            T entity = new T();

            foreach (var prop in typeof(T).GetProperties())
            {
                if (!reader.HasColumn(prop.Name)) continue;

                object value = reader[prop.Name];
                if (value == DBNull.Value)
                {
                    prop.SetValue(entity, null);
                    continue;
                }

                Type propertyType = prop.PropertyType;

                try
                {
                    object convertedValue = ConvertValue(value, propertyType);
                    prop.SetValue(entity, convertedValue);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException(
                        $"Error al convertir valor para la propiedad {prop.Name}. " +
                        $"Valor: {value}, Tipo esperado: {propertyType}, Tipo recibido: {value.GetType()}", ex);
                }
            }

            resultSet.Add(entity);
        }

        return resultSet;
    }

    public async Task<List<T>> ExecuteReportViewPaginatedAsync<T>(string viewName, string filterName, string fechaInicio, string fechaFin, int pageSize, int pageNo) where T : new()
    {
        List<T> resultSet = new List<T>();
        string sql = string.Format(Resource1.commandViewWithPaginated, viewName, filterName).Remove(0, 2);
        sql = sql.Remove(sql.Length - 1); 
        using OracleCommand command = _oracleContext.CreateCommand(sql);

        command.CommandType = CommandType.Text;

        command.Parameters.Add(Constants.PARAMETER_FECHA_INICIO, OracleDbType.Date).Value = DateTime.ParseExact(fechaInicio, Constants.SHORT_FORMAT_DATE, CultureInfo.InvariantCulture);
        command.Parameters.Add(Constants.PARAMETER_FECHA_FIN, OracleDbType.Date).Value = DateTime.ParseExact(fechaFin, Constants.SHORT_FORMAT_DATE, CultureInfo.InvariantCulture);
        command.Parameters.Add(Constants.PARAMETER_PAGE_SIZE, OracleDbType.Int16).Value = pageSize;
        command.Parameters.Add(Constants.PARAMETER_PAGE_NO, OracleDbType.Int16).Value = pageNo;

        using OracleDataReader reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            T entity = new T();

            foreach (var prop in typeof(T).GetProperties())
            {
                if (!reader.HasColumn(prop.Name)) continue;

                object value = reader[prop.Name];
                if (value == DBNull.Value)
                {
                    prop.SetValue(entity, null);
                    continue;
                }

                Type propertyType = prop.PropertyType;

                try
                {
                    object convertedValue = ConvertValue(value, propertyType);
                    prop.SetValue(entity, convertedValue);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException(
                        $"Error al convertir valor para la propiedad {prop.Name}. " +
                        $"Valor: {value}, Tipo esperado: {propertyType}, Tipo recibido: {value.GetType()}", ex);
                }
            }

            resultSet.Add(entity);
        }

        return resultSet;
    }

    public async Task<T> FindByIdAsync(TKey id)
    {
        return await _dbSet.FindAsync(id) ?? throw new Exception($"{nameof(T)} Not Found");
    }
    public async Task<T> FindByIdAsync(string Property, TKey id)
    {
        T? entity = default;

        string selectQuery = $"SELECT * FROM {_tableName} WHERE {Property} = '{id}'";
        using OracleCommand command = _oracleContext.CreateCommand(selectQuery);
        using OracleDataReader reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
            entity = Extension<T, TKey>.MapEntity(reader);

        return entity!;
    }
    public async Task<List<T>> FindByConditionsAsync(
        IEnumerable<KeyValuePair<string, object>> equalConditions = null,
        IEnumerable<KeyValuePair<string, object>> likeConditions = null,
        IEnumerable<KeyValuePair<string, (object, object)>> betweenConditions = null,
        IEnumerable<KeyValuePair<string, IEnumerable<object>>> inConditions = null,
        string logicalOperator = "AND")
    {
        List<string>? whereClauses = new List<string>();
        List<OracleParameter>? parameters = new List<OracleParameter>();
        int paramCount = 0;
        List<T> results = new List<T>();

        if (equalConditions != null)
        {
            foreach (var condition in equalConditions)
            {
                string paramName = $":eq{paramCount++}";
                whereClauses.Add($"{condition.Key} = {paramName}");
                parameters.Add(new OracleParameter(paramName, condition.Value));
            }
        }

        if (likeConditions != null)
        {
            foreach (var condition in likeConditions)
            {
                string paramName = $":lk{paramCount++}";
                whereClauses.Add($"{condition.Key} LIKE {paramName}");
                parameters.Add(new OracleParameter(paramName, condition.Value));
            }
        }

        if (betweenConditions != null)
        {
            foreach (var condition in betweenConditions)
            {
                string paramName1 = $":bt{paramCount++}";
                string paramName2 = $":bt{paramCount++}";
                whereClauses.Add($"{condition.Key} BETWEEN {paramName1} AND {paramName2}");
                parameters.Add(new OracleParameter(paramName1, condition.Value.Item1));
                parameters.Add(new OracleParameter(paramName2, condition.Value.Item2));
            }
        }

        if (inConditions != null)
        {
            foreach (var condition in inConditions)
            {
                var inValues = condition.Value.Select((v, i) => {
                    string paramName = $":in{paramCount++}";
                    parameters.Add(new OracleParameter(paramName, v));
                    return paramName;
                }).ToList();

                whereClauses.Add($"{condition.Key} IN ({string.Join(", ", inValues)})");
            }
        }

        if (whereClauses.Count == 0)
            throw new ArgumentException("Debe proporcionar al menos una condición");

        string whereClause = string.Join($" {logicalOperator} ", whereClauses);
        string selectQuery = $"SELECT * FROM {_tableName} WHERE {whereClause}";

        using OracleCommand command = _oracleContext.CreateCommand(selectQuery);
        command.Parameters.AddRange(parameters.ToArray());
        try
        {
            using OracleDataReader reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                results.Add(Extension<T, TKey>.MapEntity(reader));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"EXEPCION{ex.Message} INTERNA:{ex.InnerException}");
            throw;
        }
        finally
        {
            command.Dispose();
        }

        return results;
    }
    public async Task<T> GetByFilter(Expression<Func<T, bool>> filter)
    {
        var entity = await _dbSet.FindAsync(filter) ?? throw new Exception($"{nameof(T)} Not Found");
        return entity;
    }
    public IEnumerable<T> GetByFilterOrdered(Expression<Func<T, bool>> predicate,
        Expression<Func<T, object>> orderBy, bool? isDesc = true)
    {
        if (isDesc == false)
            return _dbSet.Where(predicate).OrderBy(orderBy);
        else
            return _dbSet.Where(predicate).OrderByDescending(orderBy);
    }
    public async Task<List<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync() ?? throw new Exception($"{nameof(T)} List Not Found");
    }
    public async Task<List<T>> GetAllAsync(DatabaseType flag)
    {
        List<T> values = [];

        string selectQuery = $"SELECT * FROM {_tableName}";
        using OracleCommand command = _oracleContext.CreateCommand(selectQuery);
        using OracleDataReader reader = await command.ExecuteReaderAsync();
        while (reader.Read())
            values.Add(Extension<T, TKey>.MapEntity(reader));
        return values;
    }
    public async Task UpdateAsync(T entity)
    {
        entity.UpdateDate = DateTime.Now;
        _dbSet.Update(entity);
        await _efContext.SaveChangesAsync();
    }
    public async Task UpdateAsync(T entity, string pKProperty, TKey id)
    {
        entity.UpdateDate = DateTime.Now;

        string updateQuery = $"UPDATE {_tableName} SET {Extension<T, TKey>.MapSetClause(entity)} WHERE {pKProperty} = '{id}'";

        using OracleCommand command = _oracleContext.CreateCommand(updateQuery);

        await command.ExecuteNonQueryAsync();
    }    

    private object ConvertValue(object value, Type targetType)
    {
        if (value == null || value == DBNull.Value)
            return null;

        Type valueType = value.GetType();

        if (targetType.IsAssignableFrom(valueType))
            return value;
        if ((targetType == typeof(decimal) || targetType == typeof(decimal?)) && value is double)        
            return Convert.ToDecimal(value);        
        else if ((targetType == typeof(int) || targetType == typeof(int?)) && value is decimal)        
            return Convert.ToInt32(value);        
        else if ((targetType == typeof(long) || targetType == typeof(long?)) && value is decimal)        
            return Convert.ToInt64(value);
        else if ((targetType == typeof(DateOnly) || targetType == typeof(DateOnly?)) && value is DateTime)
            return DateOnly.FromDateTime((DateTime)value);

        return Convert.ChangeType(value, targetType);
    }
}