using Moq;
using Sarlaft.Application.Contracts.Persistence;
using Sarlaft.Application.Contracts.Storage;
using Sarlaft.Application.Features.Parametricas.Queries.DANE.DescargarDANE;
using Sarlaft.Domain.Features.Files.Entities;
using Sarlaft.Domain.Features.Parametricas.Entities;
using Sarlaft.Test.Domain.Features;

namespace Sarlaft.Test.Application.Features.Parametricas.Queries.DANE
{
    public class DescargarDANETest
    {
        private byte[] fileBytesTest => Enumerable.Repeat((byte)0x55, 20).ToArray();
        [Fact]
        public async Task ObtenerCorrecta()
        {
            var mockRepository = new Mock<IDANEHistoricoRepository>();
            var mockStorage = new Mock<IStorageService>();

            var handler = new DescargarDANEQueryHandler(mockRepository.Object, mockStorage.Object);

            var id = EntityExtensionTest.GenerateId();
            var query = new DescargarDANEQuery(id);
            var fileName = "test.pdf";
            var daneHistorico = new DANEHistorico
            {
                Id = id,
                NombreArchivo = fileName,
                UbicacionRelativa = $"DANE/{fileName}",
                FechaCreacion = DateTime.Now
            };

            var file = new CustomFile(fileBytesTest, fileName, ApplicationTypes.JSON, 6);
            // Configura el comportamiento esperado para el repositorio y el mapeador
            mockRepository.Setup(repo => repo.ObtenerPorIdAsync(id)).ReturnsAsync(daneHistorico);
            mockStorage.Setup(storage => storage.Get(daneHistorico.UbicacionRelativa)).Returns(file);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.Equal(file, result);
        }


    }
}