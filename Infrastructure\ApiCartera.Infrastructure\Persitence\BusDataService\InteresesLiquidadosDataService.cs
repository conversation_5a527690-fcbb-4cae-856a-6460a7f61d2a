﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Exceptions;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCartera.Domain.Features.Shared.DTOs;
using ApiCartera.Domain.Features.Shared.Entities;
using FluentValidation.Results;
using ICETEX.Librerias.BusServicios;
using Microsoft.Extensions.Configuration;

namespace ApiCartera.Infrastructure.Persitence.BusDataService
{
    public class InteresesLiquidadosDataService(BusService busService, IConfiguration configuration) :
        GenericCustomRepositoryBusDataService<InteresesLiquidados>(busService, configuration), IInteresesLiquidadosRepository
    {
        public async Task<InteresesLiquidados> Guardar(InteresesLiquidados interesesLiquidados)
        {
            var url = $"registro-interesesliquidados";
            var respuestaSaldosSolicitudCartera = 
                await busService.Services.Generico.Post<RespuestaDataServiceDTO<InteresesLiquidados>, InteresesLiquidados>(url, interesesLiquidados);

            if (respuestaSaldosSolicitudCartera.Response != TiposRespuestaDataService.EXITOSA)
            {
                var validaciones = new List<ValidationFailure>()
                {
                    new()
                    {
                        PropertyName = nameof(interesesLiquidados),
                        ErrorMessage = respuestaSaldosSolicitudCartera.Response
                    }
                };
                throw new ValidationException(validaciones);
            }
            return respuestaSaldosSolicitudCartera.Data;
        }
    }
}
