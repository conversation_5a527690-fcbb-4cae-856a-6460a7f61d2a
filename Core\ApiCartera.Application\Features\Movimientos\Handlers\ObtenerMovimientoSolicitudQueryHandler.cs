﻿using ApiCartera.Application.Features.Movimientos.Queries;
using ApiCartera.Application.Features.Movimientos.ViewModels;
using ApiCartera.Domain.Features.Movimientos.DTOs;
using ApiCartera.Domain.Features.Movimientos.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Movimientos.Handlers
{
    public class ObtenerMovimientoSolicitudQueryHandler(IMovimientoSolicitudService movimientoSolicitudService, IMapper mapper) : IRequestHandler<ObtenerMovimientosCreditosQuery, List<ConsultaMovimientoVm>>
    {
        public async Task<List<ConsultaMovimientoVm>> Handle(ObtenerMovimientosCreditosQuery request, CancellationToken cancellationToken)
        {
            var parametros = mapper.Map<ParametrosConsultaMovimientoSolicitudDTO>(request);
            var resultado = await movimientoSolicitudService.ObtenerMovimientosSolicitudPorSubproducto(parametros);
            
            return mapper.Map<List<ConsultaMovimientoVm>>(resultado);
        }
    }
}
