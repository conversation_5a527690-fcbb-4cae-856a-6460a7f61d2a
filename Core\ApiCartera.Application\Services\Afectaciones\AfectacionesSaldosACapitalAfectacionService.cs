﻿using ApiCartera.Application.Exceptions;
using ApiCartera.Application.Validators;
using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.Afectaciones.Services;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using FluentValidation.Results;
using ApiCartera.Application.Extensions;

namespace ApiCartera.Application.Services.Afectaciones
{
    public class AfectacionesSaldosACapitalAfectacionService(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepository) : AfectacionesService, ICalcularSaldosCarteraAfectacionService
    {
        public async Task<SaldosCarteraDTO> CalcularSaldosCartera(MovimientoAfectacionDTO movimiento)
        {
            
            ValidarMovimientoAfectacion(movimiento);

            var idSubproducto = movimiento.ObtenerIdSubproducto();
            var saldosCartera = await saldosCarteraRepository
                .ObtenerSaldosCarteraPorIdSolicitud(movimiento.IdIcetex, idSubproducto);


            ValidarSaldosCartera(saldosCartera, movimiento.IdIcetex);


            var nuevosSaldosCartera = (SaldosCarteraDTO)saldosCartera.Clone();


            nuevosSaldosCartera = this.DisminuirSaldosProgresivamente(nuevosSaldosCartera, movimiento.MontoMovimientoCapital);


            nuevosSaldosCartera.IdMovimiento = movimiento.IdMovimientoDiario;
            nuevosSaldosCartera.IdSaldosCarteraAnt = saldosCartera.Id;


            var nuevosSaldos = MapearSaldosCartera(nuevosSaldosCartera);
            await solicitudCarteraActivaRepository.GuardarSaldos(nuevosSaldos);

            return nuevosSaldosCartera;
        }

        private static void ValidarMovimientoAfectacion(MovimientoAfectacionDTO movimiento)
        {
            if (movimiento == null)
            {
                throw new ValidationException(
                [
                    new ValidationFailure()
                    {
                        PropertyName = nameof(movimiento.IdIcetex),
                        ErrorMessage = $"{ValidationMessages.Movimiento} {movimiento.IdIcetex}"
                    }
                ]);
            }
            var movimientoAfectacionValidator = new MovimientoAfectacionValidator();
            movimientoAfectacionValidator.ValidateAndThrowCustomValidationException(movimiento);
        }
    }
}
