﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34316.72
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{E18BB4B5-A21A-4606-9A8E-197259CC6963}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{2F409931-846C-4293-BB86-7214585DBBF1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{E8E8EAB1-4932-47E7-BF83-DF73269A4C84}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiCartera.API", "API\ApiCartera.API\ApiCartera.API.csproj", "{332ED253-3BA5-46BF-B026-ED55C04D0332}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiCartera.Domain", "Core\ApiCartera.Domain\ApiCartera.Domain.csproj", "{F0E7D119-3E00-47B1-B336-61E9F3EC7829}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiCartera.Application", "Core\ApiCartera.Application\ApiCartera.Application.csproj", "{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiCartera.Infrastructure", "Infrastructure\ApiCartera.Infrastructure\ApiCartera.Infrastructure.csproj", "{E285093F-AA8B-4869-AFA2-9E6DB57A724B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{18B75F74-9B79-425C-8C61-B29E26496C05}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ApiCarteraTests", "ApiCarteraTest\ApiCarteraTests.csproj", "{6C501261-7D33-4606-9B66-DE1472D3F98A}"
	ProjectSection(ProjectDependencies) = postProject
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829} = {F0E7D119-3E00-47B1-B336-61E9F3EC7829}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WorkerServiceSaldos", "WorkerServiceSaldos\WorkerServiceSaldos.csproj", "{DE82AB2E-F93D-4A54-9787-0BDE7BE35366}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WorkerServiceMovimientos", "WorkerServiceMovimientos\WorkerServiceMovimientos.csproj", "{D5ABA7D7-7196-4D68-A346-6438EBC57D2E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkerServiceFinalizacionPlanPagos", "WorkerServiceFinalizacionPlanPagos\WorkerServiceFinalizacionPlanPagos.csproj", "{30B4804E-37F6-4817-A741-C6A55AA17B03}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WorkerServiceFinalizacionPlanPagos", "WorkerServiceFinalizacionPlanPagos", "{EE26AC3F-6A46-48B9-892A-1B97B06FD98F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WorkerServiceMovimientos", "WorkerServiceMovimientos", "{9B80804A-38D1-4072-83C9-59BF805FBF33}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WorkerServiceSaldos", "WorkerServiceSaldos", "{47AB5936-FC3E-43B0-95BD-206A65D26CF2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{332ED253-3BA5-46BF-B026-ED55C04D0332}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{332ED253-3BA5-46BF-B026-ED55C04D0332}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{332ED253-3BA5-46BF-B026-ED55C04D0332}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{332ED253-3BA5-46BF-B026-ED55C04D0332}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19}.Release|Any CPU.Build.0 = Release|Any CPU
		{E285093F-AA8B-4869-AFA2-9E6DB57A724B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E285093F-AA8B-4869-AFA2-9E6DB57A724B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E285093F-AA8B-4869-AFA2-9E6DB57A724B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E285093F-AA8B-4869-AFA2-9E6DB57A724B}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C501261-7D33-4606-9B66-DE1472D3F98A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C501261-7D33-4606-9B66-DE1472D3F98A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C501261-7D33-4606-9B66-DE1472D3F98A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C501261-7D33-4606-9B66-DE1472D3F98A}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE82AB2E-F93D-4A54-9787-0BDE7BE35366}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE82AB2E-F93D-4A54-9787-0BDE7BE35366}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE82AB2E-F93D-4A54-9787-0BDE7BE35366}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE82AB2E-F93D-4A54-9787-0BDE7BE35366}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5ABA7D7-7196-4D68-A346-6438EBC57D2E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5ABA7D7-7196-4D68-A346-6438EBC57D2E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5ABA7D7-7196-4D68-A346-6438EBC57D2E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5ABA7D7-7196-4D68-A346-6438EBC57D2E}.Release|Any CPU.Build.0 = Release|Any CPU
		{30B4804E-37F6-4817-A741-C6A55AA17B03}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30B4804E-37F6-4817-A741-C6A55AA17B03}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30B4804E-37F6-4817-A741-C6A55AA17B03}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30B4804E-37F6-4817-A741-C6A55AA17B03}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{332ED253-3BA5-46BF-B026-ED55C04D0332} = {E18BB4B5-A21A-4606-9A8E-197259CC6963}
		{F0E7D119-3E00-47B1-B336-61E9F3EC7829} = {2F409931-846C-4293-BB86-7214585DBBF1}
		{5DAB41B7-A1BB-47FD-8E42-2CBF8AEF1C19} = {2F409931-846C-4293-BB86-7214585DBBF1}
		{E285093F-AA8B-4869-AFA2-9E6DB57A724B} = {E8E8EAB1-4932-47E7-BF83-DF73269A4C84}
		{6C501261-7D33-4606-9B66-DE1472D3F98A} = {18B75F74-9B79-425C-8C61-B29E26496C05}
		{DE82AB2E-F93D-4A54-9787-0BDE7BE35366} = {47AB5936-FC3E-43B0-95BD-206A65D26CF2}
		{D5ABA7D7-7196-4D68-A346-6438EBC57D2E} = {9B80804A-38D1-4072-83C9-59BF805FBF33}
		{30B4804E-37F6-4817-A741-C6A55AA17B03} = {EE26AC3F-6A46-48B9-892A-1B97B06FD98F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6F3A4B4F-18DE-4920-B087-B2A3B697A73F}
	EndGlobalSection
EndGlobal
