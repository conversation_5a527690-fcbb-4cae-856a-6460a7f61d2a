﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Entities;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorNovacionTests
    {
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoYLargoPlazoNovacionViejo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoDeNovacionViejo_RetornaSaldosEnCero(MovimientoDTO movimiento)
        {
            var saldosCarteraDTO = new SaldosCarteraDTO()
            {
                Id = 1,
                Saldo1 = 150420,
                Saldo2 = 0,
                Saldo3 = 2500000,
                Saldo4 = 0,
                IdSolicitud = movimiento.IdSolicitud,
                IdSubproducto = 0,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(It.IsAny<long>(), It.IsAny<int>()))
                                       .Returns(Task.FromResult(saldosCarteraDTO));
            solicitudCarteraActivaRepositoryMock.Setup(x => x.GuardarSaldos(It.IsAny<SaldosCartera>()));

            var afectacionesSaldoACapitalPorNovacionService = new AfectacionesSaldoACapitalPorNovacionService(
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await afectacionesSaldoACapitalPorNovacionService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(movimiento.CodigoSubproducto, resultado.CodigoSubproducto);
            Assert.Equal(0, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(0, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
        }       
        
        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoCortoPlazoNovacionNuevo), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoDeNovacionNuevo_RetornaSaldo1IgualValorGiradoEnMovimiento(MovimientoDTO movimiento)
        {
            var saldosCarteraDTO = new SaldosCarteraDTO()
            {
                Id = 1,
                Saldo1 = 150420,
                Saldo2 = 0,
                Saldo3 = 2500000,
                Saldo4 = 0,
                IdSolicitud = movimiento.IdSolicitud,
                IdSubproducto = 0,
                CodigoSubproducto = TiposSubproducto.CORTO_PLAZO
            };

            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();

            saldosCarteraRepositoryMock.Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(It.IsAny<long>(), It.IsAny<int>()))
                                       .Returns(Task.FromResult(saldosCarteraDTO));
            solicitudCarteraActivaRepositoryMock.Setup(x => x.GuardarSaldos(It.IsAny<SaldosCartera>()));

            var afectacionesSaldoACapitalPorNovacionService = new AfectacionesSaldoACapitalPorNovacionService(
                                                                        saldosCarteraRepositoryMock.Object,
                                                                        solicitudCarteraActivaRepositoryMock.Object);

            var resultado = await afectacionesSaldoACapitalPorNovacionService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(movimiento.CodigoSubproducto, resultado.CodigoSubproducto);
            Assert.Equal(movimiento.ValorGiro, resultado.Saldo1);
            Assert.Equal(0, resultado.Saldo2);
            Assert.Equal(0, resultado.Saldo3);
            Assert.Equal(0, resultado.Saldo4);
        }
    }
}
