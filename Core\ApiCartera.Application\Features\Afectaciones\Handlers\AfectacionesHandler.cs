﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Features.Afectaciones.Commands;
using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Domain.Features.Afectaciones.Services;
using AutoMapper;
using MediatR;

namespace ApiCartera.Application.Features.Afectaciones.Handlers
{
    public class AfectacionesHandler(
        ISaldosCarteraRepository saldosCarteraRepository,
        ISolicitudCarteraActivaRepository solicitudCarteraActivaRepositor,
        IMapper _mapper) : IRequestHandler<AfectacionesCommand, SaldoCarteraVm>
    {
        private readonly CalcularSaldosCarteraFactory _calcularSaldosCarteraFactory = new(saldosCarteraRepository, solicitudCarteraActivaRepositor);

        public async Task<SaldoCarteraVm> Handle(AfectacionesCommand request, CancellationToken cancellationToken)
        {
            ICalcularSaldosCarteraService calcularSaldosCartera = _calcularSaldosCarteraFactory.ObtenerCalculadoraSaldos(request.Movimiento.CodigoNovedad);
            var resultado = await calcularSaldosCartera.CalcularSaldosCartera(request.Movimiento);
            return _mapper.Map<SaldoCarteraVm>(resultado);
        }
    }
}
