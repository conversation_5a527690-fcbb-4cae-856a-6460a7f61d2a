﻿using ApiCartera.Application.Features.DivisionSaldos.ViewModels;
using ApiCartera.Domain.Features.Shared.DTOs;
using AutoMapper;

namespace ApiCartera.Application.Features.BeneficioAcuerdo001.Mappings
{

    public class BeneficioAcuerdo001Mapping : Profile
    {
        public BeneficioAcuerdo001Mapping()
        {
            CreateMap<InteresesLiquidadosDTO, InteresesLiquidadosVM>();
        }
    }

}
