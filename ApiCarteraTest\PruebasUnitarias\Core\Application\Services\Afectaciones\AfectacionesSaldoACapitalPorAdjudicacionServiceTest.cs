﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Application.Services.Afectaciones;
using ApiCartera.Application.Services.DivisionSaldos;
using ApiCartera.Domain.Features.Afectaciones.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Constants;
using ApiCartera.Domain.Features.DivisionSaldos.DTOs;
using ApiCartera.Domain.Features.DivisionSaldos.Services;
using ApiCartera.Domain.Features.Shared.Constants;
using ApiCarteraTests.PruebasUnitarias.Config;
using Moq;

namespace ApiCarteraTests.PruebasUnitarias.Core.Application.Services.Afectaciones
{
    public class AfectacionesSaldoACapitalPorAdjudicacionServiceTest
    {
        private readonly CalculadoraSaldos _calculadoraSaldosMock;

        [Theory]
        [MemberData(nameof(TestData.ObtenerMovimientoPorAfectacionACapitalPorAdjudicacion), MemberType = typeof(TestData))]
        public async Task CalcularSaldosCartera_RecibeMovimientoAdjudicacionSostenimiento_RetornaSaldos1Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            var calculadoraSaldosServiceMock = new Mock<ICalculadoraSaldosService>();

            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "AIES - APORTES IES",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = TiposModalidadRubro.SOSTENIMIENTO,
                Periodicidad = 6
            };

            var solicitud = new SolicitudDTO()
            {
                Id = 6272872,
                IdSolicitante = 999999,
                IdTipoLinea = 2,
                IdTipoSublinea = 11,
                TipoLinea = "",
                TipoSublinea = TiposSublinea.EXTERIOR,
            };

            var saldosCarteraDTO = new List<SaldosCarteraDTO>();

            saldosCarteraRepositoryMock
               .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                   It.IsAny<long>(),
                   1));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                 It.IsAny<long>(),
                 It.IsAny<int>()))
             .Returns(Task.FromResult(desembolso));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerSolicitudPorId(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(solicitud));
            calculadoraSaldosServiceMock
                .Setup(x => x.ObtenerSaldos(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(saldosCarteraDTO));

            var afectacionesSaldoACapitalPorAdjudicacionService = new AfectacionesSaldoACapitalPorAdjudicacionService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object,
                    calculadoraSaldosServiceMock.Object);

            var resultado = await afectacionesSaldoACapitalPorAdjudicacionService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = movimiento.SaldoCapitalVigente;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado[0].Saldo1);
            Assert.Equal(0, resultado[0].Saldo2);
            Assert.Equal(0, resultado[0].Saldo3);
            Assert.Equal(0, resultado[0].Saldo4);
        }

        public async Task CalcularSaldosCartera_RecibeMovimientoAdjudicacionGiroExterior_RetornaSaldos1Aumentado(MovimientoDTO movimiento)
        {
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            var calculadoraSaldosServiceMock = new Mock<ICalculadoraSaldosService>();

            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "AIES - APORTES IES",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "MATRICULA"
            };
            var solicitud = new SolicitudDTO()
            {
                Id = 6272872,
                IdSolicitante = 999999,
                IdTipoLinea = 2,
                IdTipoSublinea = 11,
                TipoLinea = "",
                TipoSublinea = TiposSublinea.EXTERIOR,
            };

            var saldosCarteraDTO = new List<SaldosCarteraDTO>();

            saldosCarteraRepositoryMock
               .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                   It.IsAny<long>(),
                   1));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                 It.IsAny<long>(),
                 It.IsAny<int>()))
             .Returns(Task.FromResult(desembolso));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerSolicitudPorId(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(solicitud));
            calculadoraSaldosServiceMock
                .Setup(x => x.ObtenerSaldos(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(saldosCarteraDTO));

            var afectacionesSaldoACapitalPorAdjudicacionService = new AfectacionesSaldoACapitalPorAdjudicacionService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object,
                    calculadoraSaldosServiceMock.Object);

            var resultado = await afectacionesSaldoACapitalPorAdjudicacionService.CalcularSaldosCartera(movimiento);
            var saldo1Actualizado = movimiento.SaldoCapitalVigente;

            Assert.NotNull(resultado);
            Assert.Equal(saldo1Actualizado, resultado[0].Saldo1);
            Assert.Equal(0, resultado[0].Saldo2);
            Assert.Equal(0, resultado[0].Saldo3);
            Assert.Equal(0, resultado[0].Saldo4);
        }

        public async Task CalcularSaldosCartera_RecibeMovimientoAdjudicacion_RetornaSaldos(MovimientoDTO movimiento)
        {
            var saldosCarteraRepositoryMock = new Mock<ISaldosCarteraRepository>();
            var solicitudCarteraActivaRepositoryMock = new Mock<ISolicitudCarteraActivaRepository>();
            var calculadoraSaldosServiceMock = new Mock<ICalculadoraSaldosService>();

            var desembolso = new DesembolsoDTO()
            {
                IdSolicitud = 6272872,
                YearGiro = 2024,
                SemestreGiro = 1,
                Fecha = DateTime.Parse("2024-02-02"),
                TotalGirar = 24000000,
                Marca = "AIES - APORTES IES",
                ValorAportesIES = 2500000,
                NoRelacion = 35000,
                ModalidadRubro = "MATRICULA"
            };
            var solicitud = new SolicitudDTO()
            {
                Id = 6272872,
                IdSolicitante = 999999,
                IdTipoLinea = 2,
                IdTipoSublinea = 11,
                TipoLinea = "",
                TipoSublinea = TiposSublinea.EXTERIOR,
            };

            var saldosCarteraDTO = new List<SaldosCarteraDTO>()
            {
                new SaldosCarteraDTO()
                {
                    Id = 1,
                    Saldo1 = movimiento.ValorGiro,
                    Saldo2 = 0,
                    Saldo3 = 0,
                    Saldo4 = 0,
                    IdSolicitud = movimiento.IdSolicitud,
                    IdSubproducto = movimiento.IdSubproducto,
                    CodigoSubproducto = movimiento.CodigoSubproducto
                },
                new SaldosCarteraDTO()
                {
                    Id = 1,
                    Saldo1 = 0,
                    Saldo2 = 0,
                    Saldo3 = 0,
                    Saldo4 = 0,
                    IdSolicitud = movimiento.IdSolicitud,
                    IdSubproducto = 0,
                    CodigoSubproducto = ""
                }
            };

            saldosCarteraRepositoryMock
               .Setup(x => x.ObtenerSaldosCarteraPorIdSolicitud(
                   It.IsAny<long>(),
                   1));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerDesembolsoPorNoRelacion(
                 It.IsAny<long>(),
                 It.IsAny<int>()))
             .Returns(Task.FromResult(desembolso));
            solicitudCarteraActivaRepositoryMock
             .Setup(x => x.ObtenerSolicitudPorId(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(solicitud));
            calculadoraSaldosServiceMock
                .Setup(x => x.ObtenerSaldos(
                 It.IsAny<long>()))
             .Returns(Task.FromResult(saldosCarteraDTO));

            var afectacionesSaldoACapitalPorAdjudicacionService = new AfectacionesSaldoACapitalPorAdjudicacionService(
                    saldosCarteraRepositoryMock.Object,
                    solicitudCarteraActivaRepositoryMock.Object,
                    calculadoraSaldosServiceMock.Object);

            var resultado = await afectacionesSaldoACapitalPorAdjudicacionService.CalcularSaldosCartera(movimiento);

            Assert.NotNull(resultado);
            Assert.Equal(saldosCarteraDTO[0].Saldo1, resultado[0].Saldo1);
            Assert.Equal(saldosCarteraDTO[0].Saldo2, resultado[0].Saldo2);
            Assert.Equal(saldosCarteraDTO[0].Saldo3, resultado[0].Saldo3);
            Assert.Equal(saldosCarteraDTO[0].Saldo4, resultado[0].Saldo4);
        }

    }
}
