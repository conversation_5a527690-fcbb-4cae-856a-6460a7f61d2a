﻿using ApiCartera.Application.Contracts.Server;

namespace ApiCartera.API.Services
{
    public class ServerApiInfoService : IWebServerInfo
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public ServerApiInfoService(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        public string GetRootPath()
        {
            return _webHostEnvironment.ContentRootPath;
        }
    }
}
