﻿using ApiCartera.Infrastructure.Data.EFDbContext;
using Microsoft.EntityFrameworkCore;

namespace ApiCartera.API.Config.ORM;

public static class SqlConnection
{
    public static IServiceCollection SqlConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<AppDbContext>(options => options.UseSqlServer(configuration.GetConnectionString(nameof(Oracle))));
        return services;
    }
}