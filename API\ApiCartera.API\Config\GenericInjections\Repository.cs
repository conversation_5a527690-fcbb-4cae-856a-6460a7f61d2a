﻿using ApiCartera.Application.Behaviours;
using ApiCartera.Application.Interfaces.Repositories;
using ApiCartera.Application.Interfaces;
using ApiCartera.Infrastructure.Data.AdoDbContext;
using ApiCartera.Infrastructure.GenericRepository;
using MediatR;

namespace ApiCartera.API.Config.GenericInjections;

public static class Repository
{
    public static IServiceCollection AddRepositoryDependency(this IServiceCollection services)
    {
        services.AddScoped(typeof(IPipelineBehavior<,>), typeof(TransactionBehavior<,>));
        services.AddScoped(typeof(IRepository<,>), typeof(GenericRepository<,>));
        services.AddScoped(typeof(ITransactionScope), typeof(OracleDataContext));

        return services;
    }
}
