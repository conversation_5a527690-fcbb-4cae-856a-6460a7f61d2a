﻿using ApiCartera.Application.Contracts.Persistence;
using ApiCartera.Infrastructure.Persitence.BusDataService;
using ApiCartera.Infrastructure.Persitence.BusDataService.DivisionSaldos;
using ApiCartera.Infrastructure.Persitence.DataService;
using ApiCartera.Infrastructure.Persitence.Memory;
using Microsoft.Extensions.DependencyInjection;
using NuevosCreditosClass = ApiCartera.Infrastructure.Persitence.NuevosCreditosDataService;

namespace ApiCartera.Infrastructure.Config;

public static class ConfigPersistence
{
    public static IServiceCollection RegisterPersistence(this IServiceCollection services)
    {
        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepositoryMemory<>));
        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericCustomRepositoryBusDataService<>));
        services.AddScoped<INuevoscreditosRepository, NuevosCreditosClass>();
        services.AddScoped<IUsuarioPermitidoRepository, UsuarioPermitidoDataService>();
        services.AddScoped<ISolicitudCarteraActivaRepository, SolicitudCarteraActivaDataService>();
        services.AddScoped<ISaldosCarteraRepository, SaldosCarteraDataService>();
        services.AddScoped<IDesembolsoRepository, DesembolsoDataService>();
        services.AddScoped<IActualizarControlDivisionSaldos, ActualizarControlSaldosDataService>();
        services.AddScoped<ITasaCarteraRepository, TasaCarteraDataService>();
        services.AddScoped<IMovimientoRepository, MovimientoDataService>();
        services.AddScoped<IInteresesLiquidadosRepository, InteresesLiquidadosDataService>();
        services.AddScoped<IMovimientoRepository, MovimientoDataService>();
        services.AddScoped<IReporteAportesIESUniversidadesRepository, ReporteAportesIESUniversidadesDataService>();
        services.AddScoped<IReporteBeneficioAcuerdo001Repository, ReporteBeneficioAcuerdo001DataService>();
        services.AddScoped<IReporteContribucionesAportesIESRepository, ReporteContribucionesAportesIESDataService>();
        services.AddScoped<IReporteValidacionDatosSolicitudRepository, ReporteValidacionDatosSolicitudDataService>();
        return services;
    }
}
